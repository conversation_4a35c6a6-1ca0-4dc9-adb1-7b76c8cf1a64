import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import { DeepseekClipboardService } from "./DeepseekClipboardService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Deepseek 答案捕获服务
 * 继承 BaseAnswerService，提供 Deepseek 平台特定的答案处理逻辑
 * 大部分逻辑已在基类实现，此处只需提供剪贴板服务实例
 * 如有特殊的答案处理逻辑，可以覆盖基类方法
 */
export class DeepseekAnswerService extends BaseAnswerService {
    // Deepseek 平台的剪贴板服务实例
    private clipboardService: DeepseekClipboardService;

    constructor() {
        super();
        this.clipboardService = new DeepseekClipboardService();
    }

    /**
     * 获取剪贴板服务实例
     */
    protected getClipboardService(): BaseClipboardService {
        return this.clipboardService;
    }

}
