<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Membership Plans - Inclusive Design</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* ========================================
       CSS Design Tokens - 平权设计版本
       基于 UIAgentRules.md 最新规范
       ======================================== */
    :root {
      /* 主色系（统一使用） */
      --primary: #3B82F6;
      --primary-dark: #2563EB;
      --primary-light: #60A5FA;
      --primary-50: #EFF6FF;
      --primary-100: #DBEAFE;
      
      /* 辅助色（已解锁标识） */
      --purple: #8B5CF6;
      --purple-dark: #7C3AED;
      --purple-50: #F5F3FF;
      --purple-100: #EDE9FE;
      
      /* 中性色 */
      --text-primary: #111827;
      --text-secondary: #374151;
      --text-tertiary: #6B7280;
      
      --bg-base: #FAFBFC;
      --bg-elevated: #FFFFFF;
      --bg-subtle: #F9FAFB;
      
      --border-light: #E5E7EB;
      --border-medium: #D1D5DB;
      
      /* 间距（8pt Grid） */
      --space-2: 8px;
      --space-3: 12px;
      --space-4: 16px;
      --space-6: 24px;
      --space-8: 32px;
      --space-12: 48px;
      
      /* 字体 */
      --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      --font-xs: 12px;
      --font-sm: 13px;
      --font-base: 14px;
      --font-md: 16px;
      --font-lg: 20px;
      --font-xl: 24px;
      --font-2xl: 32px;
      --font-3xl: 48px;
      
      --font-regular: 400;
      --font-medium: 500;
      --font-semibold: 600;
      --font-bold: 700;
      
      --tracking-tight: -0.03em;
      --tracking-wide: 0.5px;
      
      --leading-tight: 1.25;
      --leading-normal: 1.5;
      
      /* 圆角 */
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      
      /* 动效 */
      --duration-fast: 150ms;
      --duration-normal: 200ms;
      --easing: cubic-bezier(0.4, 0, 0.2, 1);
      --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
      
      /* 阴影 */
      --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
      --shadow-hover-blue: 
        0 0 0 1px var(--primary),
        0 12px 24px -8px rgba(59, 130, 246, 0.15),
        0 24px 48px -8px rgba(0, 0, 0, 0.08);
    }
    
    /* ========================================
       基础样式
       ======================================== */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: var(--font-family);
      background: var(--bg-base);
      color: var(--text-primary);
      line-height: var(--leading-normal);
      padding: var(--space-12) var(--space-6);
      min-height: 100vh;
    }
    
    /* ========================================
       容器布局
       ======================================== */
    .container {
      max-width: 1400px;
      margin: 0 auto;
    }
    
    .page-header {
      text-align: center;
      margin-bottom: var(--space-12);
    }
    
    .page-title {
      font-size: var(--font-2xl);
      font-weight: var(--font-bold);
      letter-spacing: var(--tracking-tight);
      margin-bottom: var(--space-3);
      color: var(--text-primary);
    }
    
    .page-subtitle {
      font-size: var(--font-md);
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
    }
    
    /* ========================================
       定价卡片网格 - 响应式
       ======================================== */
    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--space-4);
    }
    
    @media (max-width: 1399px) {
      .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
      }
    }
    
    @media (max-width: 767px) {
      .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
      }
      
      body {
        padding: var(--space-6) var(--space-4);
      }
    }
    
    /* ========================================
       定价卡片 - 统一设计（平权）
       ======================================== */
    .pricing-card {
      background: var(--bg-elevated);
      border: 1.5px solid var(--border-light);
      border-radius: var(--radius-lg);
      padding: var(--space-8);
      position: relative;
      transition: all var(--duration-normal) var(--easing);
      cursor: pointer;
    }
    
    /* Linear 特色：3px 顶部蓝色边框（统一） */
    .pricing-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      border-radius: var(--radius-lg) var(--radius-lg) 0 0;
      background: linear-gradient(90deg, var(--primary), var(--primary-dark));
      opacity: 0;
      transition: opacity var(--duration-normal) var(--easing);
    }
    
    /* 悬停效果 - 统一蓝色 */
    .pricing-card:hover::before {
      opacity: 1;
    }
    
    .pricing-card:hover {
      transform: translateY(-4px);
      border-color: var(--primary);
      box-shadow: var(--shadow-hover-blue);
    }
    
    /* 点击微缩放 */
    .pricing-card:active {
      transform: translateY(-2px) scale(0.99);
    }
    
    /* 选中状态 - 蓝色 */
    .pricing-card.selected {
      border-color: var(--primary);
      box-shadow: var(--shadow-hover-blue);
    }
    
    .pricing-card.selected::before {
      opacity: 1;
    }
    
    /* Popular 标签（仅显示推荐，不用颜色） */
    .pricing-card.popular::after {
      content: 'POPULAR';
      position: absolute;
      top: var(--space-3);
      right: var(--space-3);
      padding: 4px 10px;
      border-radius: var(--radius-sm);
      font-size: 10px;
      font-weight: var(--font-bold);
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: white;
      letter-spacing: var(--tracking-wide);
    }
    
    /* ========================================
       卡片头部
       ======================================== */
    .card-header {
      margin-bottom: var(--space-6);
    }
    
    .card-tier {
      font-size: var(--font-lg);
      font-weight: var(--font-bold);
      color: var(--text-primary);
      margin-bottom: var(--space-2);
    }
    
    .card-description {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
      margin-bottom: var(--space-4);
    }
    
    /* Badge - 已解锁标识（紫色） */
    .card-badge {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: var(--radius-sm);
      font-size: var(--font-xs);
      font-weight: var(--font-semibold);
      text-transform: uppercase;
      letter-spacing: var(--tracking-wide);
      border: 1px solid var(--purple);
      background: linear-gradient(135deg, var(--purple-50), var(--purple-100));
      color: var(--purple-dark);
    }
    
    .badge-icon {
      width: 12px;
      height: 12px;
    }
    
    /* ========================================
       价格
       ======================================== */
    .card-price {
      margin-bottom: var(--space-6);
      padding-bottom: var(--space-6);
      border-bottom: 1px solid var(--border-light);
    }
    
    .price-value {
      font-size: var(--font-3xl);
      font-weight: var(--font-bold);
      letter-spacing: var(--tracking-tight);
      color: var(--primary);
      line-height: 1;
    }
    
    .price-period {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
    }
    
    /* ========================================
       功能列表
       ======================================== */
    .card-features {
      list-style: none;
      margin-bottom: var(--space-8);
    }
    
    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: var(--space-3);
      padding: var(--space-3) 0;
      font-size: var(--font-sm);
      color: var(--text-secondary);
      border-bottom: 1px solid var(--border-light);
    }
    
    .feature-item:last-child {
      border-bottom: none;
    }
    
    .feature-icon {
      width: 18px;
      height: 18px;
      border-radius: 4px;
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      color: white;
      flex-shrink: 0;
      margin-top: 2px;
      transition: transform var(--duration-fast) var(--easing-spring);
    }
    
    /* 图标微交互 */
    .pricing-card:hover .feature-icon {
      transform: scale(1.05);
    }
    
    /* ========================================
       按钮 - 统一蓝色
       ======================================== */
    .card-cta {
      width: 100%;
      padding: 12px 20px;
      border-radius: var(--radius-md);
      font-size: var(--font-base);
      font-weight: var(--font-semibold);
      border: none;
      cursor: pointer;
      transition: all var(--duration-fast) var(--easing-spring);
    }
    
    /* Primary 按钮（未解锁） */
    .btn-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    .btn-primary:active {
      transform: translateY(0) scale(0.98);
    }
    
    /* Secondary 按钮（已解锁/当前） */
    .btn-secondary {
      background: var(--bg-elevated);
      color: var(--text-secondary);
      border: 1.5px solid var(--border-light);
    }
    
    .btn-secondary:hover {
      background: var(--bg-subtle);
      border-color: var(--border-medium);
      transform: translateY(-1px);
    }
    
    .btn-secondary:active {
      transform: translateY(0) scale(0.98);
    }
    
    /* ========================================
       可访问性
       ======================================== */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        transition-duration: 0.01ms !important;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">Choose Your Plan</h1>
      <p class="page-subtitle">
        All plans include our core features. Upgrade to unlock more capabilities.
      </p>
    </div>
    
    <!-- 定价卡片网格 -->
    <div class="pricing-grid">
      
      <!-- Free Plan -->
      <div class="pricing-card">
        <div class="card-header">
          <h3 class="card-tier">Free</h3>
          <p class="card-description">Essential features to get started</p>
        </div>
        
        <div class="card-price">
          <div class="price-value">$0</div>
          <span class="price-period">/month</span>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Basic chat access</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>5 questions per day</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Standard response time</span>
          </li>
        </ul>
        
        <button class="card-cta btn-secondary">Current Plan</button>
      </div>
      
      <!-- Pro Plan -->
      <div class="pricing-card popular selected">
        <div class="card-header">
          <h3 class="card-tier">Pro</h3>
          <p class="card-description">Perfect for professionals</p>
        </div>
        
        <div class="card-price">
          <div class="price-value">$9</div>
          <span class="price-period">/month</span>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Unlimited questions</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Priority response time</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Advanced AI models</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Export conversations</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Email support</span>
          </li>
        </ul>
        
        <button class="card-cta btn-primary">Upgrade Now</button>
      </div>
      
      <!-- Plus Plan -->
      <div class="pricing-card">
        <div class="card-header">
          <h3 class="card-tier">Plus</h3>
          <p class="card-description">Advanced features for teams</p>
          <span class="card-badge">
            <svg class="badge-icon" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
            </svg>
            Unlocked
          </span>
        </div>
        
        <div class="card-price">
          <div class="price-value">$19</div>
          <span class="price-period">/month</span>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Everything in Pro</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Team collaboration (5 seats)</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Custom AI training</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>API access</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Advanced analytics</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Priority support</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Integration options</span>
          </li>
        </ul>
        
        <button class="card-cta btn-secondary">Switch to Plus</button>
      </div>
      
      <!-- Max Plan -->
      <div class="pricing-card">
        <div class="card-header">
          <h3 class="card-tier">Max</h3>
          <p class="card-description">Enterprise-grade capabilities</p>
        </div>
        
        <div class="card-price">
          <div class="price-value">$49</div>
          <span class="price-period">/month</span>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Everything in Plus</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Unlimited team seats</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Dedicated AI instance</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Custom integrations</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>White-label options</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>24/7 dedicated support</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>SLA guarantee</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Security compliance</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Onboarding assistance</span>
          </li>
        </ul>
        
        <button class="card-cta btn-primary">Upgrade to Max</button>
      </div>
      
    </div>
  </div>
</body>
</html>
