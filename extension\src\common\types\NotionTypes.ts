/**
 * Notion API 相关类型定义
 */

/**
 * Notion Block 基础类型
 */
export interface NotionBlock {
    object: 'block';
    type: string;
    [key: string]: any;
}

/**
 * 创建页面请求参数
 */
export interface CreatePageRequest {
    parent: {
        type: 'page_id';
        page_id: string;
    };
    properties: {
        title: {
            title: Array<{
                text: {
                    content: string;
                };
            }>;
        };
        [key: string]: any;
    };
    icon?: {
        type: 'emoji';
        emoji: string;
    };
    children?: NotionBlock[];
}

/**
 * Notion API 响应
 */
export interface NotionApiResponse {
    object: string;
    id: string;
    [key: string]: any;
}

/**
 * Notion 导出参数
 */
export interface NotionExportParams {
    answerIndex: number;
    title: string;
    tags: string[];
    icon?: string;
}

/**
 * Notion 配置验证结果
 */
export interface NotionConfigValidation {
    valid: boolean;
    error?: string;
    pageTitle?: string;
}
