import { BaseAIAdapter } from '@/content/core/BaseAIAdapter'
import { ClaudeConfig } from '../types/Consts'
import { PlatformEntity } from '@/common/types/database_entity'

export class ClaudeAdapter extends BaseAIAdapter {

  // /**
  //  * 获取 Claude 平台特定的选择器配置
  //  */
  // getSelectors(): SelectorConfig {
  //   return {
  //     inputField: [
  //       'div[contenteditable="true"]'
  //     ],
  //     sendButton: [
  //       'button[aria-label*="Send"]',
  //       'button[type="submit"]'
  //     ]
  //   }
  // }

}
