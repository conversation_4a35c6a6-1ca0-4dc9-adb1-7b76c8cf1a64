import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { MonicaAnswerController } from "./MonicaAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * Monica 平台适配器
 * 继承 BaseAIAdapter，提供 Monica 平台的完整集成
 * 
 * 主要特性：
 * - 自动检测 Monica 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 直接使用 HTML 转 Markdown，不依赖复制按钮
 * - 支持多模型切换（Monica、GPT-5、Claude 等）
 * 
 * URL 模式：
 * - Home页: https://monica.im/home/<USER>/{provider}/{model}
 * - Chat页: https://monica.im/home/<USER>/{provider}/{model}?convId={chatId}
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new MonicaAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class MonicaAdapter extends BaseAIAdapter {
    private monicaAnswerController: MonicaAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【MonicaAdapter】MonicaAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.monicaAnswerController = new MonicaAnswerController();
        this.monicaAnswerController.init(this);
        
        console.log('【MonicaAdapter】MonicaAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【MonicaAdapter】MonicaAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 MonicaAnswerController
            if (this.monicaAnswerController) {
                this.monicaAnswerController.destroy();
                this.monicaAnswerController = null;
                console.log('【MonicaAdapter】MonicaAnswerController 清理完成');
            }
            
            console.log('【MonicaAdapter】MonicaAdapter destroy 完成');
            
        } catch (error) {
            console.error('【MonicaAdapter】MonicaAdapter destroy 失败:', error);
        }
    }
}
