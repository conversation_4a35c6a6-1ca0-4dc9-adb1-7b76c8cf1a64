import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi 剪贴板管理服务
 * 继承 BaseClipboardService，提供 Kimi 平台特定的剪贴板操作
 * 
 * 主要特性：
 * - 使用父类的通用点击+剪贴板流程
 * - 自定义 Turndown 规则处理暗色主题代码块
 */
export class KimiClipboardService extends BaseClipboardService {
    
    /**
     * 降级方法：从 DOM 提取 markdown 内容（Kimi 特定实现）
     * 重写父类方法，添加对暗色主题代码块的支持
     * @param answerElement 答案元素
     * @returns Markdown 格式的内容
     */
    protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
        try {
            console.info('[KimiClipboardService] 使用降级方法 - 从DOM提取markdown内容（兼容暗色主题代码块）');
            
            // 1. 获取 markdown 选择器
            const markdownSelectors = SelectorManager.getSelector()?.markdown;
            if (!markdownSelectors || markdownSelectors.length === 0) {
                console.warn('[KimiClipboardService] 未找到markdown选择器配置');
                return '';
            }

            // 2. 查找 markdown 节点
            const markdownNode = DOMUtils.findElementInContainer(answerElement, markdownSelectors);
            if (!markdownNode) {
                console.warn('[KimiClipboardService] 未找到markdown节点');
                return '';
            }

            // 3. 获取 HTML 内容
            const htmlContent = markdownNode.innerHTML;
            if (!htmlContent || !htmlContent.trim()) {
                console.warn('[KimiClipboardService] markdown节点HTML内容为空');
                return '';
            }

            // 4. 配置 Turndown 服务，添加自定义规则处理 Kimi 暗色主题代码块
            const turndownService = new TurndownService({
                headingStyle: 'atx',
                codeBlockStyle: 'fenced',
                emDelimiter: '*'
            });
            
            // 自定义规则：处理 Kimi 暗色主题的代码块结构
            // .segment-code-content > div[class*="language-"] > code
            turndownService.addRule('kimiCodeBlock', {
                filter: function (node, options) {
                    // 匹配 .segment-code-content 容器
                    if (node.nodeName === 'DIV' && 
                        node.classList && 
                        node.classList.contains('segment-code-content')) {
                        return true;
                    }
                    return false;
                },
                replacement: function (content, node, options) {
                    const element = node as Element;
                    
                    // 1. 提取语言信息（从多个可能的位置）
                    let lang = '';
                    
                    // 方法1：从内层 div[class*="language-"] 提取
                    const langDiv = element.querySelector('div[class*="language-"]');
                    if (langDiv) {
                        const classList = langDiv.className || '';
                        const langMatch = classList.match(/language-([\w\d-]+)/);
                        if (langMatch) {
                            lang = langMatch[1];
                        }
                    }
                    
                    // 方法2：从 code 标签的 class 提取
                    if (!lang) {
                        const codeEl = element.querySelector('code[class*="language-"]');
                        if (codeEl) {
                            const classList = codeEl.className || '';
                            const langMatch = classList.match(/language-([\w\d-]+)/);
                            if (langMatch) {
                                lang = langMatch[1];
                            }
                        }
                    }
                    
                    // 方法3：从父容器的 data-language 属性提取
                    if (!lang && element instanceof HTMLElement && element.dataset.language) {
                        lang = element.dataset.language;
                    }
                    
                    // 2. 获取 code 标签内容（纯代码，不包括标题和按钮）
                    const codeEl = element.querySelector('code');
                    let codeText = '';
                    if (codeEl) {
                        codeText = codeEl.textContent || '';
                    }
                    
                    // 3. 清理代码内容
                    // 去除首尾空行
                    codeText = codeText.replace(/^[\n\r]+|[\n\r]+$/g, '');
                    
                    // 如果代码为空，尝试从整个容器提取（但排除语言标签和复制按钮）
                    if (!codeText && element.textContent) {
                        codeText = element.textContent;
                        // 移除可能的语言标签文本（如 "JavaScript"）
                        if (lang) {
                            // 移除开头的语言名称（大小写不敏感）
                            const langRegex = new RegExp(`^${lang}\\s*`, 'i');
                            codeText = codeText.replace(langRegex, '');
                        }
                        // 移除 "复制" 文本
                        codeText = codeText.replace(/复制\s*/g, '');
                        // 移除开头的空白
                        codeText = codeText.replace(/^[\n\r\s]+/, '');
                    }
                    
                    console.info(`[KimiClipboardService] 提取代码块 - 语言: ${lang || '未指定'}, 长度: ${codeText.length}`);
                    
                    // 4. 返回 markdown 代码块格式（确保语言标识紧跟在 ``` 后面）
                    return '\n\n```' + (lang || '') + '\n' + codeText + '\n```\n\n';
                }
            });
            
            const markdownContent = turndownService.turndown(htmlContent);
            console.info('[KimiClipboardService] 成功提取并转换markdown内容', markdownContent.slice(0, 100));
            
            return markdownContent || '';
            
        } catch (error) {
            console.error('[KimiClipboardService] 降级方法也失败', error);
            return '';
        }
    }
}
