import { SelectorConfig } from "./SelectorManager";

/**
 * Poe 适配器的选择器配置
 * 整合了所有 Poe 平台特有的选择器
 * 
 * @特殊说明
 * - Poe使用问答对(message tuple)结构，不同于其他平台
 * - 每个问答对包含两个message: 第一个是问题，第二个是答案
 * - 答案完成标志: 出现 Message_messageMetadataContainer 元素（显示时间的组件）
 * - 使用模糊匹配（[class*=""]）避免hash变化导致选择器失效
 */
export const poeSelector: SelectorConfig = {
  /**
   * 输入框选择器
   * Poe使用 GrowingTextArea 组件
   */
  inputField: [
    '[class*="GrowingTextArea_textArea"]',        // 主输入框（textarea元素）- 模糊匹配
    'textarea[placeholder="信息"]',                // 通过placeholder识别
    '[class*="ChatMessageInputContainer_textArea"] textarea',  // 通过父容器识别 - 模糊匹配
  ],
  
  /**
   * 发送按钮选择器
   */
  sendButton: [
    'button[data-button-send="true"]',            // 主发送按钮标识（data属性稳定）
    'button[aria-label="发送信息"]',               // 中文发送按钮
    'button[aria-label="Send message"]',          // 英文发送按钮
    '[class*="ChatMessageInputContainer_actionContainerRight"] button:last-child',  // 右侧操作区最后一个按钮 - 模糊匹配
  ],
  
  /**
   * 页面头部内容选择器（如机器人信息）
   */
  headerContent: [
    '[class*="BotInfoCard_sectionContainer"]',    // 机器人信息卡片 - 模糊匹配
    '[class*="BotInfoCardHeader_header"]',        // 机器人信息头部 - 模糊匹配
  ],

  /**
   * 聊天内容列表容器选择器
   * Poe使用 InfiniteScroll 布局
   */
  chatContentList: [
    '[class*="InfiniteScroll_container"][class*="ChatMessagesView_infiniteScroll"]',  // 主聊天容器 - 模糊匹配
    '[class*="ChatMessagesScrollWrapper_scrollableContainerWrapper"]',                 // 滚动容器包装器 - 模糊匹配
    '[class*="ChatMessagesView_tupleGroupContainer"]',                                 // 问答对组容器 - 模糊匹配
  ],

  /**
   * 问答对容器选择器 ⚠️ Poe特有
   * 每个问答对包含一个问题和一个答案
   */
  messageTuple: [
    '[class*="ChatMessagesView_messageTuple"]',   // 问答对容器 - 模糊匹配（推荐）
  ],

  /**
   * 用户提问项选择器
   * 在Poe中，用户问题是问答对中的第一个message
   */
  promptItem: [
    '[class*="ChatMessage_rightSideMessageWrapper"]',  // 右侧消息包装器（用户消息在右侧）- 模糊匹配
    '[class*="Message_rightSideMessageRow"]',          // 右侧消息行 - 模糊匹配
    '[class*="Message_rightSideMessageBubble"]',       // 右侧消息气泡 - 模糊匹配
  ],

  /**
   * 用户提问内容选择器
   */
  promptContent: [
    '[class*="Message_rightSideMessageBubble"] [class*="Message_messageTextContainer"]',  // 右侧消息文本容器 - 模糊匹配
    '[class*="Message_rightSideMessageBubble"] [class*="Message_selectableText"]',        // 右侧可选择文本 - 模糊匹配
    '[class*="Message_rightSideMessageBubble"] [class*="Markdown_markdownContainer"]',    // 右侧Markdown容器 - 模糊匹配
  ],

  /**
   * 用户提问操作区选择器
   */
  promptAction: [
    '[class*="ChatMessage_rightSideMessageWrapper"] button',            // 右侧消息按钮 - 模糊匹配
    '[class*="MessageOverflowActions_overflowActionsWrapper"] button',  // 溢出操作按钮 - 模糊匹配
  ],

  /**
   * AI回答项选择器
   * 在Poe中，AI答案是问答对中的第二个message，且在左侧
   */
  answerItem: [
    '[class*="Message_leftSideMessageBubble"]',                                                    // 左侧消息气泡（AI答案在左侧）- 模糊匹配
    '[class*="ChatMessage_messageWrapper"]:not([class*="ChatMessage_rightSideMessageWrapper"])',  // 左侧消息包装器 - 模糊匹配
  ],

  /**
   * 答案完成标志选择器 ⚠️ 关键
   * 当答案完成时，会出现显示时间的元素
   */
  answerCompletion: [
    '[class*="Message_messageMetadataContainer"]',                                         // 元数据容器（显示时间的组件）- 模糊匹配
    '[class*="Message_leftSideMessageBubble"] [class*="Message_messageMetadataContainer"]',  // 左侧消息的元数据 - 模糊匹配
  ],

  /**
   * 复制按钮选择器
   * ⚠️ 注意：Poe没有明显的复制按钮，需要使用溢出菜单中的操作
   */
  copyButton: [
    '[class*="ChatMessageOverflowButton_overflowButtonWrapper"] button',  // 溢出操作按钮 - 模糊匹配
    '[class*="ChatMessage_messageOverflowButton"]',                        // 消息溢出按钮 - 模糊匹配
    'button[aria-label="更多操作"]',                                        // 中文"更多操作"按钮
    'button[aria-label="More actions"]',                                   // 英文"More actions"按钮
  ],

  /**
   * Markdown内容选择器
   * 用于提取答案的Markdown内容
   */
  markdown: [
    '[class*="Markdown_markdownContainer"]',                                     // Markdown容器 - 模糊匹配
    '[class*="Prose_prose"]',                                                    // Prose容器 - 模糊匹配
    '[class*="Message_leftSideMessageBubble"] [class*="Markdown_markdownContainer"]',  // 左侧消息的Markdown - 模糊匹配
    '[class*="Message_leftSideMessageBubble"] [class*="Prose_prose"]',                 // 左侧消息的Prose - 模糊匹配
  ],

  /**
   * 代码块选择器
   * Poe使用自定义代码块组件
   */
  codeBlock: [
    '[class*="MarkdownCodeBlock_container"]',     // 代码块容器 - 模糊匹配
    '[class*="MarkdownCodeBlock_codeBlock"]',     // 代码块主体 - 模糊匹配
    '[class*="MarkdownCodeBlock_preTag"] code',   // 代码元素 - 模糊匹配
  ],

  /**
   * 消息元素选择器
   * 用于识别单个消息（问题或答案）
   */
  messageElement: [
    '[id^="message-"]',                           // 以message-开头的id元素（稳定）
    '[class*="ChatMessage_chatMessage"]',         // 聊天消息类 - 模糊匹配
  ],

  /**
   * 机器人头像/信息选择器
   * 用于识别当前使用的AI模型
   */
  botInfo: [
    '[class*="BotMessageHeader_wrapper"]',        // 机器人消息头部 - 模糊匹配
    '[class*="BotHeader_title"]',                 // 机器人标题 - 模糊匹配
    '[class*="LeftSideMessageHeader_leftSideMessageHeader"]',  // 左侧消息头部 - 模糊匹配
  ],

  /**
   * 日期分隔符选择器
   */
  dateSeparator: [
    '[class*="MessageDate_container"]',           // 日期容器 - 模糊匹配
  ],
};
