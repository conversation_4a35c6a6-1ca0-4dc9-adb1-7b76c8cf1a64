import { BasePageService } from "@/content/core/BasePageService";

/**
 * Doubao (豆包) 页面状态管理服务
 * 继承 BasePageService，提供 Doubao 平台特定的 URL 匹配规则
 * 
 * @特殊说明
 * - 域名: www.doubao.com
 * - Home页: /chat/ 或 /chat/?from_login=xxx
 * - Chat页: /chat/{chatId}，其中 chatId 是数字
 * - 页面逻辑与 Dola 完全一致
 */
export class DoubaoPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Doubao';
    }

    /**
     * 获取 Doubao 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://www.doubao.com/chat/ 或 https://www.doubao.com/chat/?from_login=xxx
            home: /^https:\/\/www\.doubao\.com\/chat\/(\?.*)?$/i,
            
            // Chat页模式: https://www.doubao.com/chat/{chatId}
            // chatId 是纯数字，例如: /chat/19086349329693954
            chat: /^https:\/\/www\.doubao\.com\/chat\/(\d+)$/i
        };
    }

    /**
     * 检测欢迎页面（Doubao 特定实现）
     * 
     * @returns {boolean} 是否为欢迎页
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（Doubao 特有的欢迎页面元素）
        // 欢迎页有 "新对话" 按钮且没有消息历史
        return document.querySelector('[data-testid="create_conversation_button"]') !== null &&
               document.querySelector('[data-testid="message-list"]') === null;
    }
    
    /**
     * 检测聊天页面（Doubao 特定实现）
     * 
     * @returns {boolean} 是否为聊天页
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测（存在消息列表）
        return document.querySelector('[data-testid="message-list"]') !== null ||
               document.querySelector('[data-testid="union_message"]') !== null;
    }

    /**
     * 从 URL 中提取 chatId
     * 
     * @returns {string | null} chatId 或 null（如果不在聊天页）
     * 
     * @example
     * // URL: https://www.doubao.com/chat/19086349329693954
     * getChatId() // 返回 "19086349329693954"
     */
    public getChatId(): string | null {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat && urlResult.chatId) {
            return urlResult.chatId;
        }
        
        // 降级从 DOM 获取 chatId（从 URL 或数据属性）
        const match = window.location.pathname.match(/\/chat\/(\d+)/);
        if (match) {
            return match[1];
        }
        
        // 尝试从激活的对话项获取
        const activeChat = document.querySelector('[data-testid="chat_list_thread_item"].active-link');
        if (activeChat) {
            const idMatch = activeChat.id.match(/conversation_(\d+)/);
            if (idMatch) {
                return idMatch[1];
            }
        }
        
        return null;
    }

    /**
     * 检测是否为 Doubao 平台页面
     * 
     * @returns {boolean} 是否为 Doubao 页面
     */
    public isDoubaoPage(): boolean {
        const hostname = window.location.hostname.toLowerCase();
        return hostname.includes('doubao.com');
    }

    /**
     * 获取当前机器人/模型信息
     * 
     * @returns {string | null} 机器人名称或 null
     */
    public getBotName(): string | null {
        // 从头部获取机器人名称
        const headerTitle = document.querySelector('[data-testid="chat_route_layout"] .title');
        if (headerTitle && headerTitle.textContent) {
            return headerTitle.textContent.trim();
        }
        
        // 从工作区图标获取
        const workspaceIcon = document.querySelector('[data-testid="workspace_icon"] .title');
        if (workspaceIcon && workspaceIcon.textContent) {
            return workspaceIcon.textContent.trim();
        }
        
        return 'Doubao'; // 默认返回平台名称
    }
}
