import './NotionSetupModal.css';
import { localStorageService, NotionConfig } from '@/common/service/LocalStorageService';
import { MessageType } from '@/common/types/enums';
import { NotionConfigValidation } from '@/common/types/NotionTypes';

/**
 * Notion 配置引导 Modal
 * 首次使用时引导用户配置 Token 和 Page
 */
export class NotionSetupModal {
    private overlay: HTMLElement | null = null;
    private modal: HTMLElement | null = null;
    private tokenInput: HTMLInputElement | null = null;
    private pageUrlInput: HTMLInputElement | null = null;
    private step: number = 1; // 当前步骤
    private totalSteps: number = 2; // 总步骤数
    private answerIndex?: number; // 可选的答案索引

    constructor(answerIndex?: number) {
        this.answerIndex = answerIndex;
    }

    /**
     * 创建 Modal 结构
     */
    private createModal(): void {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'notion-setup-modal-overlay';
        this.overlay.addEventListener('click', this.handleOverlayClick.bind(this));

        // 创建 Modal 容器
        this.modal = document.createElement('div');
        this.modal.className = 'notion-setup-modal';
        this.modal.addEventListener('click', (e) => e.stopPropagation());

        // 创建内容
        this.renderStepContent();

        this.overlay.appendChild(this.modal);
    }

    /**
     * 渲染步骤内容
     */
    private renderStepContent(): void {
        if (!this.modal) return;

        this.modal.innerHTML = '';

        if (this.step === 1) {
            this.renderStep1();
        } else if (this.step === 2) {
            this.renderStep2();
        }
    }

    /**
     * 渲染步骤 1：引导说明
     */
    private renderStep1(): void {
        if (!this.modal) return;

        // Header
        const header = document.createElement('div');
        header.className = 'setup-header';
        header.innerHTML = `
            <div class="header-title">
                <svg width="32" height="32" viewBox="0 0 59.9 62.6" xmlns="http://www.w3.org/2000/svg">
                    <style type="text/css">
                        .st0{fill:#000000;}
                        .st1{fill:#FFFFFF;fill-rule:evenodd;clip-rule:evenodd;}
                    </style>
                    <g>
                        <g>
                            <path class="st0" d="M3.8,2.7l34.6-2.6c4.2-0.4,5.3-0.1,8,1.8l11.1,7.8c1.8,1.3,2.4,1.7,2.4,3.2v42.7c0,2.7-1,4.3-4.4,4.5
                                l-40.2,2.4c-2.6,0.1-3.8-0.2-5.1-1.9L2.1,50.1c-1.5-2-2.1-3.4-2.1-5.1V7C0,4.8,1,2.9,3.8,2.7L3.8,2.7z M3.8,2.7"/>
                            <path class="st1" d="M38.4,0.1L3.8,2.7C1,2.9,0,4.8,0,7v38c0,1.7,0.6,3.2,2.1,5.1l8.1,10.6c1.3,1.7,2.6,2.1,5.1,1.9l40.2-2.4
                                c3.4-0.2,4.4-1.8,4.4-4.5V12.9c0-1.4-0.5-1.8-2.2-3c-0.1-0.1-0.2-0.1-0.3-0.2L46.4,2C43.8,0,42.7-0.2,38.4,0.1L38.4,0.1z
                                M16.2,12.2c-3.3,0.2-4,0.3-5.9-1.3L5.6,7.2C5.1,6.7,5.3,6.1,6.6,6l33.3-2.4c2.8-0.2,4.2,0.7,5.3,1.6l5.7,4.1
                                c0.3,0.1,0.9,0.8,0.1,0.8l-34.4,2.1L16.2,12.2z M12.4,55.3V19c0-1.6,0.5-2.3,1.9-2.4l39.5-2.3c1.3-0.1,1.9,0.7,1.9,2.3v36
                                c0,1.6-0.3,2.9-2.4,3l-37.8,2.2C13.4,58,12.4,57.2,12.4,55.3L12.4,55.3z M49.7,21c0.2,1.1,0,2.2-1.1,2.3l-1.8,0.4v26.8
                                c-1.6,0.9-3,1.3-4.2,1.3c-1.9,0-2.4-0.6-3.9-2.4L26.7,30.6v18.1l3.8,0.9c0,0,0,2.2-3,2.2l-8.4,0.5c-0.2-0.5,0-1.7,0.8-1.9l2.2-0.6
                                v-24l-3-0.3c-0.2-1.1,0.4-2.7,2.1-2.8l9-0.6l12.4,19V24.3l-3.2-0.4c-0.2-1.3,0.7-2.3,1.9-2.4L49.7,21z M49.7,21"/>
                        </g>
                    </g>
                </svg>
                <h3>配置 Notion 导出</h3>
            </div>
            <button class="close-btn" title="关闭">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </button>
        `;

        const closeBtn = header.querySelector('.close-btn');
        closeBtn?.addEventListener('click', this.hide.bind(this));

        // Body
        const body = document.createElement('div');
        body.className = 'setup-body';
        body.innerHTML = `
            <div class="step-indicator">
                <div class="step active">1</div>
                <div class="step-line"></div>
                <div class="step">2</div>
            </div>
            
            <h4>欢迎使用 Notion 导出功能</h4>
            <p>请按照以下步骤配置 Notion Integration：</p>
            
            <div class="setup-steps">
                <div class="setup-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h5>创建 Integration</h5>
                        <p>访问 <a href="https://www.notion.so/my-integrations" target="_blank">Notion Integrations</a> 页面</p>
                        <p>点击 "+ New integration" 创建新的 Integration</p>
                    </div>
                </div>
                
                <div class="setup-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h5>配置权限和页面</h5>
                        <p>在 "Capabilities" 中启用以下权限：</p>
                        <ul>
                            <li>Read content</li>
                            <li>Insert content</li>
                        </ul>
                        <p style="margin-top: 12px;">在 "Access" 中配置可访问的页面：</p>
                        <ul>
                            <li>选择或创建一个用于保存 AI 对话的页面</li>
                            <li>该页面将作为你的默认导出位置</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;

        // Footer
        const footer = document.createElement('div');
        footer.className = 'setup-footer';
        footer.innerHTML = `
            <button class="btn-secondary cancel-btn">取消</button>
            <button class="btn-primary next-btn">下一步</button>
        `;

        const cancelBtn = footer.querySelector('.cancel-btn');
        const nextBtn = footer.querySelector('.next-btn');
        
        cancelBtn?.addEventListener('click', this.hide.bind(this));
        nextBtn?.addEventListener('click', this.goToStep2.bind(this));

        this.modal.appendChild(header);
        this.modal.appendChild(body);
        this.modal.appendChild(footer);
    }

    /**
     * 渲染步骤 2：输入配置
     */
    private renderStep2(): void {
        if (!this.modal) return;

        // Header
        const header = document.createElement('div');
        header.className = 'setup-header';
        header.innerHTML = `
            <div class="header-title">
                <svg width="32" height="32" viewBox="0 0 59.9 62.6" xmlns="http://www.w3.org/2000/svg">
                    <style type="text/css">
                        .st0{fill:#000000;}
                        .st1{fill:#FFFFFF;fill-rule:evenodd;clip-rule:evenodd;}
                    </style>
                    <g>
                        <g>
                            <path class="st0" d="M3.8,2.7l34.6-2.6c4.2-0.4,5.3-0.1,8,1.8l11.1,7.8c1.8,1.3,2.4,1.7,2.4,3.2v42.7c0,2.7-1,4.3-4.4,4.5
                                l-40.2,2.4c-2.6,0.1-3.8-0.2-5.1-1.9L2.1,50.1c-1.5-2-2.1-3.4-2.1-5.1V7C0,4.8,1,2.9,3.8,2.7L3.8,2.7z M3.8,2.7"/>
                            <path class="st1" d="M38.4,0.1L3.8,2.7C1,2.9,0,4.8,0,7v38c0,1.7,0.6,3.2,2.1,5.1l8.1,10.6c1.3,1.7,2.6,2.1,5.1,1.9l40.2-2.4
                                c3.4-0.2,4.4-1.8,4.4-4.5V12.9c0-1.4-0.5-1.8-2.2-3c-0.1-0.1-0.2-0.1-0.3-0.2L46.4,2C43.8,0,42.7-0.2,38.4,0.1L38.4,0.1z
                                M16.2,12.2c-3.3,0.2-4,0.3-5.9-1.3L5.6,7.2C5.1,6.7,5.3,6.1,6.6,6l33.3-2.4c2.8-0.2,4.2,0.7,5.3,1.6l5.7,4.1
                                c0.3,0.1,0.9,0.8,0.1,0.8l-34.4,2.1L16.2,12.2z M12.4,55.3V19c0-1.6,0.5-2.3,1.9-2.4l39.5-2.3c1.3-0.1,1.9,0.7,1.9,2.3v36
                                c0,1.6-0.3,2.9-2.4,3l-37.8,2.2C13.4,58,12.4,57.2,12.4,55.3L12.4,55.3z M49.7,21c0.2,1.1,0,2.2-1.1,2.3l-1.8,0.4v26.8
                                c-1.6,0.9-3,1.3-4.2,1.3c-1.9,0-2.4-0.6-3.9-2.4L26.7,30.6v18.1l3.8,0.9c0,0,0,2.2-3,2.2l-8.4,0.5c-0.2-0.5,0-1.7,0.8-1.9l2.2-0.6
                                v-24l-3-0.3c-0.2-1.1,0.4-2.7,2.1-2.8l9-0.6l12.4,19V24.3l-3.2-0.4c-0.2-1.3,0.7-2.3,1.9-2.4L49.7,21z M49.7,21"/>
                        </g>
                    </g>
                </svg>
                <h3>输入配置信息</h3>
            </div>
            <button class="close-btn" title="关闭">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M12 4L4 12M4 4l8 8" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
            </button>
        `;

        const closeBtn = header.querySelector('.close-btn');
        closeBtn?.addEventListener('click', this.hide.bind(this));

        // Body
        const body = document.createElement('div');
        body.className = 'setup-body';
        body.innerHTML = `
            <div class="step-indicator">
                <div class="step completed">1</div>
                <div class="step-line active"></div>
                <div class="step active">2</div>
            </div>
            
            <div class="form-group">
                <label for="notion-token">Internal Integration Secret *</label>
                <input 
                    type="password" 
                    id="notion-token" 
                    class="form-control" 
                    placeholder="secret_xxxxxxxxxxxx"
                >
                <small>
                    在 Integration 设置页面的 "Secrets" 标签中，找到并复制 "Internal Integration Secret"
                </small>
            </div>
            
            <div class="form-group">
                <label for="notion-page-url">页面 URL *</label>
                <input 
                    type="text" 
                    id="notion-page-url" 
                    class="form-control" 
                    placeholder="https://www.notion.so/AIQ-2850fce3d30280c2b6cfd2f3db8xxxxx"
                >
                <small>
                    访问 <a href="https://www.notion.so/" target="_blank">https://www.notion.so/</a>，
                    打开你要保存对话的页面，复制浏览器地址栏中的完整 URL
                </small>
            </div>
            
            <div class="test-section">
                <button class="btn-test" id="test-config-btn">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" style="margin-right: 6px;">
                        <path d="M13.5 2L6 14.5L2.5 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    测试连接
                </button>
                <div class="test-result" id="test-result"></div>
            </div>
        `;

        this.tokenInput = body.querySelector('#notion-token');
        this.pageUrlInput = body.querySelector('#notion-page-url');

        // 绑定测试按钮
        const testBtn = body.querySelector('#test-config-btn');
        testBtn?.addEventListener('click', this.handleTest.bind(this));

        // Footer
        const footer = document.createElement('div');
        footer.className = 'setup-footer';
        footer.innerHTML = `
            <button class="btn-secondary back-btn">上一步</button>
            <button class="btn-primary finish-btn" disabled>完成配置</button>
        `;

        const backBtn = footer.querySelector('.back-btn');
        const finishBtn = footer.querySelector('.finish-btn');
        
        backBtn?.addEventListener('click', this.goToStep1.bind(this));
        finishBtn?.addEventListener('click', this.handleFinish.bind(this));

        this.modal.appendChild(header);
        this.modal.appendChild(body);
        this.modal.appendChild(footer);
    }

    /**
     * 前往步骤 2
     */
    private goToStep2(): void {
        this.step = 2;
        this.renderStepContent();
    }

    /**
     * 返回步骤 1
     */
    private goToStep1(): void {
        this.step = 1;
        this.renderStepContent();
    }

    /**
     * 处理测试连接
     */
    private async handleTest(): Promise<void> {
        const token = this.tokenInput?.value.trim();
        const pageUrl = this.pageUrlInput?.value.trim();

        if (!token || !pageUrl) {
            this.showTestResult('请先填写所有必填项', 'error');
            return;
        }

        // 解析 Page URL
        const pageInfo = this.parsePageUrl(pageUrl);
        if (!pageInfo) {
            this.showTestResult('页面 URL 格式不正确，请检查格式', 'error');
            return;
        }

        // 显示测试中状态
        this.showTestResult('正在测试连接...', 'loading');

        // 禁用测试按钮
        const testBtn = this.modal?.querySelector('#test-config-btn') as HTMLButtonElement;
        if (testBtn) {
            testBtn.disabled = true;
        }

        // 构建配置
        const config: NotionConfig = {
            token,
            pageId: pageInfo.pageId,
            pageName: pageInfo.pageName
        };

        // 调用 Background Script 验证配置
        const validation = await this.validateConfig(config);

        // 启用测试按钮
        if (testBtn) {
            testBtn.disabled = false;
        }

        if (validation.valid) {
            this.showTestResult(`✓ 连接成功！已找到页面: ${pageInfo.pageName}`, 'success');
            
            // 启用完成按钮
            const finishBtn = this.modal?.querySelector('.finish-btn') as HTMLButtonElement;
            if (finishBtn) {
                finishBtn.disabled = false;
            }
        } else {
            this.showTestResult(`✗ 连接失败: ${validation.error || '无法访问该页面'}`, 'error');
        }
    }

    /**
     * 处理完成配置
     */
    private async handleFinish(): Promise<void> {
        const token = this.tokenInput?.value.trim();
        const pageUrl = this.pageUrlInput?.value.trim();

        if (!token || !pageUrl) {
            this.showTestResult('请填写所有必填项', 'error');
            return;
        }

        // 解析 Page URL
        const pageInfo = this.parsePageUrl(pageUrl);
        if (!pageInfo) {
            this.showTestResult('页面 URL 格式不正确', 'error');
            return;
        }

        // 构建配置
        const config: NotionConfig = {
            token,
            pageId: pageInfo.pageId,
            pageName: pageInfo.pageName
        };

        // 保存配置
        await localStorageService.saveNotionConfig(config);
        this.showTestResult('✓ 配置已保存！', 'success');
        
        // 延迟关闭并打开导出 Modal
        setTimeout(() => {
            this.hide();
            // 触发配置完成事件，打开导出 Modal
            document.dispatchEvent(new CustomEvent('NOTION_CONFIG_COMPLETED', {
                detail: { 
                    openExportModal: true,
                    answerIndex: this.answerIndex 
                }
            }));
        }, 500);
    }

    /**
     * 解析 Page URL
     */
    private parsePageUrl(url: string): { pageId: string; pageName: string } | null {
        try {
            // 支持格式：
            // https://www.notion.so/Page-Title-xxxxx
            // https://www.notion.so/username/Page-Title-xxxxx
            // https://www.notion.so/xxxxx

            const urlObj = new URL(url);
            const pathname = urlObj.pathname;

            // 提取最后一段（可能包含 pageId）
            const segments = pathname.split('/').filter(Boolean);
            if (segments.length === 0) {
                return null;
            }

            const lastSegment = segments[segments.length - 1];

            // 提取 32 位十六进制 ID（可能有横线）
            const idMatch = lastSegment.match(/([a-f0-9]{32}|[a-f0-9-]{36})$/i);
            if (!idMatch) {
                return null;
            }

            const pageId = idMatch[1].replace(/-/g, ''); // 移除横线

            // 提取 pageName（ID 之前的部分）
            let pageName = lastSegment.substring(0, lastSegment.length - idMatch[0].length);
            pageName = pageName.replace(/-$/, ''); // 移除末尾横线
            pageName = pageName.replace(/-/g, ' '); // 横线转空格

            if (!pageName) {
                pageName = 'Notion Page';
            }

            return { pageId, pageName };
        } catch (error) {
            console.error('[NotionSetupModal] 解析 URL 失败', error);
            return null;
        }
    }

    /**
     * 验证配置
     */
    private async validateConfig(config: NotionConfig): Promise<NotionConfigValidation> {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage(
                {
                    type: MessageType.NOTION_VALIDATE_CONFIG,
                    payload: config
                },
                (response) => {
                    if (response?.success) {
                        resolve(response.data);
                    } else {
                        resolve({
                            valid: false,
                            error: response?.error || '验证失败'
                        });
                    }
                }
            );
        });
    }

    /**
     * 显示测试结果
     */
    private showTestResult(message: string, type: 'loading' | 'success' | 'error'): void {
        const resultEl = this.modal?.querySelector('#test-result');
        if (resultEl) {
            resultEl.className = `test-result ${type}`;
            resultEl.textContent = message;
        }
    }

    /**
     * 处理遮罩层点击
     */
    private handleOverlayClick(event: MouseEvent): void {
        if (event.target === this.overlay) {
            this.hide();
        }
    }

    /**
     * 显示 Modal
     */
    public show(): void {
        if (!this.overlay) {
            this.createModal();
        }

        if (this.overlay) {
            document.body.appendChild(this.overlay);
            // 触发动画
            setTimeout(() => {
                this.overlay?.classList.add('show');
            }, 10);
        }
    }

    /**
     * 隐藏 Modal
     */
    public hide(): void {
        if (this.overlay) {
            this.overlay.classList.remove('show');
            setTimeout(() => {
                this.overlay?.remove();
                this.overlay = null;
                this.modal = null;
            }, 300);
        }
    }
}
