import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { DoubaoAnswerService } from "./DoubaoAnswerService";
import { DoubaoPageService } from "./DoubaoPageService";

/**
 * <PERSON><PERSON><PERSON> (豆包) 答案控制器
 * 继承 BaseAnswerController，提供 Doubao 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 DoubaoAnswerService 和 DoubaoPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 */
export class DoubaoAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: DoubaoAnswerService;
        pageService: DoubaoPageService;
    } {
        console.info('[DoubaoAnswerController] 创建 Doubao 服务实例');
        
        return {
            answerService: new DoubaoAnswerService(),
            pageService: new DoubaoPageService()
        };
    }
}
