import { SelectorConfig } from "./SelectorManager";

/**
 * Monica 适配器的选择器配置
 * 整合了所有 Monica 平台特有的选择器
 * 
 * 注意：
 * 1. Monica 平台支持复制按钮功能
 * 2. Monica 使用 CSS Modules，类名包含动态 hash（如 chat-items--etBvz）
 * 3. 使用属性选择器 [class*="xxx"] 进行部分匹配，避免 hash 变化导致失效
 */
export const monicaSelector: SelectorConfig = {
  inputField: [
    'textarea[data-input_node="monica-chat-input"]',  // Monica 主输入框
    'textarea[class*="textarea"]',                     // 包含 textarea 类名
    'textarea[placeholder*="问我任何问题"]'             // 中文占位符
  ],
  
  sendButton: [
    '[class*="input-msg-btn"]',                       // 发送按钮（部分匹配）
    '[class*="inside-input-actions"] [class*="input-msg-btn"]',  // 发送按钮（备用）
    'button[aria-label*="发送"]'                      // 发送按钮（通用）
  ],
  
  headerContent: [
    // Monica 没有特定的 header content
  ],

  // Monica 特有的选择器
  chatContentList: [
    '[class*="chat-items"]',                          // 聊天消息列表（部分匹配）
    '[class*="chat-items-container"]',                // 聊天消息容器（部分匹配）
    '#monica-chat-scroll-box [class*="chat-items"]'   // 滚动区域内的消息列表
  ],

  promptItem: [
    '[class*="chat-message"][class*="chat-question"]', // 用户消息容器（精确匹配）
    '[class*="chat-question"]'                        // 用户消息容器（备用）
  ],

  promptContent: [
    '[class*="chat-question"] [class*="text-content"] [class*="inner"]',  // 用户消息内容（精确路径）
    '[class*="chat-question"] [class*="inner"]'       // 用户消息内容（备用）
  ],

  promptAction: [
    '[class*="chat-question"] [class*="message-toolbar"]',  // 用户消息工具栏
    '[class*="chat-question"] button'                 // 用户消息按钮组
  ],

  answerItem: [
    '[class*="chat-message"][class*="chat-reply"]',   // AI回复消息容器（精确匹配）
    '[class*="chat-reply"]'                           // AI回复消息容器（备用）
  ],

  answerCompletion: [
    '[class*="message-toolbar--"]',                   // 消息工具栏（答案完成标志）
    '[class*="chat-reply"] [class*="message-toolbar--"]',  // AI回复中的消息工具栏
    '[class*="chat-reply"]'                           // AI回复消息容器（备用）
  ],

  copyButton: [
    '[class*="code-enhance-copy"]',                   // 代码块复制按钮（部分匹配）
    '[class*="message-toolbar"] [class*="copy-group"]',  // 消息工具栏复制按钮
    'button[aria-label*="复制"]',                     // 复制按钮（通用）
    'button[aria-label*="Copy"]'                      // 复制按钮（英文）
  ],

  markdown: [
    '[class*="markdown"].__markdown',                 // Monica markdown 容器（结合类名）
    '[class*="chat-reply"] [class*="markdown"]',      // AI 回复中的 markdown
    '[class*="markdown"]'                             // Markdown 容器（通用）
  ]
};
