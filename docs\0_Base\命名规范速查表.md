# EchoSync 命名规范速查表

## 文件命名速查

| 类型 | 后缀 | 模式 | 位置 | 导出方式 | 示例 |
|-----|------|------|------|---------|------|
| **管理类** | `Manager` | 单例 | `core/` `utils/` | `export const xxx = Xxx.getInstance()` | `ContentScriptManager` |
| **业务类** | `Service` | 非单例 | `adapters/` `service/` | `export class XxxService` | `KimiAnswerService` |
| **控制类** | `Controller` | 非单例 | `adapters/` | `export class XxxController` | `KimiAnswerController` |
| **工具类** | `Utils` | 静态 | `utils/` | `export class XxxUtils` | `DOMUtils` |
| **注入类** | `Inject` | 实例 | `inject/` | `export class XxxInject` | `FloatingBubbleInject` |
| **数据类** | `Model` | 接口 | `model/` | `export interface XxxModel` | `AnswerModel` |
| **配置类** | `Config` `Manager` | 静态/单例 | `configs/` | `export default class` | `SelectorManager` |

## 典型文件示例

### Manager（单例管理类）
```typescript
// src/content/core/ContentScriptManager.ts
export class ContentScriptManager extends Singleton<ContentScriptManager> {
  public async initialize(): Promise<void> { }
  public destroy(): void { }
}
export const contentScriptManager = ContentScriptManager.getInstance();
```

### Service（业务服务类）
```typescript
// src/content/adapters/kimi/KimiAnswerService.ts
export class KimiAnswerService extends BaseAnswerService {
  protected async captureExistingQAPairs(): Promise<void> { }
}
// 使用: const service = new KimiAnswerService();
```

### Controller（流程控制类）
```typescript
// src/content/adapters/kimi/KimiAnswerController.ts
export class KimiAnswerController extends BaseAnswerController {
  constructor() {
    super(new KimiAnswerService(), new KimiClipboardService());
  }
}
```

### Utils（工具类）
```typescript
// src/content/utils/DOMUtils.ts
export class DOMUtils {
  static async waitForPageLoad(): Promise<void> { }
  static findElementInContainer(...): Element | null { }
}
// 使用: await DOMUtils.waitForPageLoad();
```

### Inject（注入组件）
```typescript
// src/content/inject/FloatingBubbleInject.ts
export class FloatingBubbleInject {
  public inject(): void { }
  public remove(): void { }
}
```

## 目录结构速查

```
src/content/
├── adapters/         → Service + Controller（平台特定）
├── configs/          → Config（配置）
├── core/             → Base 类 + Manager（核心）
├── inject/           → Inject（注入）
├── model/            → Model（数据）
├── utils/            → Utils + Manager（工具）
└── index.ts          → 入口
```

## 继承关系速查

```
BaseAnswerService → KimiAnswerService
BaseClipboardService → KimiClipboardService
BaseAnswerController → KimiAnswerController
```

## 日志格式速查

```typescript
console.info('[ClassName] ✅ 成功信息')
console.warn('[ClassName] ⚠️ 警告信息')
console.error('[ClassName] ❌ 错误信息')
console.log('[ClassName] 调试信息')
```

## 导入顺序速查

```typescript
// 1. 第三方库
import TurndownService from 'turndown';

// 2. 基类（@ 别名）
import { BaseService } from '@/content/core/BaseService';

// 3. 工具类（相对路径）
import { DOMUtils } from '../../utils/DOMUtils';

// 4. 类型
import type { Model } from '../../model/Model';
```

## 注释格式速查

```typescript
/**
 * 类说明
 * 继承关系说明
 * 
 * 主要功能：
 * - 功能1
 * - 功能2
 */
export class ClassName {
  /**
   * 方法说明
   * @param param1 参数1说明
   * @param param2 参数2说明
   * @returns 返回值说明
   */
  public method(param1: string, param2: number): string {
    // 实现
  }
}
```

## 快速决策树

**我需要创建一个新类，应该用什么后缀？**

```
需要全局唯一实例？
├─ 是 → Manager
└─ 否 → 继续

实现业务逻辑？
├─ 是 → Service
└─ 否 → 继续

协调多个 Service？
├─ 是 → Controller
└─ 否 → 继续

静态工具方法？
├─ 是 → Utils
└─ 否 → 继续

注入 DOM 元素？
├─ 是 → Inject
└─ 否 → 继续

定义数据结构？
├─ 是 → Model
└─ 否 → Config
```

## 常见错误提示

❌ **错误**: Service 使用单例
```typescript
export class XxxService {
  private static instance: XxxService;
  // ❌ Service 不应该是单例
}
```

✅ **正确**: Service 非单例
```typescript
export class XxxService extends BaseService {
  // ✅ Service 可创建多个实例
}
```

---

❌ **错误**: Manager 非单例
```typescript
export class XxxManager {
  // ❌ Manager 应该是单例
}
```

✅ **正确**: Manager 单例
```typescript
export class XxxManager extends Singleton<XxxManager> {
  // ✅ Manager 是单例
}
export const xxxManager = XxxManager.getInstance();
```

---

❌ **错误**: Utils 有状态
```typescript
export class DOMUtils {
  private state: any; // ❌ Utils 不应该有实例状态
}
```

✅ **正确**: Utils 静态方法
```typescript
export class DOMUtils {
  static method(): void { } // ✅ Utils 都是静态方法
}
```

## 参考资料

- 完整规范: `docs/0_Base/代码规范和命名约定.md`
- Copilot 指令: `.github/copilot-instructions.md`
- 项目架构: `docs/3_Project_Structure/`
