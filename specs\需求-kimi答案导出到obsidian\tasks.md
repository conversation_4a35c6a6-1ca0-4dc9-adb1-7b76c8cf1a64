# Kimi 答案导出到 Obsidian - 任务清单

## 📋 任务概览

| 阶段 | 任务数 | 预估工时 | 状态 |
|------|--------|----------|------|
| Phase 1 | 2 | 1h | ⏳ 待开始 |
| Phase 2 | 3 | 2h | ⏳ 待开始 |
| Phase 3 | 4 | 3h | ⏳ 待开始 |
| Phase 4 | 3 | 2h | ⏳ 待开始 |
| Phase 5 | 2 | 1.5h | ⏳ 待开始 |
| Phase 6 | 3 | 2h | ⏳ 待开始 |
| **总计** | **17** | **11.5h** | |

---

## 🎯 Phase 1: 数据模型增强 (1h)

### Task 1.1: 扩展 AnswerModel
- **文件**: `extension/src/content/model/AnswerModel.ts`
- **工作内容**:
  - [ ] 添加 `getQAPair(index: number)` 方法
  - [ ] 添加 `getAnswerCount()` 方法
  - [ ] 添加类型定义 `QAPair`
- **验收标准**:
  - 能通过索引获取问答对
  - 返回数据格式正确
  - 边界情况处理（index 越界）
- **预估工时**: 30min

### Task 1.2: 添加事件机制到 KimiAnswerService
- **文件**: `extension/src/content/service/KimiAnswerService.ts`
- **工作内容**:
  - [ ] 在 `processExistingAnswer` 方法中触发自定义事件
  - [ ] 在 `extractCompletedAnswer` 方法中触发自定义事件
  - [ ] 定义事件类型 `ANSWER_EXTRACTED`
  - [ ] 在事件中传递 `answerElement` 和 `answerIndex`
- **验收标准**:
  - 每次答案提取成功后触发事件
  - 事件携带正确的参数
  - 不影响原有功能
- **预估工时**: 30min

---

## 🎨 Phase 2: 图标按钮组件 (2h)

### Task 2.1: 创建 ObsidianExportButton 组件
- **文件**: `extension/src/content/components/ObsidianExportButton.ts`
- **工作内容**:
  - [ ] 创建类和构造函数
  - [ ] 实现 `render()` 方法
  - [ ] 添加 Obsidian Logo SVG
  - [ ] 实现 `onClick()` 事件绑定
  - [ ] 实现 tooltip 显示/隐藏
- **验收标准**:
  - 渲染正确的图标
  - 悬浮显示 tooltip
  - 点击触发回调
  - 样式符合设计稿
- **预估工时**: 1h

### Task 2.2: 编写 ObsidianExportButton 样式
- **文件**: `extension/src/content/components/ObsidianExportButton.css`
- **工作内容**:
  - [ ] 定义 CSS 变量
  - [ ] 编写按钮基础样式
  - [ ] 编写 hover 样式
  - [ ] 编写 tooltip 样式
  - [ ] 添加过渡动画
- **验收标准**:
  - 样式符合设计规范
  - 过渡流畅
  - 响应式良好
- **预估工时**: 30min

### Task 2.3: 单元测试
- **文件**: `extension/src/content/components/__tests__/ObsidianExportButton.test.ts`
- **工作内容**:
  - [ ] 测试组件渲染
  - [ ] 测试点击事件
  - [ ] 测试 tooltip 显示
- **验收标准**:
  - 测试覆盖率 > 80%
  - 所有测试通过
- **预估工时**: 30min

---

## 🪟 Phase 3: Modal 导出卡片 (3h)

### Task 3.1: 创建 ObsidianExportModal 组件
- **文件**: `extension/src/content/components/ObsidianExportModal.ts`
- **工作内容**:
  - [ ] 创建类和构造函数
  - [ ] 实现 `show()` 方法
  - [ ] 实现 `hide()` 方法
  - [ ] 实现表单数据获取
  - [ ] 实现关闭交互（ESC、点击遮罩、关闭按钮）
- **验收标准**:
  - Modal 正确显示和隐藏
  - 表单数据正确收集
  - 关闭交互正常
- **预估工时**: 1h

### Task 3.2: 创建 Tag 选择器
- **文件**: `extension/src/content/components/TagSelector.ts`
- **工作内容**:
  - [ ] 创建 Tag 输入框
  - [ ] 实现添加标签（回车触发）
  - [ ] 实现删除标签
  - [ ] 实现标签列表渲染
  - [ ] （可选）实现历史标签推荐
- **验收标准**:
  - 可以添加和删除标签
  - 标签去重
  - 交互流畅
- **预估工时**: 1h

### Task 3.3: 编写 Modal 样式
- **文件**: `extension/src/content/components/ObsidianExportModal.css`
- **工作内容**:
  - [ ] 遮罩层样式
  - [ ] Modal 容器样式
  - [ ] Header/Body/Footer 样式
  - [ ] 表单元素样式
  - [ ] Tag 选择器样式
  - [ ] 动画效果（fade-in/fade-out）
- **验收标准**:
  - 样式符合设计规范
  - 蓝紫主题色正确
  - 动画流畅
- **预估工时**: 30min

### Task 3.4: 单元测试
- **文件**: `extension/src/content/components/__tests__/ObsidianExportModal.test.ts`
- **工作内容**:
  - [ ] 测试 Modal 显示/隐藏
  - [ ] 测试表单数据收集
  - [ ] 测试 Tag 操作
- **验收标准**:
  - 测试覆盖率 > 80%
  - 所有测试通过
- **预估工时**: 30min

---

## ⚙️ Phase 4: 导出服务 (2h)

### Task 4.1: 创建 ObsidianExportService
- **文件**: `extension/src/content/service/ObsidianExportService.ts`
- **工作内容**:
  - [ ] 创建单例类
  - [ ] 实现 `exportToObsidian()` 方法
  - [ ] 实现 `getQAPair()` 调用
  - [ ] 实现 `generateMarkdown()` 方法
  - [ ] 实现 `generateFilename()` 方法
  - [ ] 实现 `checkConfiguration()` 方法
  - [ ] 实现 `saveFile()` 方法
- **验收标准**:
  - 导出逻辑完整
  - Markdown 格式正确
  - 文件名生成符合规则
  - 配置检查有效
- **预估工时**: 1h

### Task 4.2: 错误处理
- **文件**: `extension/src/content/service/ObsidianExportService.ts`
- **工作内容**:
  - [ ] 定义错误类型 `ObsidianExportError`
  - [ ] 实现各种错误情况处理
  - [ ] 添加 Toast 提示
- **验收标准**:
  - 错误处理完整
  - 提示信息友好
- **预估工时**: 30min

### Task 4.3: 单元测试
- **文件**: `extension/src/content/service/__tests__/ObsidianExportService.test.ts`
- **工作内容**:
  - [ ] 测试 Markdown 生成
  - [ ] 测试文件名生成（包括重复处理）
  - [ ] 测试配置检查
  - [ ] 测试错误处理
- **验收标准**:
  - 测试覆盖率 > 80%
  - 所有测试通过
- **预估工时**: 30min

---

## 🔌 Phase 5: 注入器实现 (1.5h)

### Task 5.1: 创建 ObsidianExportInject
- **文件**: `extension/src/content/inject/ObsidianExportInject.ts`
- **工作内容**:
  - [ ] 创建类和构造函数
  - [ ] 实现 `start()` 方法
  - [ ] 实现 `stop()` 方法
  - [ ] 监听 `ANSWER_EXTRACTED` 事件
  - [ ] 实现 `handleNewAnswer()` 方法
  - [ ] 实现 `injectButton()` 方法
  - [ ] 管理按钮实例 Map
  - [ ] 处理按钮点击，打开 Modal
- **验收标准**:
  - 监听正确触发
  - 按钮正确注入
  - Modal 正确打开
  - 内存管理良好
- **预估工时**: 1h

### Task 5.2: 集成测试
- **文件**: `extension/src/content/inject/__tests__/ObsidianExportInject.test.ts`
- **工作内容**:
  - [ ] 测试注入流程
  - [ ] 测试按钮点击
  - [ ] 测试 Modal 显示
- **验收标准**:
  - 测试覆盖率 > 70%
  - 所有测试通过
- **预估工时**: 30min

---

## 🚀 Phase 6: 整合与测试 (2h)

### Task 6.1: 注册 Inject 到 content.ts
- **文件**: `extension/src/content/content.ts`
- **工作内容**:
  - [ ] 导入 `ObsidianExportInject`
  - [ ] 在适当时机调用 `inject.start()`
  - [ ] 确保生命周期正确
- **验收标准**:
  - 插件加载时启动注入
  - 不影响其他功能
- **预估工时**: 30min

### Task 6.2: E2E 测试
- **工作内容**:
  - [ ] 在 Kimi 页面测试完整流程
  - [ ] 测试多个答案场景
  - [ ] 测试边界情况（未配置路径、重复文件名等）
  - [ ] 测试错误处理
  - [ ] 测试性能（多个答案注入）
- **验收标准**:
  - 完整流程无报错
  - 导出文件正确
  - 性能可接受
- **预估工时**: 1h

### Task 6.3: 代码审查与优化
- **工作内容**:
  - [ ] 代码规范检查
  - [ ] 性能优化
  - [ ] 注释完善
  - [ ] 文档更新
- **验收标准**:
  - 代码通过 ESLint
  - 注释清晰完整
  - 文档准确
- **预估工时**: 30min

---

## 📝 文件清单

### 新增文件 (13 个)

#### 组件
1. `extension/src/content/components/ObsidianExportButton.ts`
2. `extension/src/content/components/ObsidianExportButton.css`
3. `extension/src/content/components/ObsidianExportModal.ts`
4. `extension/src/content/components/ObsidianExportModal.css`
5. `extension/src/content/components/TagSelector.ts`

#### 服务
6. `extension/src/content/service/ObsidianExportService.ts`

#### 注入器
7. `extension/src/content/inject/ObsidianExportInject.ts`

#### 测试
8. `extension/src/content/components/__tests__/ObsidianExportButton.test.ts`
9. `extension/src/content/components/__tests__/ObsidianExportModal.test.ts`
10. `extension/src/content/service/__tests__/ObsidianExportService.test.ts`
11. `extension/src/content/inject/__tests__/ObsidianExportInject.test.ts`

#### 文档
12. `specs/需求-kimi答案导出/requirements.md` ✅ 已完成
13. `specs/需求-kimi答案导出/design.md` ✅ 已完成
14. `specs/需求-kimi答案导出/tasks.md` ✅ 当前文件

### 修改文件 (3 个)

1. `extension/src/content/model/AnswerModel.ts`
   - 添加 `getQAPair()` 方法
   - 添加 `getAnswerCount()` 方法

2. `extension/src/content/service/KimiAnswerService.ts`
   - 在答案提取成功后触发事件

3. `extension/src/content/content.ts`
   - 注册并启动 `ObsidianExportInject`

---

## 🧪 测试计划

### 单元测试
- [ ] ObsidianExportButton 组件测试
- [ ] ObsidianExportModal 组件测试
- [ ] TagSelector 组件测试
- [ ] ObsidianExportService 服务测试
- [ ] ObsidianExportInject 注入器测试

### 集成测试
- [ ] AnswerModel + ObsidianExportService 集成测试
- [ ] ContentSettingsService + ObsidianExportService 集成测试
- [ ] 注入流程测试

### E2E 测试
- [ ] 完整导出流程测试
- [ ] 多答案场景测试
- [ ] 错误处理测试
- [ ] 边界情况测试

### 性能测试
- [ ] 多个答案同时注入性能测试
- [ ] Modal 打开/关闭性能测试
- [ ] 文件保存性能测试

---

## 🎯 验收标准

### 功能完整性
- ✅ 每个答案都有 Obsidian 图标
- ✅ 图标位置正确（复制按钮旁边）
- ✅ 点击图标打开 Modal
- ✅ Modal 显示问答内容
- ✅ 可以编辑标题
- ✅ 可以添加/删除标签
- ✅ 点击导出按钮成功保存文件
- ✅ 未配置路径时提示用户
- ✅ 文件名重复时自动加后缀

### UI/UX 标准
- ✅ 图标样式符合设计（灰色默认，蓝色悬浮）
- ✅ Modal 居中显示，半透明遮罩
- ✅ Modal 蓝紫主题色正确
- ✅ 动画流畅（fade-in/fade-out）
- ✅ 交互友好（ESC 关闭、点击遮罩关闭）
- ✅ Toast 提示信息清晰

### 代码质量
- ✅ 代码通过 ESLint 检查
- ✅ 测试覆盖率 > 80%
- ✅ 注释清晰完整
- ✅ 无 console.error 或 warning
- ✅ 性能可接受

### 兼容性
- ✅ 不影响现有功能
- ✅ 与其他注入器兼容
- ✅ 内存管理良好（无泄漏）

---

## 📅 里程碑

| 里程碑 | 完成标准 | 截止时间 | 状态 |
|--------|----------|----------|------|
| M1: 数据层准备完成 | Phase 1 所有任务完成 | Day 1 | ⏳ |
| M2: 组件开发完成 | Phase 2 & 3 所有任务完成 | Day 3 | ⏳ |
| M3: 服务和注入完成 | Phase 4 & 5 所有任务完成 | Day 5 | ⏳ |
| M4: 整合测试完成 | Phase 6 所有任务完成 | Day 7 | ⏳ |

---

## 🚨 风险与注意事项

### 技术风险
1. **文件系统权限问题**
   - 缓解措施: 提前测试文件保存 API
   - 错误处理: 捕获权限错误，友好提示

2. **DOM 结构变化**
   - 缓解措施: 使用健壮的选择器
   - 错误处理: 选择器失败时记录日志

3. **性能问题**
   - 缓解措施: 懒加载、防抖、缓存
   - 监控: 添加性能日志

### 开发风险
1. **时间估算偏差**
   - 缓解措施: 预留 20% 缓冲时间
   - 监控: 每日进度检查

2. **测试覆盖不足**
   - 缓解措施: 优先完成核心逻辑测试
   - 监控: 代码审查时检查测试

---

## 📖 参考资料

### 代码参考
- `extension/src/content/inject/ArchiveButtonInject.ts` - 注入器模式
- `extension/src/content/inject/FloatingBubbleInject.ts` - 注入器模式
- `extension/src/content/service/KimiAnswerService.ts` - 服务模式
- `extension/src/content/model/AnswerModel.ts` - 数据模型

### 设计文档
- `specs/需求-kimi答案导出/requirements.md` - 需求文档
- `specs/需求-kimi答案导出/design.md` - 架构设计
- `docs/需求-popup的UI设计.md` - UI 设计参考

### 技术文档
- [Chrome Extension File System API](https://developer.chrome.com/docs/extensions/reference/fileSystem/)
- [MutationObserver MDN](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver)
- [Custom Events MDN](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent)
