# monica平台实现
content模块和common模块新增ai 聊天平台monica的支持，注意：
1. monica平台要支持网站 https://monica.im/
2. monica平台的home页是https://monica.im/home/<USER>/Monica/monica 或者 https://monica.im/home/<USER>/GPT-5/gpt_5 这样的，home后面的信息分别是模型提供商和模型
3. monica平台的chat页是 `https://monica.im/home/<USER>/Monica/monica?convId=conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208` 这样，后面convId=是chatId.
4. 要帮我实现这个平台特定的 dexie.ts 文件中的配置，以及src/content/configs/SelectorManager.ts 文件中的选择器配置。src/content/configs/monicaConfig.ts 文件中的选择器配置。包括适配器的所有子类MonicaAnswerController.ts, MonicaAnswerService.ts, MonicaPageService.ts, MonicaClipboardService.ts. 以及主适配器Monica.ts.
5. 请在实现过程中，如果有需要，可以增加新的选择器配置，但是不要修改其他平台已经存在的选择器配置。
6. 可以使用chrome-mcp查看`https://monica.im/home/<USER>/Monica/monica?convId=conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208` 页面，获得选择器配置相关的内容。
7. 可以借鉴ChatGPT平台的实现模式。

---

## 实现方案设计

### 一、URL 匹配规则分析

根据需求，Monica平台的URL模式：
- **Home页**: `https://monica.im/home/<USER>/{provider}/{model}`
  - 示例: `https://monica.im/home/<USER>/Monica/monica`
  - 示例: `https://monica.im/home/<USER>/GPT-5/gpt_5`
- **Chat页**: `https://monica.im/home/<USER>/{provider}/{model}?convId={chatId}`
  - 示例: `https://monica.im/home/<USER>/Monica/monica?convId=conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208`

**关键问题1**: URL结构分析
- Home页和Chat页的路径部分是相同的，只是Chat页多了`convId`查询参数
- 需要通过查询参数 `convId` 来区分Home页和Chat页

**URL匹配正则表达式**:
```typescript
// Home页模式: 无convId参数
home: /^https:\/\/monica\.im\/home\/<USER>\/[^\/]+\/[^\/]+\/?(\?(?!.*convId=).*)?$/i

// Chat页模式: 有convId参数
chat: /^https:\/\/monica\.im\/home\/<USER>\/[^\/]+\/[^\/]+\/?\?.*convId=([^&]+)/i
```

### 二、DOM 结构分析（基于chrome-mcp获取的页面结构）

#### 1. 输入框选择器
```typescript
// 主输入框
textarea[data-input_node="monica-chat-input"]
textarea.textarea--JX_6f
textarea[placeholder*="问我任何问题"]
```

#### 2. 发送按钮选择器
```typescript
// 发送按钮容器
.input-msg-btn--cT5PX
.inside-input-actions--g5EB0 .input-msg-btn--cT5PX
```

#### 3. 聊天内容区域
```typescript
// 聊天消息容器
.chat-items--etBvz
.chat-items-container--bx3FO
#monica-chat-scroll-box
```

#### 4. 用户提问消息
```typescript
// 用户消息容器
.chat-message--RxFki.chat-question--yrvtP
.chat-question--yrvtP

// 用户消息内容
.chat-question--yrvtP .text-content--tpWNl .inner--m4jj2
```

#### 5. AI回答消息
```typescript
// AI回答消息容器
.chat-message--RxFki.chat-reply--ntVOt
.chat-reply--ntVOt

// AI回答内容(markdown)
.chat-reply--ntVOt .markdown--UqDin
.markdown--UqDin.__markdown
```

#### 6. 复制按钮
```typescript
// 代码块复制按钮
.code-enhance-copy--XipCY
.code-enhance-header-right--R62yQ .code-enhance-copy--XipCY

// 消息工具栏复制按钮
.message-toolbar--jkuMG .copy-group--n8lXU
```

#### 7. 模型切换器
```typescript
// 模型切换按钮
.bot-switcher--mV5HB
.bot-info--tbsxI
.bot-logo--Fr3In
.bot-name--Uxn6h
```

### 三、需要实现的文件清单

#### 1. 数据库配置
- [x] `extension/src/common/database/dexie.ts` - 添加Monica平台数据

#### 2. 选择器配置
- [ ] `extension/src/content/configs/monicaConfig.ts` - Monica选择器配置
- [ ] `extension/src/content/configs/SelectorManager.ts` - 注册Monica选择器

#### 3. 服务层实现
- [ ] `extension/src/content/adapters/monica/MonicaPageService.ts` - 页面状态管理
- [ ] `extension/src/content/adapters/monica/MonicaClipboardService.ts` - 剪贴板服务
- [ ] `extension/src/content/adapters/monica/MonicaAnswerService.ts` - 答案管理服务
- [ ] `extension/src/content/adapters/monica/MonicaAnswerController.ts` - 答案控制器

#### 4. 主适配器
- [ ] `extension/src/content/adapters/monica/monica.ts` - Monica主适配器

### 四、核心设计问题讨论

#### 问题1: URL区分Home和Chat页
**当前方案**: 
- Home页: 路径 `/home/<USER>/{provider}/{model}` 且无 `convId` 参数
- Chat页: 路径 `/home/<USER>/{provider}/{model}` 且有 `convId` 参数

**需要确认**:
- [ ] 是否所有聊天对话页面都会有 `convId` 参数？
- [ ] 新建聊天时是否会立即生成 `convId`？

#### 问题2: chatId提取
**当前方案**: 从URL查询参数 `convId` 提取
```typescript
extractChatId(): string | null {
  const match = window.location.href.match(/convId=([^&]+)/);
  return match ? decodeURIComponent(match[1]) : null;
}
```

**需要确认**:
- [ ] `convId` 值是否需要URL解码？(示例中是 `conv%3A257771e1-...`)
- [ ] chatId 的格式是否总是 `conv:{uuid}` 这种格式？

#### 问题3: 模型信息提取
Monica平台支持多个模型提供商和模型，URL中包含 `{provider}/{model}`

**需要确认**:
- [ ] 是否需要提取并存储模型提供商信息？
- [ ] 不同模型的DOM结构是否一致？

#### 问题4: 答案完成检测
从HTML中看到Monica使用markdown渲染答案，与ChatGPT类似

**当前方案**: 
1. 监听 `.chat-reply--ntVOt` 容器的变化
2. 检测 `.markdown--UqDin` 内容变化
3. 使用复制按钮 `.code-enhance-copy--XipCY` 出现作为完成信号

**需要确认**:
- [ ] Monica是否有流式输出？
- [ ] 答案完成时是否有特定的CSS类或属性变化？
- [ ] 是否需要等待所有代码块的复制按钮都出现？

#### 问题5: 复制按钮策略
**当前方案**: 
- 代码块复制: `.code-enhance-copy--XipCY`
- 消息复制: `.copy-group--n8lXU`

**需要确认**:
- [ ] 优先使用哪个复制按钮？
- [ ] 如果没有代码块，是否使用消息复制按钮？

### 五、选择器配置初步设计

```typescript
// monicaConfig.ts
export const monicaSelector: SelectorConfig = {
  inputField: [
    'textarea[data-input_node="monica-chat-input"]',
    'textarea.textarea--JX_6f',
    'textarea[placeholder*="问我任何问题"]'
  ],
  
  sendButton: [
    '.input-msg-btn--cT5PX',
    '.inside-input-actions--g5EB0 .input-msg-btn--cT5PX'
  ],
  
  chatContentList: [
    '.chat-items--etBvz',
    '.chat-items-container--bx3FO',
    '#monica-chat-scroll-box .chat-items--etBvz'
  ],
  
  promptItem: [
    '.chat-message--RxFki.chat-question--yrvtP',
    '.chat-question--yrvtP'
  ],
  
  promptContent: [
    '.chat-question--yrvtP .text-content--tpWNl .inner--m4jj2',
    '.chat-question--yrvtP .inner--m4jj2'
  ],
  
  answerItem: [
    '.chat-message--RxFki.chat-reply--ntVOt',
    '.chat-reply--ntVOt'
  ],
  
  answerCompletion: [
    '.chat-reply--ntVOt .markdown--UqDin',
    '.chat-reply--ntVOt'
  ],
  
  markdown: [
    '.markdown--UqDin.__markdown',
    '.chat-reply--ntVOt .markdown--UqDin'
  ],
  
  copyButton: [
    '.code-enhance-copy--XipCY',
    '.message-toolbar--jkuMG .copy-group--n8lXU',
    '.copy-group--n8lXU'
  ]
};
```

### 六、实现顺序建议

1. **第一步**: 添加数据库配置（dexie.ts）
2. **第二步**: 实现选择器配置（monicaConfig.ts + SelectorManager.ts）
3. **第三步**: 实现MonicaPageService（URL匹配和页面类型检测）
4. **第四步**: 实现MonicaClipboardService（复制按钮逻辑）
5. **第五步**: 实现MonicaAnswerService（答案监听和提取）
6. **第六步**: 实现MonicaAnswerController（答案控制协调）
7. **第七步**: 实现monica.ts主适配器
8. **第八步**: 测试和调试

---

## 问题确认结果 ✅

1. **URL匹配**: ✅ 通过 `convId` 参数区分Home和Chat页
2. **chatId格式**: ✅ 需要URL解码，格式不固定
3. **模型信息**: ✅ 不需要提取和存储
4. **答案检测**: ✅ 父类BaseAnswerService已处理完成信号
5. **复制策略**: ✅ **Monica平台支持复制按钮，优先使用复制按钮，失败时降级到 HTML 转 Markdown**
6. **DOM选择器**: ✅ 当前分析的选择器已充分，**需要使用属性选择器 `[class*="xxx"]` 处理动态 hash**

## 最终实现方案

### 关键调整：
1. **MonicaClipboardService**: 使用父类的复制按钮实现，降级时使用 HTML 转 Markdown
2. **选择器配置**: 添加 `copyButton` 选择器，**使用 `[class*="xxx"]` 属性选择器匹配含 hash 的类名**
3. **答案检测**: 依赖父类BaseAnswerService的监听机制

### 选择器 Hash 问题解决方案

#### 问题
Monica 平台使用了 CSS Modules 或类似的打包工具，类名中包含动态 hash 后缀：
- 实际类名: `chat-items--etBvz` （hash 会变化）
- 组件中确实有这些带 hash 的类名

#### 解决方案
使用 CSS 属性选择器进行部分匹配：

```typescript
// ❌ 错误方案1: 使用完整类名（hash 变化后失效）
chatContentList: ['.chat-items--etBvz']

// ❌ 错误方案2: 直接去掉 hash（无法匹配实际类名）
chatContentList: ['.chat-items']

// ✅ 正确方案: 使用属性选择器部分匹配
chatContentList: ['[class*="chat-items"]']
```

#### 工作原理
- `[class*="chat-items"]` 会匹配任何包含 "chat-items" 的 class 属性
- 可以匹配 `chat-items--etBvz`、`chat-items--xyz123` 等各种 hash 变体
- 不受打包后 hash 变化的影响

### ✅ 实现完成！

所有 Monica 平台支持文件已创建完成：

#### 1. 数据库配置 ✅
- [x] `extension/src/common/database/dexie.ts` - 添加 Monica 平台（ID: 10）
- [x] `extension/src/common/types/database_entity.ts` - 添加 'Monica' 到 PlatformName 类型

#### 2. 选择器配置 ✅
- [x] `extension/src/content/configs/monicaConfig.ts` - Monica 选择器配置
- [x] `extension/src/content/configs/SelectorManager.ts` - 注册 Monica 选择器

#### 3. 服务层实现 ✅
- [x] `extension/src/content/adapters/monica/MonicaPageService.ts` - 页面状态管理
- [x] `extension/src/content/adapters/monica/MonicaClipboardService.ts` - 剪贴板服务（HTML转Markdown）
- [x] `extension/src/content/adapters/monica/MonicaAnswerService.ts` - 答案管理服务
- [x] `extension/src/content/adapters/monica/MonicaAnswerController.ts` - 答案控制器

#### 4. 主适配器 ✅
- [x] `extension/src/content/adapters/monica/monica.ts` - Monica 主适配器

### 实现特点

1. **URL 匹配规则**
   ```typescript
   // Home页: 无 convId 参数
   home: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+\/?(\?(?!.*convId=).*)?$/i
   
   // Chat页: 有 convId 参数  
   chat: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+\/?\?.*convId=([^&]+)/i
   ```

2. **chatId 提取**
   ```typescript
   // 从 convId 查询参数提取并 URL 解码
   const convId = urlParams.get('convId');
   return decodeURIComponent(convId);
   ```

3. **剪贴板策略**
   - 直接使用 HTML 转 Markdown，不依赖复制按钮
   - 自定义 Turndown 规则处理 Monica 的代码块格式（hljs + language-xxx）

4. **选择器配置**
   - 输入框: `textarea[data-input_node="monica-chat-input"]`
   - 发送按钮: `.input-msg-btn--cT5PX`
   - 用户消息: `.chat-question--yrvtP`
   - AI 回答: `.chat-reply--ntVOt`
   - Markdown 内容: `.markdown--UqDin.__markdown`

### 后续步骤

Monica 平台的适配器实现已完成，需要进行以下测试：

1. **基础功能测试**
   - [ ] 访问 Monica 平台，确认适配器加载
   - [ ] 测试 Home 页和 Chat 页的识别
   - [ ] 测试 chatId 提取功能

2. **答案捕获测试**
   - [ ] 发送提问，观察答案监听
   - [ ] 验证答案完成检测
   - [ ] 检查答案内容提取（HTML 转 Markdown）

3. **数据存储测试**
   - [ ] 验证数据库存储
   - [ ] 检查提示词和答案关联
   - [ ] 测试多轮对话

4. **边界情况测试**
   - [ ] 测试不同模型切换（Monica、GPT-5、Claude 等）
   - [ ] 测试含代码块的答案
   - [ ] 测试长答案和复杂格式

---

## 实现总结

Monica 平台支持已完全实现，主要特点：

✅ **简化实现**: 直接使用 HTML 转 Markdown，不依赖复制按钮  
✅ **统一架构**: 完全遵循 BaseAIAdapter 架构模式  
✅ **代码复用**: 最大化利用父类功能，减少重复代码  
✅ **易于维护**: 清晰的职责划分和良好的注释文档  

下一步可以进行实际测试和调试优化。