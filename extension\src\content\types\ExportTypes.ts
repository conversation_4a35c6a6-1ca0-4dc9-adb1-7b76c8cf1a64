
/**
 * 导出参数接口
 */
export interface ExportParams {
    answerIndex: number;
    title: string;
    tags: string[];
}

/**
 * 导出数据接口
 */
export interface ExportData {
    title: string;
    question: string;
    answer: string;
    tags: string[];
    timestamp: string;
}

/**
 * 问答对接口
 */
export interface QAPair {
    prompt: string;
    answer: string;
}

/**
 * 导出错误类型
 */
export enum ExportError {
    NOT_CONFIGURED = 'NOT_CONFIGURED',
    INVALID_INDEX = 'INVALID_INDEX',
    FILE_SAVE_FAILED = 'FILE_SAVE_FAILED',
    PERMISSION_DENIED = 'PERMISSION_DENIED'
}

/**
 * 页面状态结构
 */
export interface PageState {
    isWelcomePage: boolean;
    isChatPage: boolean;
    hasTransitioned: boolean;
    chatId?: string;  // 聊天页面的聊天ID
}