import { BasePageService } from "@/content/core/BasePageService";

/**
 * Grok 页面状态管理服务
 * 继承 BasePageService，提供 Grok 平台特定的 URL 匹配规则
 */
export class GrokPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Grok';
    }

    /**
     * 获取 Grok 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://grok.com 或 https://grok.com/?...
            home: /^https:\/\/grok\.com\/?(\?.*)?$/i,
            // Chat页模式: https://grok.com/c/{chat_id}
            chat: /^https:\/\/grok\.com\/c\/([a-zA-Z0-9-]+)$/i
        };
    }

    /**
     * 检测欢迎页面（Grok 特定实现）
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（Grok 特有的欢迎页面元素）
        return document.querySelector('textarea[aria-label*="How can Grok help"]') !== null &&
               document.querySelector('[id="last-reply-container"]') === null;
    }
    
    /**
     * 检测聊天页面（Grok 特定实现）
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测（存在对话历史记录）
        return document.querySelector('[id="last-reply-container"]') !== null ||
               document.querySelector('.message-bubble') !== null;
    }
}
