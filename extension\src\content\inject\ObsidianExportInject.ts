import { ObsidianExportButton } from './components/ObsidianExportButton';
import { ObsidianExportModal } from './components/ObsidianExportModal';
import SelectorManager from '../configs/SelectorManager';
import { DOMUtils } from '../utils/DOMUtils';
import { EchoSyncEventEnum } from '../types/DOMEnum';

/**
 * Obsidian 导出注入器
 * 监听答案完成事件，在每个答案中注入导出按钮
 */
export class ObsidianExportInject {
    private buttons: Map<Element, ObsidianExportButton> = new Map();
    // private isActive: boolean = false;

    constructor() {
        this.setupEventListeners();
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        // 使用箭头函数自动绑定 this，无需手动 bind
        document.addEventListener(EchoSyncEventEnum.ANSWER_EXTRACTED, this.handleAnswerExtracted);
        console.info('【ObsidianExportInject】 事件监听器已设置');
    }

    /**
     * 处理答案提取完成事件
     * 使用箭头函数自动绑定 this 上下文
     */
    private handleAnswerExtracted = (event: Event): void => {
        // if (!this.isActive) {
        //     console.warn('【ObsidianExportInject】 收到事件但注入器未启动，忽略');
        //     return;
        // }

        const customEvent = event as CustomEvent;
        const { completionElement, answerIndex } = customEvent.detail;

        console.info('【ObsidianExportInject】 收到答案提取事件', answerIndex );
        console.info('【ObsidianExportInject】 completionElement', completionElement);

        // 注入导出按钮
        this.injectButton(completionElement, answerIndex);
    }

    /**
     * 注入导出按钮
     */
    private injectButton(completionElement: Element, answerIndex: number): void {
        try {
            const selectors = SelectorManager.getSelector();
            if (!selectors) {
                console.warn('【ObsidianExportInject】 选择器配置未找到');
                return;
            }

            // 查找 answerCompletion 组件
            // const completionElement = DOMUtils.findElementInContainer(
            //     answerElement,
            //     selectors.answerCompletion
            // );

            // if (!completionElement) {
            //     console.warn('【ObsidianExportInject】 未找到 answerCompletion 组件');
            //     return;
            // }
            // 检查是否已注入
            if (this.buttons.has(completionElement)) {
                console.info('【ObsidianExportInject】 按钮已存在，跳过注入');
                return;
            }

            // 创建按钮组件
            const button = new ObsidianExportButton(answerIndex);
            
            // 设置点击回调
            button.onClick((index) => {
                this.handleButtonClick(index);
            });

            // 渲染按钮元素
            const buttonElement = button.render();

            // 策略1：尝试查找复制按钮并插入到旁边（Kimi、ChatGPT等平台）
            const copyButton = DOMUtils.findElementInContainer(
                completionElement,
                selectors.copyButton
            );

            if (copyButton && copyButton.parentElement) {
                // 将 Obsidian 按钮插入到复制按钮旁边
                copyButton.parentElement.insertBefore(buttonElement, copyButton.nextSibling);
                console.info('【ObsidianExportInject】 导出按钮已插入到复制按钮旁边');
            } else {
                // 策略2：没有复制按钮，直接添加到 completionElement 底部（Poe等平台）
                console.info('【ObsidianExportInject】 未找到复制按钮，将按钮添加到答案区域底部');
                
                // 创建一个容器来放置按钮，保持样式一致
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = 'display: flex; justify-content: flex-end; margin-top: 8px; padding: 4px 0;';
                buttonContainer.appendChild(buttonElement);
                
                completionElement.appendChild(buttonContainer);
            }

            // 存储按钮实例
            this.buttons.set(completionElement, button);

            console.info('【ObsidianExportInject】 导出按钮注入成功', { answerIndex });

        } catch (error) {
            console.error('【ObsidianExportInject】 注入按钮失败', error);
        }
    }

    /**
     * 处理按钮点击
     */
    private handleButtonClick(answerIndex: number): void {
        console.info('【ObsidianExportInject】 按钮被点击', { answerIndex });

        try {
            // 创建并显示 Modal
            const modal = new ObsidianExportModal(answerIndex);
            modal.show();
        } catch (error) {
            console.error('【ObsidianExportInject】 显示 Modal 失败', error);
        }
    }

    /**
     * 启动注入器
     */
    // public start(): void {
    //     if (this.isActive) {
    //         console.warn('【ObsidianExportInject】 注入器已经启动');
    //         return;
    //     }

    //     this.isActive = true;
    //     console.info('【ObsidianExportInject】 注入器已启动');
    // }

    /**
     * 停止注入器
     */
    public stop(): void {
        // if (!this.isActive) {
        //     return;
        // }

        // this.isActive = false;

        // 清理所有按钮
        this.buttons.forEach((button) => {
            button.destroy();
        });
        this.buttons.clear();

        console.info('【ObsidianExportInject】 注入器已停止');
    }

    /**
     * 销毁注入器
     */
    public destroy(): void {
        this.stop();
        
        // 箭头函数可以直接移除，因为它是类的属性
        document.removeEventListener(EchoSyncEventEnum.ANSWER_EXTRACTED, this.handleAnswerExtracted);
        
        console.info('【ObsidianExportInject】 注入器已销毁');
    }
}
