# EchoSync 代码规范和命名约定

## 文件命名规范

### 1. Manager 类（管理类）

**特征**:
- ✅ 单例模式，全局唯一实例
- ✅ 管理和协调多个组件或服务
- ✅ 负责生命周期管理

**命名规则**:
- 文件名: `XxxManager.ts`
- 类名: `XxxManager`
- 导出: `export const xxxManager = XxxManager.getInstance()`

**目录位置**:
- 核心管理类: `src/content/core/`
- 工具管理类: `src/content/utils/`

**示例**:
```typescript
// src/content/core/ContentScriptManager.ts
export class ContentScriptManager extends Singleton<ContentScriptManager> {
  private answerController: BaseAnswerController | null = null;
  
  public async initialize(): Promise<void> {
    // 初始化逻辑
  }
  
  public destroy(): void {
    // 清理逻辑
  }
}

export const contentScriptManager = ContentScriptManager.getInstance();
```

```typescript
// src/content/utils/PermissionManager.ts
export class PermissionManager {
  private static instance: PermissionManager | null = null;
  
  public static getInstance(): PermissionManager {
    if (!PermissionManager.instance) {
      PermissionManager.instance = new PermissionManager();
    }
    return PermissionManager.instance;
  }
  
  private constructor() {}
}

export const permissionManager = PermissionManager.getInstance();
```

**使用场景**:
- 需要全局唯一实例
- 需要管理复杂的状态和生命周期
- 需要协调多个子组件

---

### 2. Service 类（服务类）

**特征**:
- ❌ 非单例模式，可创建多个实例
- ✅ 实现具体的业务逻辑
- ✅ 可被 Controller 管理和调用

**命名规则**:
- 文件名: `XxxService.ts`
- 类名: `XxxService`
- 导出: `export class XxxService`（不导出实例）

**目录位置**:
- 平台服务: `src/content/adapters/{platform}/`
- 通用服务: `src/common/service/`
- 基础服务: `src/content/core/`

**示例**:
```typescript
// src/content/core/BaseAnswerService.ts（基类）
export abstract class BaseAnswerService {
  protected abstract captureExistingQAPairs(): Promise<void>;
  
  public async startListeningOnChatList(): Promise<void> {
    await this.captureExistingQAPairs();
  }
}

// src/content/adapters/kimi/KimiAnswerService.ts（子类）
export class KimiAnswerService extends BaseAnswerService {
  protected async captureExistingQAPairs(): Promise<void> {
    // Kimi 特定实现
  }
}

// 使用（在 Controller 中）
const answerService = new KimiAnswerService();
```

**使用场景**:
- 实现具体的业务功能
- 需要多个实例的场景
- 继承基类实现平台适配

---

### 3. Controller 类（控制器类）

**特征**:
- ❌ 非单例模式
- ✅ 流程控制和编排
- ✅ 协调多个 Service 工作

**命名规则**:
- 文件名: `XxxController.ts`
- 类名: `XxxController`
- 导出: `export class XxxController`

**目录位置**:
- `src/content/adapters/{platform}/`
- `src/content/core/`

**示例**:
```typescript
// src/content/core/BaseAnswerController.ts
export abstract class BaseAnswerController {
  protected answerService: BaseAnswerService;
  protected clipboardService: BaseClipboardService;
  
  constructor(
    answerService: BaseAnswerService,
    clipboardService: BaseClipboardService
  ) {
    this.answerService = answerService;
    this.clipboardService = clipboardService;
  }
  
  public async startChatListeners(): Promise<void> {
    // 协调多个 Service
    await this.answerService.startListeningOnChatList();
  }
}

// src/content/adapters/kimi/KimiAnswerController.ts
export class KimiAnswerController extends BaseAnswerController {
  constructor() {
    super(
      new KimiAnswerService(),
      new KimiClipboardService()
    );
  }
}
```

**使用场景**:
- 需要协调多个 Service
- 实现复杂的业务流程
- 分离流程控制和业务逻辑

---

### 4. Utils 类（工具类）

**特征**:
- ✅ 静态工具方法
- ✅ 无状态或单例状态
- ✅ 提供通用辅助功能

**命名规则**:
- 文件名: `XxxUtils.ts` 或 `XxxManager.ts`（如果是单例工具）
- 类名: `XxxUtils` 或 `XxxManager`
- 导出: `export class XxxUtils` 或 `export const xxxManager`

**目录位置**:
- `src/content/utils/`
- `src/common/utils/`

**示例**:
```typescript
// src/content/utils/DOMUtils.ts（静态工具）
export class DOMUtils {
  /**
   * 等待页面加载完成
   */
  static async waitForPageLoad(): Promise<void> {
    // 实现
  }
  
  /**
   * 查找元素
   */
  static findElementInContainer(
    container: Element,
    selectors: string[]
  ): Element | null {
    // 实现
  }
}

// 使用
await DOMUtils.waitForPageLoad();
const element = DOMUtils.findElementInContainer(container, selectors);
```

```typescript
// src/content/utils/PermissionManager.ts（单例工具）
export class PermissionManager {
  private static instance: PermissionManager | null = null;
  
  public static getInstance(): PermissionManager {
    // 单例实现
  }
  
  public async requestClipboardPermission(): Promise<boolean> {
    // 权限管理逻辑
  }
}

export const permissionManager = PermissionManager.getInstance();
```

**使用场景**:
- DOM 操作工具
- 字符串处理工具
- 权限管理
- 存储管理

---

### 5. Inject 类（注入组件）

**特征**:
- ✅ 特殊的 Service
- ✅ 负责 UI 注入
- ✅ 管理注入的 DOM 元素

**命名规则**:
- 文件名: `XxxInject.ts`
- 类名: `XxxInject`
- 导出: `export class XxxInject`

**目录位置**:
- `src/content/inject/`

**示例**:
```typescript
// src/content/inject/FloatingBubbleInject.ts
export class FloatingBubbleInject {
  private bubbleElement: HTMLElement | null = null;
  
  /**
   * 注入悬浮气泡
   */
  public inject(): void {
    if (this.bubbleElement) {
      console.warn('[FloatingBubbleInject] 气泡已存在');
      return;
    }
    
    this.bubbleElement = this.createBubble();
    document.body.appendChild(this.bubbleElement);
  }
  
  /**
   * 移除气泡
   */
  public remove(): void {
    if (this.bubbleElement) {
      this.bubbleElement.remove();
      this.bubbleElement = null;
    }
  }
  
  private createBubble(): HTMLElement {
    const bubble = document.createElement('div');
    bubble.id = 'echosync-floating-bubble';
    // 设置样式和内容
    return bubble;
  }
}
```

**使用场景**:
- 注入悬浮气泡
- 注入模态框
- 注入按钮或菜单

---

### 6. Model 类（数据模型）

**特征**:
- ✅ 数据结构定义
- ✅ 使用 `interface` 或 `type`
- ✅ 描述业务数据

**命名规则**:
- 文件名: `XxxModel.ts`
- 接口名: `XxxModel`
- 导出: `export interface XxxModel`

**目录位置**:
- `src/content/model/`
- `src/common/model/`

**示例**:
```typescript
// src/content/model/AnswerModel.ts
export interface AnswerModel {
  id: string;
  question: string;
  answer: string;
  timestamp: number;
  platform: 'kimi' | 'chatgpt' | 'claude';
}

export interface QAPairModel {
  questionElement: Element;
  answerElement: Element;
  content: AnswerModel;
}
```

**使用场景**:
- 定义答案数据结构
- 定义配置数据结构
- 定义消息传递数据结构

---

### 7. Config 类（配置类）

**特征**:
- ✅ 配置数据管理
- ✅ 选择器配置
- ✅ 常量定义

**命名规则**:
- 文件名: `XxxConfig.ts` 或 `SelectorManager.ts`
- 类名: `XxxConfig` 或 `SelectorManager`
- 导出: `export default class XxxConfig`

**目录位置**:
- `src/content/configs/`

**示例**:
```typescript
// src/content/configs/SelectorManager.ts
interface SelectorConfig {
  chatList: string[];
  answerItem: string[];
  markdown: string[];
}

interface PlatformSelectors {
  kimi: SelectorConfig;
  chatgpt: SelectorConfig;
}

export default class SelectorManager {
  private static selectors: PlatformSelectors = {
    kimi: {
      chatList: ['.chat-list', '[data-chat-list]'],
      answerItem: ['.answer-item', '[data-answer]'],
      markdown: ['.markdown-body', '[data-markdown]']
    }
  };
  
  public static getSelector(): SelectorConfig {
    const hostname = window.location.hostname;
    if (hostname.includes('kimi.moonshot.cn')) {
      return this.selectors.kimi;
    }
    throw new Error(`Unsupported platform: ${hostname}`);
  }
}
```

**使用场景**:
- 平台选择器配置
- 应用配置常量
- 主题配置

---

## 目录结构规范

```
src/content/
├── adapters/              # 平台适配器
│   ├── kimi/             # Kimi 平台
│   │   ├── KimiAnswerService.ts
│   │   ├── KimiAnswerController.ts
│   │   └── KimiClipboardService.ts
│   └── chatgpt/          # ChatGPT 平台（未来）
│
├── configs/              # 配置文件
│   └── SelectorManager.ts
│
├── core/                 # 核心基础类
│   ├── BaseAnswerService.ts
│   ├── BaseAnswerController.ts
│   ├── BaseClipboardService.ts
│   └── ContentScriptManager.ts
│
├── inject/               # 注入组件
│   ├── FloatingBubbleInject.ts
│   └── ObsidianExportModal.tsx
│
├── model/                # 数据模型
│   └── AnswerModel.ts
│
├── utils/                # 工具类
│   ├── DOMUtils.ts
│   └── PermissionManager.ts
│
└── index.ts              # Content Script 入口
```

---

## 继承关系规范

### Service 继承链
```
BaseAnswerService (抽象基类)
└── KimiAnswerService (Kimi 实现)
└── ChatGPTAnswerService (ChatGPT 实现，未来)

BaseClipboardService (抽象基类)
└── KimiClipboardService (Kimi 实现)
└── ChatGPTClipboardService (ChatGPT 实现，未来)
```

### Controller 继承链
```
BaseAnswerController (抽象基类)
└── KimiAnswerController (Kimi 实现)
└── ChatGPTAnswerController (ChatGPT 实现，未来)
```

---

## 日志规范

**格式**: `[类名] 日志内容`

**级别**:
- `console.info()`: 正常流程信息
- `console.warn()`: 警告信息
- `console.error()`: 错误信息
- `console.log()`: 调试信息（生产环境移除）

**示例**:
```typescript
console.info('[KimiAnswerService] 开始监听聊天列表')
console.warn('[PermissionManager] ⚠️ 剪贴板权限未授予')
console.error('[FileSystemService] 文件写入失败', error)
```

**emoji 使用**:
- ✅ 成功: `console.info('[Manager] ✅ 操作成功')`
- ❌ 失败: `console.error('[Manager] ❌ 操作失败')`
- ⚠️ 警告: `console.warn('[Manager] ⚠️ 警告信息')`
- ⏳ 等待: `console.info('[Manager] ⏳ 等待用户操作')`

---

## 注释规范

**类注释**:
```typescript
/**
 * Kimi 答案捕获服务
 * 继承 BaseAnswerService，实现 Kimi 平台特定的答案捕获逻辑
 * 
 * 主要功能：
 * - 监听聊天列表变化
 * - 捕获问答对
 * - 提取答案内容
 */
export class KimiAnswerService extends BaseAnswerService {
  // ...
}
```

**方法注释**:
```typescript
/**
 * 提取答案内容
 * @param answerElement 答案元素
 * @param copyButton 复制按钮
 * @returns 答案的 markdown 内容
 */
protected async extractAnswerContent(
  answerElement: Element,
  copyButton: Element
): Promise<string> {
  // ...
}
```

**重要逻辑注释**:
```typescript
// 1. 模拟点击复制按钮
if (copyButton && copyButton instanceof HTMLElement) {
  // 确保窗口获得焦点
  window.focus();
  
  // 点击复制按钮
  copyButton.click();
  
  // 等待剪贴板更新
  await BaseClipboardService.delay(200);
}
```

---

## 导入顺序规范

```typescript
// 1. 第三方库
import TurndownService from 'turndown';

// 2. 基类/核心类（使用 @ 别名）
import { BaseClipboardService } from '@/content/core/BaseClipboardService';

// 3. 工具类（相对路径）
import SelectorManager from '../../configs/SelectorManager';
import { DOMUtils } from '../../utils/DOMUtils';

// 4. 类型定义
import type { AnswerModel } from '../../model/AnswerModel';
```

---

## 错误处理规范

```typescript
try {
  // 业务逻辑
  await someOperation();
} catch (error) {
  // 日志输出
  console.error('[ServiceName] 操作失败', error);
  
  // 降级方案
  return await fallbackOperation();
}
```

---

## 总结

遵循这些命名规范和代码风格，可以：
- ✅ 让 Copilot 更准确地理解项目结构
- ✅ 提高代码的可读性和可维护性
- ✅ 统一团队的代码风格
- ✅ 便于新成员快速上手

**记住核心原则**：
- **Manager = 单例管理类**
- **Service = 非单例业务类**
- **Controller = 流程控制类**
- **Utils = 工具类**
- **Inject = 注入组件**
- **Model = 数据模型**
- **Config = 配置类**
