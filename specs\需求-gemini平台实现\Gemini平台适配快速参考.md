# Gemini 平台适配快速参考

## 📁 文件结构

```
extension/src/content/
├── configs/
│   └── geminiConfig.ts           # Gemini 选择器配置
└── adapters/
    ├── gemini.ts                  # 主适配器
    └── gemini/
        ├── GeminiAnswerController.ts    # 答案控制器
        ├── GeminiAnswerService.ts       # 答案服务
        ├── GeminiPageService.ts         # 页面服务
        └── GeminiClipboardService.ts    # 剪贴板服务
```

## 🔑 关键选择器

```typescript
// 输入框
'rich-textarea .ql-editor[contenteditable="true"]'

// 发送按钮
'button[aria-label="发送"]'
'button:has(mat-icon[fonticon="send"])'

// 用户提问
'user-query'
'user-query-content .query-text'

// AI回答
'model-response'
'message-content .markdown'

// 答案完成标记
'model-response:has(message-actions)'

// 复制按钮
'copy-button > button[aria-label="复制"]'
```

## 🌐 URL 模式

```typescript
// Home 页
https://gemini.google.com/app

// Chat 页
https://gemini.google.com/app/{chatId}
```

## 🎯 核心功能

### 1. 输入捕获
- 监听 Quill 编辑器的 `rich-textarea` 组件
- 捕获用户输入内容

### 2. 答案监听
- 监听 `model-response` 元素的 DOM 变化
- 通过 `message-actions` 组件判断答案完成

### 3. 内容提取
- 优先使用复制按钮
- 降级方案: 从 `message-content .markdown` 提取
- 特殊处理 KaTeX 数学公式

### 4. 页面检测
- URL 匹配判断页面类型
- DOM 降级检测 (检查 `model-response` 是否存在)

## 🔧 特殊处理

### KaTeX 数学公式
```typescript
// 识别公式
node.className.includes('katex')

// 提取原始公式
node.getAttribute('data-math')

// 转换为 Markdown
`$${mathContent}$`
```

### 代码块
```typescript
// 识别代码块
node.nodeName === 'CODE' && /language-/.test(node.className)

// 提取语言
const lang = node.className.match(/language-([\w-]+)/)[1]

// 转换为 Markdown
`\n\`\`\`${lang}\n${code}\n\`\`\`\n`
```

## 🐛 调试技巧

### 1. 检查适配器初始化
```javascript
// 在控制台查看
console.log('GeminiAdapter initialized:', window.__geminiAdapter)
```

### 2. 检查选择器是否匹配
```javascript
// 测试输入框选择器
document.querySelector('rich-textarea .ql-editor[contenteditable="true"]')

// 测试答案选择器
document.querySelectorAll('model-response')
```

### 3. 监听答案更新
```javascript
// 观察 MutationObserver 日志
// 查找 [GeminiAnswerService] 相关日志
```

## ⚠️ 已知限制

1. **Shadow DOM**
   - Gemini 可能使用 Shadow DOM
   - 需要测试确认并可能需要特殊处理

2. **图片回答**
   - 当前未特殊处理图片
   - 如需支持需要额外实现

3. **流式输出**
   - 需要测试流式输出的准确性
   - 可能需要调整完成检测逻辑

## 📊 测试清单

- [ ] 在 Home 页输入问题并发送
- [ ] 验证问题是否被正确捕获
- [ ] 验证答案是否被实时监听
- [ ] 验证答案完成是否准确检测
- [ ] 测试复制按钮功能
- [ ] 测试含代码块的答案
- [ ] 测试含数学公式的答案
- [ ] 测试多轮对话
- [ ] 验证浮动气泡UI
- [ ] 验证存档按钮UI
- [ ] 测试页面切换 (Home ↔ Chat)

## 🚀 快速启动

1. 加载扩展
2. 访问 `https://gemini.google.com/app`
3. 打开浏览器控制台
4. 查看初始化日志: `【GeminiAdapter】GeminiAdapter initialized`
5. 开始对话并观察日志
