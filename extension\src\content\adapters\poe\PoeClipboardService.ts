import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * 创建Poe专用的Turndown实例
 * 配置特殊规则以适配Poe的HTML结构
 */
const poeTurndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  hr: '---',
  bulletListMarker: '-'
});

/**
 * Poe代码块规则
 * Poe使用 MarkdownCodeBlock_container 容器（使用模糊匹配避免hash变化）
 * 内部结构: MarkdownCodeBlock_preTag > code.language-xxx
 */
poeTurndownService.addRule('poeCodeBlock', {
  filter: (node) => {
    // 检测Poe的代码块容器 - 使用模糊匹配
    return (
      node.nodeName === 'DIV' &&
      Array.from(node.classList).some((cls: string) => cls.startsWith('MarkdownCodeBlock_container'))
    );
  },
  replacement: (content, node) => {
    // 查找语言标识 - 使用模糊匹配
    const languageDiv = node.querySelector('[class*="MarkdownCodeBlock_languageName"]');
    const lang = languageDiv?.textContent?.trim() || '';
    
    // 查找代码内容 - 使用模糊匹配
    const codeElement = node.querySelector('[class*="MarkdownCodeBlock_preTag"] code');
    const code = codeElement?.textContent || '';
    
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

/**
 * Poe标准代码块规则（备用）
 * 处理标准的 <pre><code> 结构
 */
poeTurndownService.addRule('poeStandardCode', {
  filter: (node) => {
    return (
      node.nodeName === 'CODE' &&
      /language-/.test(node.className)
    );
  },
  replacement: (content, node) => {
    const code = node.textContent || '';
    const match = node.className.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

/**
 * Poe Prose容器规则
 * Poe使用 Prose_prose 和 Markdown_markdownContainer 作为内容容器（使用模糊匹配）
 */
poeTurndownService.addRule('poeProse', {
  filter: (node) => {
    return (
      node.nodeName === 'DIV' &&
      Array.from(node.classList).some((cls: string) => 
        cls.startsWith('Prose_prose') || cls.startsWith('Markdown_markdownContainer')
      )
    );
  },
  replacement: (content) => {
    // 保持内容，只是标记容器
    return content;
  }
});

/**
 * Poe 表格规则
 * Poe 使用标准的 HTML table 结构
 * 转换为 Markdown 表格格式
 */
poeTurndownService.addRule('poeTable', {
  filter: 'table',
  replacement: (content, node) => {
    const table = node as HTMLTableElement;
    
    // 提取表头
    const thead = table.querySelector('thead');
    const headerRows = thead ? Array.from(thead.querySelectorAll('tr')) : [];
    
    // 提取表体
    const tbody = table.querySelector('tbody');
    const bodyRows = tbody ? Array.from(tbody.querySelectorAll('tr')) : [];
    
    if (headerRows.length === 0 && bodyRows.length === 0) {
      return content;
    }
    
    let markdown = '\n';
    
    // 处理表头
    if (headerRows.length > 0) {
      const headerRow = headerRows[0];
      const headers = Array.from(headerRow.querySelectorAll('th, td'))
        .map(cell => cell.textContent?.trim() || '');
      
      // 表头行
      markdown += '| ' + headers.join(' | ') + ' |\n';
      
      // 分隔行
      markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';
    }
    
    // 处理表体
    for (const row of bodyRows) {
      const cells = Array.from(row.querySelectorAll('td, th'))
        .map(cell => cell.textContent?.trim() || '');
      
      markdown += '| ' + cells.join(' | ') + ' |\n';
    }
    
    markdown += '\n';
    return markdown;
  }
});

/**
 * Poe 剪贴板管理服务
 * 
 * ⚠️ 特殊说明：
 * Poe平台没有明显的复制按钮，因此必须使用HTML解析方案
 * - 不使用 simulateClickAndGetContent 方法
 * - 直接从DOM提取HTML并转换为Markdown
 * - 使用定制的TurndownService规则处理Poe特有的HTML结构
 */
export class PoeClipboardService extends BaseClipboardService {
  
  /**
   * 直接提取答案内容（Poe专用方法）
   * ⚠️ 因为Poe没有复制按钮，这是主要的内容提取方法
   * @param answerElement 答案元素
   * @returns Markdown格式的答案内容
   */
  public async extractAnswerContent(answerElement: Element): Promise<string> {
    try {
      console.info('[PoeClipboardService] 开始提取Poe答案内容（HTML解析模式）');
      
      // 直接使用降级方案（对Poe来说这是主方案）
      const content = await this.getFallbackClipboardContent(answerElement);
      
      if (content && content.trim()) {
        console.info('[PoeClipboardService] ✅ 内容提取成功，长度:', content.length);
      } else {
        console.warn('[PoeClipboardService] ⚠️ 未提取到内容');
      }
      
      return content;
      
    } catch (error) {
      console.error('[PoeClipboardService] 内容提取失败', error);
      return '';
    }
  }

  /**
   * 覆盖：从DOM提取内容（Poe的主要实现）
   * 使用Poe专用的TurndownService配置
   * @param answerElement 答案元素
   * @returns Markdown格式的内容
   */
  protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    try {
      const selectors = SelectorManager.getSelector();
      
      // 1. 尝试查找左侧消息气泡（AI答案在左侧）- 使用模糊匹配
      let contentContainer = DOMUtils.findElementInContainer(
        answerElement,
        ['[class*="Message_leftSideMessageBubble"]', '[class*="Message_messageBubbleWrapper"]']
      );

      // 2. 如果没找到气泡容器，尝试查找Markdown容器
      if (!contentContainer) {
        contentContainer = DOMUtils.findElementInContainer(
          answerElement,
          selectors.markdown || []
        );
      }

      // 3. 如果还是没找到，使用答案元素本身
      if (!contentContainer) {
        console.warn('[PoeClipboardService] 未找到内容容器，使用答案元素本身');
        contentContainer = answerElement;
      }

      // 4. 查找实际的内容节点（Markdown容器或Prose容器）- 使用模糊匹配
      const markdownNodes = DOMUtils.findElementAllInContainer(
        contentContainer,
        selectors.markdown || [
          '[class*="Markdown_markdownContainer"]',
          '[class*="Prose_prose"]'
        ]
      );

      if (markdownNodes.length === 0) {
        console.warn('[PoeClipboardService] 未找到Markdown节点');
        // 尝试直接使用内容容器
        return this.convertToMarkdown(contentContainer);
      }

      // 5. 如果有多个Markdown节点，合并它们
      if (markdownNodes.length > 1) {
        console.info(`[PoeClipboardService] 找到 ${markdownNodes.length} 个Markdown节点，进行合并`);
        const contents = markdownNodes.map(node => 
          this.convertToMarkdown(node)
        );
        return contents.join('\n\n').trim();
      }

      // 6. 单个Markdown节点
      return this.convertToMarkdown(markdownNodes[0]);

    } catch (error) {
      console.error('[PoeClipboardService] DOM提取失败', error);
      throw error;
    }
  }

  /**
   * 将HTML元素转换为Markdown
   * @param element HTML元素
   * @returns Markdown字符串
   */
  private convertToMarkdown(element: Element): string {
    try {
      const html = element.innerHTML;
      
      if (!html || !html.trim()) {
        console.warn('[PoeClipboardService] HTML内容为空');
        return '';
      }

      // 使用Poe专用的Turndown服务
      let markdown = poeTurndownService.turndown(html);
      
      // 清理多余的空行
      markdown = markdown
        .replace(/\n{3,}/g, '\n\n')  // 多个连续空行替换为两个
        .trim();

      console.info('[PoeClipboardService] HTML转Markdown完成', {
        htmlLength: html.length,
        markdownLength: markdown.length,
        preview: markdown.slice(0, 100)
      });

      return markdown;

    } catch (error) {
      console.error('[PoeClipboardService] HTML转Markdown失败', error);
      // 降级：返回纯文本
      return element.textContent?.trim() || '';
    }
  }

  /**
   * 覆盖：Poe不使用复制按钮方式
   * 这个方法在Poe中不会被调用，但为了类型兼容性需要覆盖
   * @param answerElement 答案元素
   * @param copyButton 复制按钮（Poe中不存在）
   * @returns 答案内容
   */
  public async simulateClickAndGetContent(
    answerElement: Element, 
    copyButton: Element
  ): Promise<string> {
    console.warn('[PoeClipboardService] Poe平台不支持复制按钮方式，使用HTML解析');
    return this.extractAnswerContent(answerElement);
  }

  /**
   * 覆盖：Poe不需要剪贴板API
   * @param answerElement 答案元素
   * @returns 空字符串（不使用剪贴板）
   */
  protected async getClipboardContent(answerElement: Element): Promise<string> {
    console.info('[PoeClipboardService] Poe不使用剪贴板API，直接返回空');
    return '';
  }
}
