import React, { useState } from 'react'

export function SupportPage() {
  const [feedbackType, setFeedbackType] = useState('bug')
  const [description, setDescription] = useState('')
  const [email, setEmail] = useState('')

  const feedbackTypes = [
    { value: 'bug', label: 'Bug报告' },
    { value: 'feature', label: '功能建议' },
    { value: 'usage', label: '使用问题' },
    { value: 'other', label: '其他' }
  ]

  const helpLinks = [
    '如何开始使用EchoSync？',
    '如何导出对话记录？',
    '多设备同步设置',
    '查看完整帮助文档'
  ]

  const handleSubmitFeedback = () => {
    // TODO: Implement feedback submission
    console.log('Submitting feedback:', { feedbackType, description, email })
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 联系我们 */}
      <div className="card fade-in">
        <div className="card-title">📧 联系我们</div>
        <div className="card-content">
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: 600, marginBottom: '4px' }}>客服邮箱</div>
            <div style={{ fontSize: '14px', color: 'var(--blue-600)' }}><EMAIL></div>
          </div>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontWeight: 600, marginBottom: '4px' }}>工作时间</div>
            <div style={{ fontSize: '14px', color: 'var(--text-muted)' }}>
              周一至周五 9:00-18:00 (UTC+8)
            </div>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button className="btn btn-primary">发送邮件</button>
            <button className="btn btn-secondary">查看帮助文档</button>
          </div>
        </div>
      </div>

      {/* 留言反馈 */}
      <div className="card fade-in">
        <div className="card-title">💬 留言反馈</div>
        <div className="card-content">
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'block', fontWeight: 600, marginBottom: '4px' }}>
              反馈类型
            </label>
            <select
              value={feedbackType}
              onChange={(e) => setFeedbackType(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid rgba(15, 23, 42, 0.12)',
                borderRadius: '8px',
                fontSize: '14px'
              }}
            >
              {feedbackTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'block', fontWeight: 600, marginBottom: '4px' }}>
              详细描述
            </label>
            <textarea
              placeholder="请详细描述您遇到的问题或建议..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              style={{
                width: '100%',
                height: '80px',
                padding: '8px',
                border: '1px solid rgba(15, 23, 42, 0.12)',
                borderRadius: '8px',
                fontSize: '14px',
                resize: 'vertical'
              }}
            />
          </div>
          
          <div style={{ marginBottom: '12px' }}>
            <label style={{ display: 'block', fontWeight: 600, marginBottom: '4px' }}>
              联系邮箱
            </label>
            <input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid rgba(15, 23, 42, 0.12)',
                borderRadius: '8px',
                fontSize: '14px'
              }}
            />
          </div>
          
          <button 
            className="btn btn-primary" 
            style={{ width: '100%' }}
            onClick={handleSubmitFeedback}
          >
            提交反馈
          </button>
        </div>
      </div>

      {/* 快速帮助 */}
      <div className="card fade-in">
        <div className="card-title">📚 快速帮助</div>
        <div className="card-content">
          {helpLinks.map((link, index) => (
            <div key={index} style={{ marginBottom: index < helpLinks.length - 1 ? '8px' : '0' }}>
              <a
                href="#"
                style={{
                  display: 'block',
                  padding: '8px 0',
                  color: 'var(--blue-600)',
                  textDecoration: 'none',
                  borderBottom: index < helpLinks.length - 1 ? '1px solid rgba(15, 23, 42, 0.06)' : 'none'
                }}
                onClick={(e) => {
                  e.preventDefault()
                  // TODO: Handle help link click
                  console.log('Help link clicked:', link)
                }}
              >
                {link}
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
