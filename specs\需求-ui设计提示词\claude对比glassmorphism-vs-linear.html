<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视觉方案对比：玻璃拟态 vs Linear现代风</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: #F5F5F7;
        }

        /* ============================================================ */
        /* 顶部导航 */
        /* ============================================================ */
        .comparison-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid #E5E7EB;
            z-index: 1000;
            padding: 16px 0;
        }

        .nav-inner {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .nav-buttons {
            display: flex;
            gap: 12px;
        }

        .nav-btn {
            padding: 8px 16px;
            border: 1px solid #E5E7EB;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 150ms ease;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-btn:hover {
            border-color: #3B82F6;
            color: #3B82F6;
        }

        /* ============================================================ */
        /* 对比容器 */
        /* ============================================================ */
        .comparison-container {
            padding-top: 80px;
            max-width: 1400px;
            margin: 0 auto;
            padding-left: 32px;
            padding-right: 32px;
        }

        .version-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
            margin-bottom: 64px;
        }

        .version-section {
            min-height: 100vh;
        }

        .version-header {
            text-align: center;
            padding: 48px 0 32px;
            position: sticky;
            top: 64px;
            background: #F5F5F7;
            z-index: 10;
            margin-bottom: 24px;
        }

        .version-label {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }

        .label-old {
            background: #FEE2E2;
            color: #DC2626;
        }

        .label-new {
            background: #DBEAFE;
            color: #2563EB;
        }

        .version-title {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .version-desc {
            font-size: 15px;
            color: #6B7280;
            max-width: 500px;
            margin: 0 auto;
        }

        /* ============================================================ */
        /* 旧版本：玻璃拟态风 */
        /* ============================================================ */
        .old-version {
            background: linear-gradient(135deg, #667EEA 0%, #764BA2 50%, #F093FB 100%);
            border-radius: 16px;
            padding: 48px 32px;
            position: relative;
            overflow: hidden;
        }

        .old-version::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-50px, 50px) scale(1.1); }
        }

        .old-version .pricing-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .old-version .pricing-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 28px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .old-version .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 600ms;
        }

        .old-version .pricing-card:hover::before {
            left: 100%;
        }

        .old-version .pricing-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .old-version .card-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            margin-bottom: 12px;
            background: rgba(255,255,255,0.25);
            color: white;
            backdrop-filter: blur(10px);
        }

        .old-version .card-title {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 8px;
            color: white;
        }

        .old-version .card-price {
            font-size: 44px;
            font-weight: 800;
            color: white;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            gap: 6px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .old-version .card-price .currency {
            font-size: 22px;
            opacity: 0.8;
        }

        .old-version .card-period {
            color: rgba(255,255,255,0.8);
            font-size: 13px;
            margin-bottom: 20px;
        }

        .old-version .card-features {
            list-style: none;
            margin-bottom: 24px;
        }

        .old-version .card-features li {
            padding: 10px 0;
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0.95;
        }

        .old-version .feature-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        .old-version .card-cta {
            width: 100%;
            padding: 14px;
            border-radius: 10px;
            border: none;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            color: #667EEA;
        }

        .old-version .card-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 24px rgba(255,255,255,0.3);
        }

        /* ============================================================ */
        /* 新版本：Linear 现代极简风 */
        /* ============================================================ */
        .new-version {
            background: linear-gradient(180deg, #FAFBFC 0%, #F5F7FA 100%);
            border-radius: 16px;
            padding: 48px 32px;
            border: 1px solid #E5E7EB;
        }

        .new-version .pricing-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .new-version .pricing-card {
            background: white;
            border-radius: 12px;
            padding: 32px;
            border: 1.5px solid #E5E7EB;
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        /* Linear特色：顶部彩色边框 */
        .new-version .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3B82F6, #8B5CF6);
            opacity: 0;
            transition: opacity 200ms;
        }

        .new-version .pricing-card:hover::before {
            opacity: 1;
        }

        .new-version .pricing-card:hover {
            border-color: #3B82F6;
            transform: translateY(-4px);
            box-shadow: 
                0 0 0 1px #3B82F6,
                0 12px 24px -8px rgba(59, 130, 246, 0.15),
                0 24px 48px -8px rgba(0, 0, 0, 0.08);
        }

        /* Popular标记 */
        .new-version .pricing-card.popular {
            border-color: #3B82F6;
            background: linear-gradient(180deg, #FAFBFF 0%, #FFFFFF 100%);
        }

        .new-version .pricing-card.popular::before {
            opacity: 1;
        }

        .new-version .pricing-card.popular::after {
            content: 'POPULAR';
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 10px;
            font-weight: 700;
            letter-spacing: 0.5px;
            background: linear-gradient(135deg, #3B82F6, #8B5CF6);
            color: white;
        }

        .new-version .card-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
            border: 1px solid;
        }

        .new-version .badge-free {
            background: #F9FAFB;
            color: #6B7280;
            border-color: #E5E7EB;
        }

        .new-version .badge-pro {
            background: linear-gradient(135deg, #EFF6FF, #DBEAFE);
            color: #2563EB;
            border-color: #93C5FD;
        }

        .new-version .badge-plus {
            background: linear-gradient(135deg, #F5F3FF, #EDE9FE);
            color: #7C3AED;
            border-color: #C4B5FD;
        }

        .new-version .badge-max {
            background: linear-gradient(135deg, #FDF4FF, #FCE7F3);
            color: #C026D3;
            border-color: #F0ABFC;
        }

        .new-version .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #111827;
            letter-spacing: -0.02em;
        }

        .new-version .card-price {
            font-size: 48px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            gap: 8px;
            letter-spacing: -0.03em;
        }

        .new-version .card-price .currency {
            font-size: 24px;
            color: #6B7280;
            font-weight: 600;
        }

        .new-version .card-period {
            color: #6B7280;
            font-size: 14px;
            margin-bottom: 24px;
            font-weight: 500;
        }

        .new-version .card-features {
            list-style: none;
            margin-bottom: 28px;
        }

        .new-version .card-features li {
            padding: 12px 0;
            color: #374151;
            font-size: 14px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            line-height: 1.5;
        }

        .new-version .feature-icon {
            width: 18px;
            height: 18px;
            border-radius: 4px;
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        /* Linear风格按钮 */
        .new-version .card-cta {
            width: 100%;
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            letter-spacing: -0.01em;
        }

        .new-version .cta-primary {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .new-version .cta-primary:hover {
            box-shadow: 
                0 0 0 3px rgba(59, 130, 246, 0.1),
                0 4px 12px rgba(59, 130, 246, 0.3);
            transform: translateY(-1px);
        }

        .new-version .cta-primary:active {
            transform: translateY(0);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .new-version .cta-secondary {
            background: white;
            color: #374151;
            border: 1.5px solid #E5E7EB;
        }

        .new-version .cta-secondary:hover {
            background: #F9FAFB;
            border-color: #D1D5DB;
            transform: translateY(-1px);
        }

        /* 特殊卡片：Plus（紫色强调） */
        .new-version .pricing-card.highlight-purple::before {
            background: linear-gradient(90deg, #8B5CF6, #C026D3);
        }

        .new-version .pricing-card.highlight-purple:hover {
            border-color: #8B5CF6;
            box-shadow: 
                0 0 0 1px #8B5CF6,
                0 12px 24px -8px rgba(139, 92, 246, 0.15),
                0 24px 48px -8px rgba(0, 0, 0, 0.08);
        }

        /* 特殊卡片：Max（粉色强调） */
        .new-version .pricing-card.highlight-pink::before {
            background: linear-gradient(90deg, #EC4899, #C026D3);
        }

        .new-version .pricing-card.highlight-pink:hover {
            border-color: #EC4899;
            box-shadow: 
                0 0 0 1px #EC4899,
                0 12px 24px -8px rgba(236, 72, 153, 0.15),
                0 24px 48px -8px rgba(0, 0, 0, 0.08);
        }

        .new-version .pricing-card.highlight-pink .cta-primary {
            background: linear-gradient(135deg, #EC4899, #C026D3);
        }

        .new-version .pricing-card.highlight-pink .cta-primary:hover {
            box-shadow: 
                0 0 0 3px rgba(236, 72, 153, 0.1),
                0 4px 12px rgba(236, 72, 153, 0.3);
        }

        /* ============================================================ */
        /* 优缺点对比卡片 */
        /* ============================================================ */
        .pros-cons-section {
            max-width: 1400px;
            margin: 64px auto;
            padding: 0 32px;
        }

        .pros-cons-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
        }

        .pros-cons-card {
            background: white;
            border-radius: 12px;
            padding: 32px;
            border: 1.5px solid #E5E7EB;
        }

        .pros-cons-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 24px;
            color: #111827;
        }

        .pros-cons-list {
            list-style: none;
        }

        .pros-cons-list li {
            padding: 12px 0;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
        }

        .pros-icon {
            color: #10B981;
            font-weight: bold;
            flex-shrink: 0;
        }

        .cons-icon {
            color: #EF4444;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* ============================================================ */
        /* 响应式 */
        /* ============================================================ */
        @media (max-width: 1200px) {
            .version-grid {
                grid-template-columns: 1fr;
                gap: 64px;
            }

            .old-version .pricing-grid,
            .new-version .pricing-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .old-version .pricing-grid,
            .new-version .pricing-grid {
                grid-template-columns: 1fr;
            }

            .pros-cons-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <nav class="comparison-nav">
        <div class="nav-inner">
            <div class="nav-title">🎨 视觉方案优化对比</div>
            <div class="nav-buttons">
                <button class="nav-btn" onclick="scrollToSection('old')">旧版方案</button>
                <button class="nav-btn" onclick="scrollToSection('new')">新版方案</button>
                <button class="nav-btn" onclick="scrollToSection('comparison')">对比分析</button>
            </div>
        </div>
    </nav>

    <!-- 对比容器 -->
    <div class="comparison-container">
        <div class="version-grid">
            <!-- ============================================================ -->
            <!-- 旧版本：玻璃拟态风 -->
            <!-- ============================================================ -->
            <div class="version-section" id="old">
                <div class="version-header">
                    <span class="version-label label-old">旧版本</span>
                    <h2 class="version-title">玻璃拟态风格</h2>
                    <p class="version-desc">炫目但易疲劳，透明度影响可读性</p>
                </div>

                <div class="old-version">
                    <div class="pricing-grid">
                        <!-- Free -->
                        <div class="pricing-card">
                            <span class="card-badge">Free</span>
                            <h3 class="card-title">免费版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>0</span>
                            </div>
                            <p class="card-period">永久免费</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 每日10次AI对话</li>
                                <li><span class="feature-icon">✓</span> 基础内容导出</li>
                                <li><span class="feature-icon">✓</span> 7天历史记录</li>
                                <li><span class="feature-icon">✓</span> 社区支持</li>
                            </ul>
                            <button class="card-cta">开始使用</button>
                        </div>

                        <!-- Pro -->
                        <div class="pricing-card">
                            <span class="card-badge">Pro</span>
                            <h3 class="card-title">专业版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>29</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 每日100次AI对话</li>
                                <li><span class="feature-icon">✓</span> Markdown导出</li>
                                <li><span class="feature-icon">✓</span> 30天历史记录</li>
                                <li><span class="feature-icon">✓</span> 优先响应支持</li>
                                <li><span class="feature-icon">✓</span> 自定义提示词</li>
                            </ul>
                            <button class="card-cta">立即升级</button>
                        </div>

                        <!-- Plus -->
                        <div class="pricing-card">
                            <span class="card-badge">Plus</span>
                            <h3 class="card-title">增强版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>69</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 无限AI对话</li>
                                <li><span class="feature-icon">✓</span> 全格式导出</li>
                                <li><span class="feature-icon">✓</span> 无限历史记录</li>
                                <li><span class="feature-icon">✓</span> 1对1专属支持</li>
                                <li><span class="feature-icon">✓</span> API访问权限</li>
                            </ul>
                            <button class="card-cta">立即升级</button>
                        </div>

                        <!-- Max -->
                        <div class="pricing-card">
                            <span class="card-badge">Max</span>
                            <h3 class="card-title">旗舰版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>199</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> Plus全部功能</li>
                                <li><span class="feature-icon">✓</span> GPT-4优先访问</li>
                                <li><span class="feature-icon">✓</span> 私有部署选项</li>
                                <li><span class="feature-icon">✓</span> 专属客户经理</li>
                                <li><span class="feature-icon">✓</span> 定制化开发</li>
                            </ul>
                            <button class="card-cta">联系我们</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ============================================================ -->
            <!-- 新版本：Linear 现代极简风 -->
            <!-- ============================================================ -->
            <div class="version-section" id="new">
                <div class="version-header">
                    <span class="version-label label-new">新版本</span>
                    <h2 class="version-title">Linear 现代极简风</h2>
                    <p class="version-desc">清晰专业，长时间使用不疲劳</p>
                </div>

                <div class="new-version">
                    <div class="pricing-grid">
                        <!-- Free -->
                        <div class="pricing-card">
                            <span class="card-badge badge-free">Free</span>
                            <h3 class="card-title">免费版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>0</span>
                            </div>
                            <p class="card-period">永久免费</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 每日10次AI对话</li>
                                <li><span class="feature-icon">✓</span> 基础内容导出</li>
                                <li><span class="feature-icon">✓</span> 7天历史记录</li>
                                <li><span class="feature-icon">✓</span> 社区支持</li>
                            </ul>
                            <button class="card-cta cta-secondary">开始使用</button>
                        </div>

                        <!-- Pro -->
                        <div class="pricing-card popular">
                            <span class="card-badge badge-pro">Pro</span>
                            <h3 class="card-title">专业版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>29</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 每日100次AI对话</li>
                                <li><span class="feature-icon">✓</span> Markdown导出</li>
                                <li><span class="feature-icon">✓</span> 30天历史记录</li>
                                <li><span class="feature-icon">✓</span> 优先响应支持</li>
                                <li><span class="feature-icon">✓</span> 自定义提示词</li>
                            </ul>
                            <button class="card-cta cta-primary">立即升级</button>
                        </div>

                        <!-- Plus -->
                        <div class="pricing-card highlight-purple">
                            <span class="card-badge badge-plus">Plus</span>
                            <h3 class="card-title">增强版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>69</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> 无限AI对话</li>
                                <li><span class="feature-icon">✓</span> 全格式导出</li>
                                <li><span class="feature-icon">✓</span> 无限历史记录</li>
                                <li><span class="feature-icon">✓</span> 1对1专属支持</li>
                                <li><span class="feature-icon">✓</span> API访问权限</li>
                            </ul>
                            <button class="card-cta cta-primary">立即升级</button>
                        </div>

                        <!-- Max -->
                        <div class="pricing-card highlight-pink">
                            <span class="card-badge badge-max">Max</span>
                            <h3 class="card-title">旗舰版</h3>
                            <div class="card-price">
                                <span class="currency">¥</span>
                                <span>199</span>
                            </div>
                            <p class="card-period">每月</p>
                            <ul class="card-features">
                                <li><span class="feature-icon">✓</span> Plus全部功能</li>
                                <li><span class="feature-icon">✓</span> GPT-4优先访问</li>
                                <li><span class="feature-icon">✓</span> 私有部署选项</li>
                                <li><span class="feature-icon">✓</span> 专属客户经理</li>
                                <li><span class="feature-icon">✓</span> 定制化开发</li>
                            </ul>
                            <button class="card-cta cta-primary">联系我们</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ============================================================ -->
    <!-- 优缺点对比分析 -->
    <!-- ============================================================ -->
    <section class="pros-cons-section" id="comparison">
        <div class="pros-cons-grid">
            <!-- 旧版优缺点 -->
            <div class="pros-cons-card">
                <h3 class="pros-cons-title">玻璃拟态风格</h3>
                <ul class="pros-cons-list">
                    <li><span class="pros-icon">✓</span> 视觉冲击力强，第一印象炫目</li>
                    <li><span class="pros-icon">✓</span> 时尚前卫，符合流行趋势</li>
                    <li><span class="pros-icon">✓</span> 层次感丰富，有空间深度</li>
                    <li style="border-top: 1px solid #E5E7EB; margin-top: 12px; padding-top: 16px;"></li>
                    <li><span class="cons-icon">✗</span> <strong>透明度影响文字可读性</strong></li>
                    <li><span class="cons-icon">✗</span> <strong>毛玻璃模糊效果在大屏幕上易疲劳</strong></li>
                    <li><span class="cons-icon">✗</span> backdrop-filter性能开销大</li>
                    <li><span class="cons-icon">✗</span> 渐变背景喧宾夺主，干扰内容</li>
                    <li><span class="cons-icon">✗</span> PC端长时间使用不舒适</li>
                    <li><span class="cons-icon">✗</span> 不够专业，偏娱乐化</li>
                </ul>
            </div>

            <!-- 新版优缺点 -->
            <div class="pros-cons-card">
                <h3 class="pros-cons-title">Linear 现代极简风</h3>
                <ul class="pros-cons-list">
                    <li><span class="pros-icon">✓</span> <strong>清晰度高，长时间使用不疲劳</strong></li>
                    <li><span class="pros-icon">✓</span> <strong>专业感强，符合C端专业用户审美</strong></li>
                    <li><span class="pros-icon">✓</span> <strong>PC大屏体验优秀</strong></li>
                    <li><span class="pros-icon">✓</span> 细节精致（顶部彩色边框、微动效）</li>
                    <li><span class="pros-icon">✓</span> 性能优秀，无性能开销</li>
                    <li><span class="pros-icon">✓</span> 蓝/紫/粉分级强调，层次清晰</li>
                    <li><span class="pros-icon">✓</span> 符合Linear/Notion等现代工具审美</li>
                    <li><span class="pros-icon">✓</span> 易于维护和扩展</li>
                    <li style="border-top: 1px solid #E5E7EB; margin-top: 12px; padding-top: 16px;"></li>
                    <li><span class="cons-icon">✗</span> 初次视觉冲击不如玻璃拟态强</li>
                    <li><span class="cons-icon">✗</span> 需要精细调整细节才能出彩</li>
                </ul>
            </div>
        </div>
    </section>

    <script>
        function scrollToSection(id) {
            const element = document.getElementById(id);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }
    </script>
</body>
</html>