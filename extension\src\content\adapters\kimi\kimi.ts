import { BaseAIAdapter } from '@/content/core/BaseAIAdapter'
import { KimiAnswerController } from './KimiAnswerController'
import { PlatformEntity } from '@/common/types/database_entity';

export class KimiAdapter extends BaseAIAdapter {
  private kimiAnswerController: KimiAnswerController | null = null;

  constructor(platform: PlatformEntity) {
    console.log('【KimiAdapter】KimiAdapter constructor called')
    super(platform)
    this.kimiAnswerController = new KimiAnswerController()
    this.kimiAnswerController.init(this);    
    console.log('【KimiAdapter】KimiAdapter initialized with platform:', platform)
  }

  /**
   * 销毁适配器，清理所有资源
   */
  destroy(): void {
    try {
      console.log('【KimiAdapter】KimiAdapter destroy called');
      // 调用父类销毁方法
      super.destroy();
      
      // 清理 KimiAnswerController
      if (this.kimiAnswerController) {
        this.kimiAnswerController.destroy();
        this.kimiAnswerController = null;
        console.log('【KimiAdapter】KimiAnswerController 清理完成');
      }
      
      console.log('【KimiAdapter】KimiAdapter destroy 完成');
      
    } catch (error) {
      console.error('【KimiAdapter】KimiAdapter destroy 失败:', error);
    }
  }

}
