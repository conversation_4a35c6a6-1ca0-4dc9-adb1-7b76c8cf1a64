import { CommonSelectors } from "./CommonSelectors";
import { kimiSelector } from "./kimiConfig";
import { PlatformEntity } from "@/common/types/database_entity";
import { deepseekSelector } from "./deepseekConfig";
import { chatgptSelector } from "./chatgptConfig";
import { grokSelector } from "./grokConfig";
import { geminiSelector } from "./geminiConfig";
import { poeSelector } from "./poeConfig";
import { dolaSelector } from "./dolaConfig";
import { doubaoSelector } from "./doubaoConfig";
import { monicaSelector } from "./monicaConfig";

  /**
   * 选择器配置
   */
export interface SelectorConfig {
    inputField: string[];
    sendButton: string[];
    // Kimi 特有的选择器类型
    headerContent?: string[];
    headerValue?: string[];
    chatContentList?: string[];
    chatHeaderContent?: string[];
    promptItem?: string[];   // 提示词父节点
    promptContent?: string[] // 提示词内容
    promptAction?: string[]; // 提示词操作按钮
    answerItem?: string[];
    answerCompletion?: string[];
    copyButton?: string[];
    markdown?: string[]; // markdown内容选择器(即真实的回答)
    // Poe 特有的选择器类型
    messageTuple?: string[];    // Poe问答对容器选择器
    messageElement?: string[];  // 单个消息元素选择器
    botInfo?: string[];         // 机器人信息选择器
    dateSeparator?: string[];   // 日期分隔符选择器
    codeBlock?: string[];       // 代码块选择器
}


/**
 * 选择器管理器
 */
export default class SelectorManager { 

    // 提供统一的选择器获得入口
    private static selector = null;
    public static getSelector(): SelectorConfig { 
        return SelectorManager.selector;
    }

    private static getPlatformSelectors(platform: PlatformEntity): SelectorConfig { 
        // 获取平台特定的选择器
        switch (platform.name) { 
            case "Kimi":
                return kimiSelector;
            case "DeepSeek":
                return deepseekSelector;
            case "ChatGPT":
                return chatgptSelector;
            case "Grok":
                return grokSelector;
            case "Gemini":
                return geminiSelector;
            case "Poe":
                return poeSelector;
            case "Dola":
                return dolaSelector;
            case "Doubao":
                return doubaoSelector;
            case "Monica":
                return monicaSelector;
            default:
                return CommonSelectors;
        }
    }
    
    // 改造为init
    public static initSelector(platform: PlatformEntity): SelectorConfig {
        // 获取平台特定的选择器
        const platformSelectors = this.getPlatformSelectors(platform);
        // 与common选择器合并
        const common = CommonSelectors;

        // 合并所有选择器类型（包括新增的可选类型）
        const allKeys = new Set([
            ...Object.keys(common),
            ...Object.keys(platformSelectors)
        ]);

        // 平台特定选择器优先，然后是通用选择器
        SelectorManager.selector = Array.from(allKeys).reduce((merged, key) => {
            merged[key] = [
                ...(platformSelectors[key] || []),
                ...(common[key] || [])
            ];
            return merged;
        }, {} as SelectorConfig);
        return SelectorManager.selector;
    }


    // TODO 新增一个refresh
    public static refreshSelector(platform: PlatformEntity): SelectorConfig {
        // 获取平台特定的选择器
        const platformSelectors = this.getPlatformSelectors(platform);
        // 与common选择器合并
        const common = CommonSelectors;

        // 合并所有选择器类型（包括新增的可选类型）
        const allKeys = new Set([
            ...Object.keys(common),
            ...Object.keys(platformSelectors)
        ]);

        // 平台特定选择器优先，然后是通用选择器
        SelectorManager.selector = Array.from(allKeys).reduce((merged, key) => {
            merged[key] = [
                ...(platformSelectors[key] || []),
                ...(common[key] || [])
            ];
            return merged;
        }, {} as SelectorConfig);
        return SelectorManager.selector;
    }
}
