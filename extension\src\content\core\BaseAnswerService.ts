import { BaseClipboardService } from "./BaseClipboardService";
import { AnswerModel } from "../model/AnswerModel";
import SelectorManager from "../configs/SelectorManager";
import { DOMUtils } from "../utils/DOMUtils";
import { SelectorService } from "./SelectorService";
import { EchoSyncEventEnum } from "../types/DOMEnum";

/**
 * 答案服务基类
 * 提供答案监听和提取的通用实现
 */
export abstract class BaseAnswerService {
  protected answerObserver: MutationObserver | null = null;
  protected readonly OBSERVER_CONFIG = { childList: true, subtree: true };
  // 答案生成监听配置：监听子节点变化、文本内容变化和属性变化
  protected readonly ANSWER_GENERATION_CONFIG = {
    childList: true,      // 监听子节点的添加/删除
    subtree: true,        // 监听所有后代节点
    characterData: true,  // 监听文本节点内容变化
    attributes: true,     // 监听属性变化
    attributeFilter: ['class', 'style'] // 只监听特定属性
  };
  protected readonly ANSWER_TIMEOUT = 90000; // 90秒答案超时

  // 生成中的问题和答案元素（子类可使用）
  protected generatingPromptElement: Element | null = null;
  protected generatingAnswerElement: Element | null = null;

  /**
   * 获取剪贴板服务实例（子类需要提供）
   */
  protected abstract getClipboardService(): BaseClipboardService;
  /**********************************  ⬆️⬆️⬆️⬆️⬆️⬆️ ***********************************/

  /**
   * 在聊天列表上启动监听（通用实现）
   * @param chatListElement 聊天列表元素
   */
  public async startListeningOnChatList(chatListElement: Element): Promise<void> {
    try {
      console.info('【BaseAnswerService】 启动聊天列表监听');

      // 先停止之前的监听器
      this.stopAnswerListener();

      // 捕获现有的问答对
      await this.captureExistingQAPairs(chatListElement);

      // 创建新的监听器
      this.answerObserver = new MutationObserver((mutations) => {
        this.handleMutations(mutations);
      });

      this.answerObserver.observe(chatListElement, this.OBSERVER_CONFIG);
      console.info('【BaseAnswerService】  聊天列表监听器设置完成');
    } catch (error) {
      console.error('【BaseAnswerService】 聊天列表监听器启动失败', error);
      throw error;
    }
  }

  /**********************************  ⬇️⬇️⬇️ 处理已存在  ⬇️⬇️⬇️ ***********************************/
  /**
   * 捕获现有的问答对（通用实现，子类可覆盖）
   * @param chatListElement 聊天列表元素
   */
  protected async captureExistingQAPairs(chatListElement: Element): Promise<void> {
    try {
      console.info('【BaseAnswerService】 开始捕获现有问答对');
      const selectors = SelectorManager.getSelector();

      // 处理问题
      const promptElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.promptItem);
      // console.log(promptElements);
      console.info(`【BaseAnswerService】 找到 ${promptElements.length} 个问题节点`);

      for (const element of promptElements) {
        await this.processExistingPrompt(element);
      }

      // 处理答案
      const answerElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.answerItem);
      console.info(`【BaseAnswerService】 找到 ${answerElements.length} 个答案节点`, answerElements);
      if (answerElements.length == 0) {
        return;
      }
      // 如果只有一个答案，要确保捕捉完成
      if (answerElements.length == 1) {
        // 要确保捕捉完成
        console.info("【BaseAnswerService】 只有1个答案，等待答案完成...");
        await DOMUtils.asyncSelectElementInContainer(chatListElement, selectors.answerCompletion);
        const sureAnswerElements = DOMUtils.findElementAllInContainer(chatListElement, selectors.answerItem);
        console.info(`【BaseAnswerService】 再次确认找到 ${sureAnswerElements.length} 个答案节点`);
        for (const element of sureAnswerElements) {
          await this.processExistingAnswer(element);
        }
      } else {
        for (const element of answerElements) {
          await this.processExistingAnswer(element);
        }
      }
      console.info('【BaseAnswerService】 现有问答对捕获完成');
    } catch (error) {
      console.error('【BaseAnswerService】 捕获现有问答对失败', error);
    }
  }

  protected getPromptDivHash(promptElement: Element): string|null {
    return null
  }

  protected getAnswerDivHash(answerElement: Element): string|null {
    return null
  }

  /**
   * 处理已存在的问题（通用实现，子类可覆盖）
   * @param promptElement 问题元素
   */
  protected async processExistingPrompt(promptElement: Element): Promise<void> {
    const selectors = SelectorManager.getSelector();
    const contentNode = DOMUtils.findElementInContainer(promptElement, selectors.promptContent);

    if (contentNode?.textContent) {
      const content = contentNode.textContent.trim();
      await AnswerModel.getInstance().addPrompt(content, this.getPromptDivHash(promptElement));
      // console.info('【BaseAnswerService】 问题提取完成', content.slice(0, 50));
    }
  }

  /**
   * 处理已存在的答案（通用实现，子类可覆盖）
   * @param answerElement 答案元素
   */
  protected async processExistingAnswer(answerElement: Element): Promise<void> {
    try {
      console.info('【BaseAnswerService】Existing模式: 开始提取答案', answerElement);
      const selectors = SelectorManager.getSelector();
      // 1. 查找答案完成组件
      let completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
      if (completionNode == null) {
        completionNode = await DOMUtils.asyncSelectElementInContainer(answerElement, selectors.answerCompletion, this.ANSWER_TIMEOUT);
      }
      if (completionNode == null) {
        console.warn(`【BaseAnswerService】Existing模式: 未找到答案完成组件`);
        return;
      }

      // 2. 查找复制按钮
      const copyButton = SelectorService.findCompletionButton(completionNode);
      if (!copyButton) {
        console.warn(`【BaseAnswerService】Existing模式: 未找到复制按钮`);
        return;
      }

      // 3. 使用剪贴板服务获取内容
      const clipboardService = this.getClipboardService();
      const answerContent = await clipboardService.simulateClickAndGetContent(answerElement, copyButton);

      if (!answerContent?.trim()) {
        console.warn(`【BaseAnswerService】Existing模式: 提取的答案内容为空`);
        return;
      }

      // 4. 保存到数据模型
      const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
      console.info(`【BaseAnswerService】Existing模式: 已保存，下标: ${answerIndex}`);

      // 5. 触发事件通知
      this.dispatchAnswerExtractedEvent(completionNode, answerIndex);

    } catch (error) {
      console.error(`【BaseAnswerService】Existing模式: 提取失败`, error);
    }
  }


  /**********************************  ⬆️⬆️⬆️⬆️⬆️⬆️ ***********************************/



  /**********************************  ⬇️⬇️⬇️ 处理新生成  ⬇️⬇️⬇️ ***********************************/
  /**
   * 处理 DOM 变化（通用实现，子类可覆盖）
   * @param mutations DOM 变化记录
   */
  private handleMutations(mutations: MutationRecord[]): void {
    for (const mutation of mutations) {
      for (const node of mutation.addedNodes) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          this.handleNewNode(node as Element);
        }
      }
    }
  }

  /**
     * 处理新节点（Kimi 特定实现，覆盖基类）
     * 增加了对生成中答案的特殊处理
     * @param node 新增的 DOM 节点
     */
  protected async handleNewNode(node: Element): Promise<void> {
    const selectors = SelectorManager.getSelector();

    // 检测问题节点
    if (selectors.promptItem?.some(sel => node.matches(sel))) {
      console.info('【BaseAnswerService】 检测到新的问题元素');
      if (this.generatingPromptElement) {
        console.info('【BaseAnswerService】 已存在生成中的问题节点，跳过处理');
        return;
      } else {
        this.generatingPromptElement = node;
        // 立即处理问题节点（问题内容通常是完整的）
        await this.processExistingPrompt(this.generatingPromptElement);
      }
    } else {
      // 查找子节点中的问题元素
      const childPromptElements = DOMUtils.findElementAllInContainer(node, selectors.promptItem);
      if (childPromptElements?.length > 0) {
        console.info('【BaseAnswerService】 在子节点中检测到问题元素');
        if (this.generatingPromptElement) {
          console.info('【BaseAnswerService】 已存在生成中的问题节点，跳过处理');
          return;
        } else {
          this.generatingPromptElement = childPromptElements[0]; // 取第一个
          // 立即处理问题节点（问题内容通常是完整的）
          await this.processExistingPrompt(this.generatingPromptElement);
        }
      }
    }

    // 检测答案节点
    if (selectors.answerItem?.some(sel => node.matches(sel))) {
      console.info('【BaseAnswerService】 检测到新的答案元素', node);

      // 检查是否是完成状态的答案（包含 answerCompletion 组件）
      const hasCompletion = DOMUtils.findElementInContainer(node, selectors.answerCompletion);
      if (hasCompletion) {
        console.info('【BaseAnswerService】 检测到完成状态的答案元素（包含answerCompletion），跳过MutationObserver监听，直接处理');
        // 这是已完成的答案，不需要监听生成过程
        if (this.generatingAnswerElement) {
          console.info('【BaseAnswerService】 已存在生成中的答案节点，跳过处理');
          return;
        }
        // 直接作为已存在的答案处理
        await this.processExistingAnswer(node);
        return;
      }

      if (this.generatingAnswerElement) {
        console.info('【BaseAnswerService】 已存在生成中的答案节点，跳过处理');
        console.info('【BaseAnswerService】 当前生成中的答案:', this.generatingAnswerElement);
        console.info('【BaseAnswerService】 新检测到的答案:', node);
        // 检查是否是同一个答案元素（可能是 class 变化导致的重复检测）
        if (this.generatingAnswerElement === node) {
          console.info('【BaseAnswerService】 这是同一个答案元素，可能是属性变化，继续等待...');
          return;
        }
        // 如果是不同的元素，说明旧的答案可能已经完成，清除旧的引用
        console.warn('【BaseAnswerService】 检测到新的答案元素，清除旧的生成中答案引用');
        this.clearGeneratingElements();
      }

      console.info('【BaseAnswerService】 开始监听新答案的生成过程');
      this.generatingAnswerElement = node;
      await this.processGeneratingAnswer(this.generatingAnswerElement);
    } else {
      // 查找子节点中的答案元素
      const childAnswerElements = DOMUtils.findElementAllInContainer(node, selectors.answerItem);
      if (childAnswerElements?.length > 0) {
        console.info('【BaseAnswerService】 在子节点中检测到答案元素');
        const answerElement = childAnswerElements[0];
        console.info('【BaseAnswerService】 子节点答案元素信息:', answerElement);

        if (this.generatingAnswerElement) {
          console.info('【BaseAnswerService】 已存在生成中的答案节点，跳过处理');
          return;
        }
        this.generatingAnswerElement = answerElement;
        await this.processGeneratingAnswer(this.generatingAnswerElement);
      }
    }
  }

  /**
       * 监听答案完成状态并在完成后提取内容
       * @param answerElement 答案元素
       */
  protected async processGeneratingAnswer(answerElement: Element): Promise<void> {
    console.info('【BaseAnswerService】 开始监听答案生成完成状态');
    const startTime = Date.now();
    let mutationCount = 0; // 用于调试

    // 创建专门的监听器来监听答案完成状态
    const completionObserver = new MutationObserver((mutations) => {
      mutationCount++;
      console.info(`【BaseAnswerService】 检测到 DOM 变化 (第${mutationCount}次)`, {
        mutationsCount: mutations.length,
        types: mutations.map(m => m.type).join(',')
      });

      // 检查是否超时
      if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
        console.warn('【BaseAnswerService】 答案生成监听超时');
        completionObserver.disconnect();
        this.clearGeneratingElements();
        return;
      }

      // 检查答案是否完成（answerCompletion + copyButton）
      if (this.isAnswerComplete(answerElement)) {
        console.info('【BaseAnswerService】 检测到答案生成完成');
        completionObserver.disconnect();

        // 答案完成，开始提取内容
        this.extractCompletedAnswer(answerElement).catch(error => {
          console.error('【BaseAnswerService】 提取完成答案失败', error);
        });
      } else {
        console.info('【BaseAnswerService】 答案仍在生成中，继续监听...');
      }
    });

    // 使用增强的配置来监听 answerElement 及其子树的变化
    completionObserver.observe(answerElement, this.ANSWER_GENERATION_CONFIG);
    console.info('【BaseAnswerService】 已设置答案生成监听器，配置:', this.ANSWER_GENERATION_CONFIG);

    // 立即检查一次（可能答案已经完成了）
    if (this.isAnswerComplete(answerElement)) {
      console.info('【BaseAnswerService】 答案已经完成，立即处理');
      completionObserver.disconnect();
      await this.extractCompletedAnswer(answerElement);
    } else {
      console.info('【BaseAnswerService】 答案尚未完成，等待生成...');
    }
  }

  /**
     * 标准：存在 answerCompletion 组件且包含 copyButton
     * @param answerElement 答案元素
     * @returns 是否完成
     */
  protected isAnswerComplete(answerElement: Element): boolean {
    const selectors = SelectorManager.getSelector();
    if (!selectors?.answerCompletion || !selectors?.copyButton) {
      return false;
    }

    // 查找 answerCompletion 组件
    const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
    if (!completionNode) {
      return false;
    }

    // 检查 completionNode 中是否存在 copyButton
    const copyButton = DOMUtils.findElementInContainer(completionNode, selectors.copyButton);
    return !!copyButton;
  }

  /**
    * 提取已完成的答案内容
    * @param answerElement 答案元素
    */
  protected async extractCompletedAnswer(answerElement: Element): Promise<void> {
    try {
      // 使用基类的方法提取答案内容
      await this.extractAnswerContent(answerElement, '提取新生成答案');
      // 清除生成中的元素引用
      this.clearGeneratingElements();
    } catch (error) {
      console.error('【BaseAnswerService】提取完成答案失败', error);
      this.clearGeneratingElements();
    }
  }

  /**
   * 提取答案内容（通用实现，子类可覆盖）
   * @param answerElement 答案元素
   * @param context 上下文描述（用于日志）
   * @returns 答案内容或 null
   */
  protected async extractAnswerContent(answerElement: Element, context: string): Promise<string | null> {
    try {
      const selectors = SelectorManager.getSelector();

      // 1. 查找答案完成组件
      const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
      if (!completionNode) {
        console.warn(`【BaseAnswerService】 ${context}: 未找到答案完成组件`);
        return null;
      }

      // 2. 查找复制按钮
      const copyButton = SelectorService.findCompletionButton(completionNode);
      if (!copyButton) {
        console.warn(`【BaseAnswerService】 ${context}: 未找到复制按钮`);
        return null;
      }

      // 3. 使用剪贴板服务获取内容
      const clipboardService = this.getClipboardService();
      const answerContent = await clipboardService.simulateClickAndGetContent(answerElement, copyButton);

      if (!answerContent?.trim()) {
        console.warn(`【BaseAnswerService】 ${context}: 提取的答案内容为空`);
        return null;
      }

      // 4. 保存到数据模型
      const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
      console.info(`【BaseAnswerService】 ${context} 已保存，下标: ${answerIndex}`);

      // 5. 触发事件通知
      this.dispatchAnswerExtractedEvent(completionNode, answerIndex);

      return answerContent;
    } catch (error) {
      console.error(`【BaseAnswerService】 ${context}: 提取失败`, error);
      return null;
    }
  }

  /**
   * 清除生成中的元素引用（通用实现）
   */
  protected clearGeneratingElements(): void {
    this.generatingPromptElement = null;
    this.generatingAnswerElement = null;
  }
  /**********************************  ⬆️⬆️⬆️⬆️⬆️⬆️ ***********************************/

  /**
  * 触发注入导出事件（通用实现）
  * @param element 答案元素
  * @param index 答案索引
  */
  protected dispatchAnswerExtractedEvent(completionElement: Element, index: number): void {
    const event = new CustomEvent(EchoSyncEventEnum.ANSWER_EXTRACTED, {
      detail: {
        completionElement: completionElement,  // 使用 completionElement 字段名（用于注入器）
        answerIndex: index,       // 使用 answerIndex 字段名
        chatId: AnswerModel.getInstance().getCurrentChatId()
      },
    });
    console.info('【BaseAnswerService】 发送注入导出事件', { index });
    document.dispatchEvent(event);

  }

  /**
   * 停止监听器（通用实现）
   */
  public stopAnswerListener(): void {
    if (this.answerObserver) {
      this.answerObserver.disconnect();
      this.answerObserver = null;
      console.info('【BaseAnswerService】 监听器已停止');
    }
  }

  /**
   * 销毁服务（通用实现）
   */
  public destroy(): void {
    this.stopAnswerListener();
    this.clearGeneratingElements();
    console.info('【BaseAnswerService】 服务已销毁');
  }
}
