# 实施计划

## 任务列表

- [ ] 1. 创建DI框架基础设施
  - 实现DIContainer接口
  - 实现SimpleDIContainer类
  - 创建依赖标识符tokens
  - 添加相关类型定义
  - _需求: 需求1_

- [ ] 2. 创建模型接口
  - 定义IInputModel接口
  - 让InputModel实现IInputModel接口
  - 创建interfaces目录和导出文件
  - _需求: 需求2, 需求3_

- [ ] 3. 改造InputCapture模块
  - 修改构造函数接受IInputModel依赖
  - 移除直接调用InputModel.getInstance()
  - 更新所有使用inputModel的地方
  - 确保功能保持不变
  - _需求: 需求2_

- [ ] 4. 改造ArchiveButtonInject模块
  - 修改构造函数接受IInputModel依赖
  - 移除直接调用InputModel.getInstance()
  - 更新所有使用inputModel的地方
  - 确保功能保持不变
  - _需求: 需求3_

- [ ] 5. 更新BaseAIAdapter集成DI
  - 在BaseAIAdapter中集成DI容器
  - 配置依赖注册
  - 修改子模块创建方式使用DI解析
  - 添加容器清理逻辑
  - _需求: 需求4_

- [ ] 6. 更新具体平台适配器
  - 检查chatgpt.ts等具体适配器
  - 确保继承的方法调用正确
  - 测试各平台功能正常
  - _需求: 需求5_

- [ ] 7. 添加单元测试
  - 为DI容器编写测试
  - 为改造后的模块编写测试
  - 验证依赖注入功能正常
  - 确保mock测试可行
  - _需求: 需求2, 需求3_

- [ ] 8. 集成测试和验证
  - 测试完整的content模块功能
  - 验证输入捕捉功能正常
  - 验证存档按钮功能正常
  - 验证chatUid生成和共享正常
  - _需求: 需求5_

- [ ] 9. 更新相关规则文档
  - 更新content/model.md规则
  - 添加DI使用规范
  - 更新开发检查清单
  - 记录最佳实践
  - _需求: 所有需求_

- [ ] 10. 代码清理和优化
  - 移除不再使用的代码
  - 优化导入导出
  - 确保代码符合规范
  - 添加必要的注释
  - _需求: 所有需求_
