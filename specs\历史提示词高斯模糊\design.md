# 技术方案设计

## 架构概述

将当前的 HistoryBubble 组件从悬浮气泡模式重构为模态页模式，增加背景模糊、复制功能和Toast提示。

## 技术栈

- **样式框架**: Tailwind CSS 3 + shadcn/ui
- **开发语言**: TypeScript 5
- **组件类型**: Content Script UI组件（原生DOM操作）
- **样式管理**: 内联样式 + 独立CSS文件

## 核心组件设计

### 1. HistoryBubble 组件重构

```typescript
export class HistoryBubble {
  private modalContainer: HTMLElement | null = null
  private backdropElement: HTMLElement | null = null
  private contentContainer: HTMLElement | null = null
  private toastContainer: HTMLElement | null = null
  
  // 新增方法
  private createModalStructure(): void
  private createBackdrop(): void
  private showToast(message: string, position: {x: number, y: number}): void
  private copyToClipboard(text: string): Promise<boolean>
  private handleOutsideClick(event: MouseEvent): void
}
```

### 2. 模态页结构

```
Modal Container (fixed, z-index: 2147483647)
├── Backdrop (高斯模糊背景)
└── Content Container (居中内容)
    ├── Header (标题区域)
    ├── History List (提示词列表)
    └── Toast Container (复制提示)
```

### 3. 样式架构

#### 主要CSS类设计
- `.echosync-modal-backdrop` - 模态页背景遮罩
- `.echosync-modal-content` - 模态页内容容器
- `.echosync-history-modal` - 历史记录模态页
- `.echosync-toast` - Toast提示组件

#### 响应式设计
- 移动端: 全屏模态页，底部弹出
- 桌面端: 居中模态页，固定尺寸

## 功能实现方案

### 1. 模态页显示逻辑

```mermaid
graph TD
    A[鼠标悬浮小球] --> B[创建模态页结构]
    B --> C[添加背景模糊]
    C --> D[显示模态页动画]
    D --> E[绑定事件监听器]
```

### 2. 复制功能流程

```mermaid
graph TD
    A[点击提示词] --> B[复制到剪贴板]
    B --> C{复制成功?}
    C -->|是| D[显示成功Toast]
    C -->|否| E[显示错误Toast]
    D --> F[3秒后隐藏Toast]
    E --> F
    F --> G[关闭模态页]
```

### 3. 关闭交互逻辑

```mermaid
graph TD
    A[用户交互] --> B{交互类型}
    B -->|点击空白区域| C[关闭模态页]
    B -->|点击提示词| D[复制后关闭]
    B -->|ESC键| C
    C --> E[移除背景模糊]
    D --> E
    E --> F[清理事件监听器]
```

## 性能优化策略

### 1. 内存管理
- 及时清理事件监听器
- 销毁时移除DOM元素
- 使用 WeakMap 存储临时数据

### 2. 动画优化
- 使用 CSS transform 而非改变布局属性
- 利用 requestAnimationFrame 优化动画
- 硬件加速：`will-change: transform`

### 3. 事件处理优化
- 使用事件委托减少监听器数量
- 防抖处理频繁触发的事件

## 兼容性考虑

### 1. 浏览器兼容性
- 支持 Chrome 88+
- backdrop-filter 降级方案
- Clipboard API 降级到 document.execCommand

### 2. 样式隔离
- 使用 `echosync-` 前缀避免冲突
- 设置最高 z-index 确保层级
- 重置默认样式避免继承

## 文件结构

```
extension/src/content/components/
├── HistoryBubble.ts (主组件文件)
├── HistoryBubble.css (样式文件)
└── Toast.ts (Toast组件，可选独立)
```

## 关键技术点

### 1. 高斯模糊实现
```css
.echosync-modal-backdrop {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.3);
}
```

### 2. 剪贴板API使用
```typescript
async copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch {
    // 降级方案
    return this.fallbackCopyToClipboard(text)
  }
}
```

### 3. 事件处理
```typescript
private handleOutsideClick = (event: MouseEvent): void => {
  if (!this.contentContainer?.contains(event.target as Node)) {
    this.hide()
  }
}
```
