# Grok 平台优化测试指南

## 📋 测试清单

### 1. 环境准备
- [ ] 编译扩展：`npm run build`
- [ ] 加载扩展到 Chrome
- [ ] 打开 Grok 测试页面

### 2. 测试场景

#### 场景 A：只有 last-reply-container（新会话）
**测试页面**：https://grok.com/ 
1. 在首页输入问题
2. 等待跳转到 chat 页面
3. 等待答案生成完成

**验证点**：
- [ ] 控制台输出 `[GrokAnswerService] 开始捕获 Grok 问答对`
- [ ] 显示 `找到 0 个历史节点, last-reply-container: 存在`
- [ ] 显示 `处理 last-reply-container`
- [ ] 显示 `last-reply-container 包含 2 个 response 节点`
- [ ] 问题和答案都被正确捕获并存储

#### 场景 B：有历史记录（已有会话）
**测试页面**：https://grok.com/c/db3e2379-da11-4f47-9d5e-305a82ff7af4

1. 直接打开已有会话链接
2. 观察控制台输出

**验证点**：
- [ ] 显示 `找到 N 个历史节点`（N > 0）
- [ ] 显示 `处理 N 个历史节点`
- [ ] 每个问答对都显示 `处理问答对: response-xxx -> response-yyy`
- [ ] 显示 `处理 last-reply-container`
- [ ] 所有问答对都被正确捕获

#### 场景 C：重复访问同一页面（去重测试）
1. 打开已有会话：https://grok.com/c/db3e2379-da11-4f47-9d5e-305a82ff7af4
2. 刷新页面
3. 观察控制台输出

**验证点**：
- [ ] 第一次访问：正常捕获所有问答对
- [ ] 刷新后：由于 `processedResponseIds` 已清空，会重新捕获
- [ ] （这是预期行为：页面刷新后重新捕获）

#### 场景 D：答案生成中（等待测试）
1. 在 Grok 首页输入问题
2. 快速刷新页面（在答案生成过程中）
3. 观察是否等待答案完成

**验证点**：
- [ ] 显示 `答案生成中，等待完成（最多90秒）`
- [ ] 等待完成后显示 `答案已完成`
- [ ] 答案被正确捕获

#### 场景 E：多轮对话
1. 打开已有会话
2. 继续提问新问题
3. 等待答案生成完成

**验证点**：
- [ ] 历史问答对被正确捕获
- [ ] 新问答对被正确捕获
- [ ] 没有重复存储

### 3. 控制台日志检查

#### 正常流程日志示例
```
[GrokAnswerService] 开始捕获 Grok 问答对
[GrokAnswerService] 找到 2 个历史节点, last-reply-container: 存在
[GrokAnswerService] 处理 2 个历史节点
[GrokAnswerService] 处理问答对: response-xxx -> response-yyy
[GrokAnswerService] 处理 last-reply-container
[GrokAnswerService] last-reply-container 包含 2 个 response 节点
[GrokAnswerService] 处理 last-reply-container 问答对: response-aaa -> response-bbb
[GrokAnswerService] 答案已完成，直接处理
[GrokAnswerService] Grok 问答对捕获完成
```

#### 去重日志示例
```
[GrokAnswerService] 问答对已处理，跳过: response-xxx
```

#### 错误日志（需要关注）
```
[GrokAnswerService] 问答对不完整，跳过
[GrokAnswerService] 节点类型验证失败，跳过
[GrokAnswerService] 等待答案完成超时或失败
```

### 4. 数据验证

#### 检查存储的数据
1. 打开扩展的 Options 页面
2. 查看聊天历史
3. 验证：
   - [ ] 问题内容正确
   - [ ] 答案内容完整
   - [ ] 没有重复记录
   - [ ] Markdown 格式正确

### 5. 边界情况测试

#### 测试 A：空会话
- 打开 Grok 首页但不提问
- **预期**：不应有错误

#### 测试 B：快速切换会话
- 快速打开多个不同的会话链接
- **预期**：每个会话独立处理，无混淆

#### 测试 C：网络慢加载
- 使用 Chrome DevTools 限制网络速度
- **预期**：等待机制正常工作

### 6. 性能检查

- [ ] 页面加载速度无明显影响
- [ ] 内存占用正常
- [ ] 没有内存泄漏（多次刷新后检查）

## 🐛 已知问题记录

### 问题模板
```markdown
**问题描述**：

**复现步骤**：
1. 
2. 
3. 

**预期行为**：

**实际行为**：

**控制台输出**：
```

## ✅ 测试结果总结

**测试日期**：____________________

**测试人员**：____________________

**测试环境**：
- Chrome 版本：____________________
- 扩展版本：____________________

**测试结果**：
- [ ] 场景 A - 通过
- [ ] 场景 B - 通过
- [ ] 场景 C - 通过
- [ ] 场景 D - 通过
- [ ] 场景 E - 通过
- [ ] 边界测试 - 通过
- [ ] 性能检查 - 通过

**发现的问题**：
1. 
2. 

**建议优化**：
1. 
2. 

**总体评价**：____________________
