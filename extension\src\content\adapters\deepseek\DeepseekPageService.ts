import { BasePageService } from "@/content/core/BasePageService";

/**
 * Deepseek 页面状态管理服务
 * 继承 BasePageService，提供 Deepseek 平台特定的 URL 匹配规则
 */
export class DeepseekPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'DeepSeek';
    }

    /**
     * 获取 Deepseek 的 URL 匹配模式
     * DeepSeek URL 结构:
     * - Home: https://chat.deepseek.com/
     * - Chat: https://chat.deepseek.com/a/chat/s/{chatId}
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: 主页或根路径
            home: /^https:\/\/chat\.deepseek\.com\/?$/i,
            // Chat页模式: /a/chat/s/ 后跟 UUID 格式的 chatId
            chat: /^https:\/\/chat\.deepseek\.com\/a\/chat\/s\/([a-f0-9-]+)$/i
        };
    }

    /**
     * 检测欢迎页面（可选：添加 DOM 降级检测）
     * 如果 URL 检测不足，可以覆盖此方法添加 DOM 选择器
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 可选：添加 DOM 降级检测
        // return document.querySelector('.deepseek-home-page') !== null;
        return false;
    }
    
    /**
     * 检测聊天页面（可选：添加 DOM 降级检测）
     * 如果 URL 检测不足，可以覆盖此方法添加 DOM 选择器
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 可选：添加 DOM 降级检测
        // return document.querySelector('.deepseek-chat-page') !== null;
        return false;
    }
}
