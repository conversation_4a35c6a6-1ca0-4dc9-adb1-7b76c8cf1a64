# dola平台实现

新增ai 聊天平台dola的支持，注意：
1. dola平台要支持2个网站 www.dola.com 和旧版本地址www.cici.com.
2. dola平台的home页可以是https://www.dola.com/chat/?from_login=这样的前缀，也可能是`https://www.dola.com/chat/` 这样
3. dola平台的chat页是 `https://www.dola.com/chat/214073385654545` 这样，后面是chatId.
4. 要帮我实现这个平台特定的 dexie.ts 文件中的配置，以及src/content/configs/SelectorManager.ts 文件中的选择器配置。src/content/configs/dolaConfig.ts 文件中的选择器配置。包括适配器的所有子类OolaAnswerController.ts, OolaAnswerService.ts, OolaPageService.ts, OolaClipboardService.ts. 以及主适配器Oola.ts.
5. 请在实现过程中，如果有需要，可以增加新的选择器配置，但是不要修改其他平台已经存在的选择器配置。
6. 可以使用chrome-mcp查看https://www.dola.com/chat/214073385654545 页面，获得选择器配置相关的内容。

---

## ✅ 实施完成记录 (2025-10-25)

### 核心设计决策

#### 1. 类名规范 ✅
**决定**: 统一使用 **`Dola`** (与平台名称一致)
- 文件名: `DolaPageService.ts`, `DolaAnswerController.ts` 等
- 类名: `class DolaAdapter`, `class DolaPageService` 等
- 避免需求文档中的 "Oola" 拼写错误

#### 2. 页面 DOM 结构分析 ✅
**使用工具**: chrome-mcp 抓取 `https://www.dola.com/chat/214073385654545`

**关键选择器发现**:
```typescript
{
  // 问答对容器
  messageTuple: '[data-testid="union_message"]',
  
  // 用户消息和AI回答
  promptItem: '[data-testid="send_message"]',
  answerItem: '[data-testid="receive_message"]',
  
  // 答案内容
  markdown: '[data-testid="message_text_content"]',
  
  // 输入和发送
  inputField: '[data-testid="chat_input_input"]',
  sendButton: '[data-testid="chat_input_send_button"]',
  
  // 操作按钮
  copyButton: '[data-testid="message_action_copy"]',
  
  // 答案完成标记（关键！）
  answerCompletion: '[data-testid="message_action_bar"][class*="opacity-100"]'
}
```

#### 3. 答案完成判断逻辑 ✅
**选择方案**: MutationObserver 监听 + 操作栏 opacity 检测

**实现细节**:
```typescript
// 三层检测策略（由精确到兜底）
1. 检查操作栏 opacity: class 包含 'opacity-100' 且不包含 'opacity-0'
2. 检查操作栏交互: 无 'pointer-events-none' class
3. 检查复制按钮: 可见且非 hidden 状态
```

**优势**:
- ✅ 更精确：直接检测 Dola 的 UI 状态变化
- ✅ 更稳定：使用官方的 data-testid 属性
- ✅ 有降级：三层检测保证鲁棒性

**参考**: 
- 基于 ChatGPT 平台的实现模式
- 父类 `BaseAnswerService` 已提供完整的答案完成检测框架

#### 4. 平台类型定义 ✅
**确定**: 使用 PascalCase **`"Dola"`**

**类型定义**:
```typescript
// database_entity.ts
export type PlatformName = 
  | 'DeepSeek' 
  | 'Kimi' 
  | 'ChatGPT' 
  | 'Claude' 
  | 'Gemini' 
  | 'Grok'
  | 'Poe'
  | 'Dola'  // ✅ 新增
  | 'Perplexity'
  | 'You'
```

**保持一致性**: 所有平台使用 PascalCase

#### 5. cici.com 兼容性 ✅
**确认**: cici.com 会重定向到 dola.com

**实现策略**:
- ✅ URL 正则同时支持两个域名: `/^www\.(dola|cici)\.com$/`
- ✅ manifest.json 中同时添加两个域名权限
- ✅ 无需特殊处理 DOM 差异（重定向后使用统一结构）

---

## 📁 已实现文件清单

### 适配器核心文件
- ✅ `extension/src/content/adapters/dola/Dola.ts` - 主适配器
- ✅ `extension/src/content/adapters/dola/DolaAnswerController.ts` - 控制器
- ✅ `extension/src/content/adapters/dola/DolaAnswerService.ts` - 答案服务
- ✅ `extension/src/content/adapters/dola/DolaClipboardService.ts` - 剪贴板服务
- ✅ `extension/src/content/adapters/dola/DolaPageService.ts` - 页面服务

### 配置文件
- ✅ `extension/src/content/configs/dolaConfig.ts` - 选择器配置
- ✅ `extension/src/content/configs/SelectorManager.ts` - 添加 Dola 配置
- ✅ `extension/src/content/types/Consts.ts` - 添加 DolaConfig
- ✅ `extension/src/common/types/database_entity.ts` - 添加 Dola 类型

### 系统集成
- ✅ `extension/src/content/core/ContentScriptManager.ts` - 注册 DolaAdapter
- ✅ `extension/public/manifest.json` - 添加域名权限

### 文档
- ✅ `需求-dola平台实现-完成总结.md` - 实施总结文档

---

## 🎯 设计亮点

1. **稳定选择器**: 使用 Dola 官方的 `data-testid` 属性，抗页面更新
2. **双域名支持**: 同时支持 dola.com 和 cici.com（旧版）
3. **三层检测**: 答案完成检测有三重保障，确保准确性
4. **完整降级**: 每个关键功能都有多层降级方案
5. **架构一致**: 完全遵循现有平台实现模式，易于维护

---

## ✅ 实施状态
- **开始时间**: 2025-10-25 20:00
- **完成时间**: 2025-10-25 20:30
- **状态**: 🎉 实施完成，待测试
- **编译检查**: ✅ 无错误

---

## 📝 后续工作

### 测试阶段
1. [ ] 功能测试：页面检测、问答捕获、数据提取
2. [ ] 边缘情况测试：快速提问、长文本、代码块
3. [ ] 兼容性测试：cici.com 重定向验证
4. [ ] 性能测试：多轮对话、大量数据

### 优化方向
1. [ ] 根据实际测试结果调整答案完成检测阈值
2. [ ] 如需要，增加更多选择器降级方案
3. [ ] 建立选择器健康度监控

---

详细实施过程和技术细节请参考: `需求-dola平台实现-完成总结.md`