# 需求-实现gemini平台的适配

## ✅ 实现完成总结

### 1. 需求背景
- Gemini平台的home页: `https://gemini.google.com/app`
- Gemini平台的chat页: `https://gemini.google.com/app/770c477315509a43` (其中 `770c477315509a43` 是chatId)
- 需要参考ChatGPT平台的实现，完成完整的Gemini平台适配

### 2. 页面结构分析 (基于 chrome-mcp 实际抓取)

**关键发现:**
- Gemini 使用 **Angular Material** 组件和 **Web Components** 架构
- 自定义元素: `<chat-app>`, `<model-response>`, `<user-query>` 等
- 输入框: 基于 Quill 编辑器的 `rich-textarea` 组件
- 答案完成标记: `message-actions` 组件的出现
- 数学公式: 使用 KaTeX 渲染,带 `data-math` 属性

**核心DOM选择器:**
```typescript
inputField: 'rich-textarea .ql-editor[contenteditable="true"]'
sendButton: 'button[aria-label="发送"]' 或 'button:has(mat-icon[fonticon="send"])'
answerItem: 'model-response' 或 'response-container'
answerCompletion: 'model-response:has(message-actions)'
copyButton: 'copy-button > button'
markdown: 'message-content .markdown'
```

### 3. 已完成的文件清单

#### ✅ 配置文件
- **`geminiConfig.ts`**: 完整的选择器配置
  - 定义了所有 Gemini 特有的 DOM 选择器
  - 支持中英文界面
  - 包含降级选择器方案

#### ✅ 服务层实现
1. **`GeminiClipboardService.ts`**
   - 继承 `BaseClipboardService`
   - 自定义 Turndown 规则处理 KaTeX 数学公式
   - 支持从 Angular Material 组件提取内容
   - 实现降级方案(从 DOM 直接提取)

2. **`GeminiPageService.ts`**
   - 继承 `BasePageService`
   - URL 模式匹配: Home页(`/app`) 和 Chat页(`/app/{chatId}`)
   - DOM 降级检测(通过 `model-response` 元素判断)

3. **`GeminiAnswerService.ts`**
   - 继承 `BaseAnswerService`
   - 集成 `GeminiClipboardService`
   - 监听 DOM 变化检测答案更新

4. **`GeminiAnswerController.ts`**
   - 继承 `BaseAnswerController`
   - 创建并协调 `GeminiAnswerService` 和 `GeminiPageService`
   - 管理完整答案处理流程

#### ✅ 适配器主类
- **`gemini.ts`** (重构版)
  - 继承 `BaseAIAdapter`
  - 集成 `GeminiAnswerController`
  - 完整的生命周期管理(初始化/销毁)

#### ✅ 注册集成
- **`SelectorManager.ts`**: 已添加 `geminiSelector` 导入和注册
- **`ContentScriptManager.ts`**: 已存在 `GeminiAdapter` 导入和工厂方法注册

### 4. 架构特点

```
GeminiAdapter (主适配器)
    ├── GeminiAnswerController (答案控制器)
    │   ├── GeminiAnswerService (答案服务)
    │   │   └── GeminiClipboardService (剪贴板服务)
    │   └── GeminiPageService (页面服务)
    └── BaseAIAdapter (基类功能)
        ├── InputCapture (输入捕获)
        └── UI Injection (UI注入)
```

### 5. Gemini 平台特殊处理

1. **Angular Material 组件**
   - 使用 Web Components 自定义元素
   - 需要穿透 Shadow DOM (如果存在)
   
2. **数学公式处理**
   - 识别 KaTeX 渲染的公式
   - 从 `data-math` 属性提取原始公式
   - 转换为标准 Markdown 数学语法

3. **答案完成检测**
   - 监听 `message-actions` 组件出现
   - 而非特定按钮(与 ChatGPT 不同)

4. **输入框特殊性**
   - Quill 编辑器,非原生 `<textarea>`
   - 需要使用 `.ql-editor` 选择器

### 6. 待测试项

下一步需要在实际Gemini页面测试:
- [ ] 输入框识别和捕获
- [ ] 发送按钮点击
- [ ] 用户提问捕获
- [ ] AI答案实时监听
- [ ] 答案完成检测准确性
- [ ] 复制按钮功能
- [ ] Markdown内容提取
- [ ] KaTeX 公式转换
- [ ] 浮动气泡UI注入
- [ ] 存档按钮UI注入
- [ ] 页面类型检测(Home/Chat)

### 7. 潜在优化点

1. **选择器优先级调优**
   - 根据实际测试调整选择器顺序
   - 可能需要添加更多降级选择器

2. **性能优化**
   - Gemini 页面较重,需要优化 MutationObserver
   - 考虑节流/防抖策略

3. **兼容性**
   - Gemini 可能会更新组件结构
   - 需要保持选择器的灵活性

### 8. 讨论和疑问

**已解决:**
- ✅ 如何识别 Gemini 的输入框? → 使用 `rich-textarea` 和 `.ql-editor`
- ✅ 答案完成的标志是什么? → `message-actions` 组件出现
- ✅ 如何处理数学公式? → 自定义 Turndown 规则处理 KaTeX

**待确认:**
- ⚠️ Gemini 是否使用 Shadow DOM? (实测确认)
- ⚠️ 是否需要特殊处理图片回答? (如果支持)
- ⚠️ 多轮对话的答案如何区分? (通过父容器 `.conversation-container`)

---

## 📝 原始需求

请参考content模块ChatGPT平台的实现，完成Gemini平台的适配。
比如完成gemini.ts, GeminiAnswerService.ts, GeminiPageService.ts, GeminiClipboardService.ts, GeminiAnswerController.ts五个文件的实现。
以及对应的适配器注册。

请使用chrome-mcp查看https://gemini.google.com/app/770c477315509a43页面来完成configs目录下的gemini适配器geminiConfig