import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

// 创建 Turndown 实例,配置适合 Gemini 的转换规则
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  hr: '---'
});

// 处理代码块: Gemini 使用标准的 <code> 标签
turndownService.addRule('fencedCodeBlock', {
  filter: (node) => {
    return (
      node.nodeName === 'CODE' &&
      /language-/.test(node.className)
    );
  },
  replacement: (content, node) => {
    const code = node.innerText || node.textContent || '';
    const match = node.className.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

// 处理 <pre><code> 组合
turndownService.addRule('preCodeBlock', {
  filter: (node) => node.nodeName === 'PRE' && node.querySelector('code'),
  replacement: (content, node) => {
    const codeNode = node.querySelector('code');
    const code = codeNode?.innerText || node.innerText || '';
    const match = codeNode?.className?.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

// 处理 Gemini 的数学公式 (KaTeX)
turndownService.addRule('mathFormula', {
  filter: (node) => {
    return node.nodeName === 'SPAN' && 
           (node.className?.includes('katex') || node.className?.includes('math-inline'));
  },
  replacement: (content, node) => {
    // 尝试从 data-math 属性获取原始公式
    const mathContent = node.getAttribute('data-math') || content;
    return `$${mathContent}$`;
  }
});

/**
 * Gemini 剪贴板管理服务
 * 继承 BaseClipboardService，提供 Gemini 平台特定的剪贴板操作
 * 
 * 主要特性：
 * - 使用父类的通用点击+剪贴板流程
 * - 自定义 Turndown 规则处理 Gemini 的特殊格式(如 KaTeX 数学公式)
 * - 支持从 Angular Material 组件提取内容
 */
export class GeminiClipboardService extends BaseClipboardService {
    
  /**
   * 降级方法：直接从 DOM 提取内容
   * @param answerElement 答案元素 (通常是 model-response 或 response-container)
   * @returns Markdown 格式的内容
   */
  protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    const selectors = SelectorManager.getSelector();
    const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
    
    if (!markdownNode) {
      console.warn('[GeminiClipboardService] 未找到 markdown 内容节点, 尝试直接使用答案元素');
      // 如果找不到 markdown 节点，尝试在 message-content 中查找
      const messageContent = answerElement.querySelector('message-content');
      if (messageContent) {
        return turndownService.turndown(messageContent.innerHTML);
      }
      throw new Error('未找到可提取的内容节点');
    }

    console.info('[GeminiClipboardService] 从 markdown 节点提取内容');
    // 使用配置好的 Turndown 服务转换 HTML 到 Markdown
    return turndownService.turndown(markdownNode.innerHTML);
  }
}
