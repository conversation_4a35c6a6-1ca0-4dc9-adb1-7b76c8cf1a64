# Bug 修复: ArchiveService is not available

## 🐛 问题描述

```
ArchiveButtonInject.ts.js:105 【EchoSync】ArchiveService is not available
updateButtonState @ ArchiveButtonInject.ts.js:105
```

## 🔍 根本原因

在 `ArchiveButtonInject.ts` 文件中存在一个**错误的导入路径**:

```typescript
// ❌ 错误的导入路径
import { BaseAIAdapter } from '@/content/adapters/core/BaseAIAdapter';
```

正确的路径应该是:

```typescript
// ✅ 正确的导入路径
import { BaseAIAdapter } from '@/content/core/BaseAIAdapter';
```

## 🔧 修复方案

### 修改文件: `ArchiveButtonInject.ts`

**修改位置**: 第 2 行

```diff
import { ArchiveButton } from '@/content/inject/components/ArchiveButton';
- import { BaseAIAdapter } from '@/content/adapters/core/BaseAIAdapter';
+ import { BaseAIAdapter } from '@/content/core/BaseAIAdapter';
import { EchoSyncEventEnum } from '../types/DOMEnum';
import ArchiveService from '../service/ArchiveService';
import { PlatformEntity } from '@/common/types/database_entity';
```

### 额外改进

在构造函数中添加了更详细的日志:

```typescript
constructor(adapter: BaseAIAdapter) {
    this.adapter = adapter;
    
    // 正确获取 ArchiveService 单例实例
    this.archiveService = ArchiveService.getInstance();
    
    // 验证 archiveService 是否正确初始化
    if (!this.archiveService) {
      console.error('【EchoSync】ArchiveButtonInject: ArchiveService initialization failed');
      throw new Error('ArchiveService initialization failed');
    }
    
    console.log('【EchoSync】ArchiveButtonInject: ArchiveService initialized successfully');
    
    this.component = new ArchiveButton();
    this.setupEventListeners();
    this.inject();
}
```

## ✅ 验证

1. 编译检查: ✅ 无错误
2. 导入路径: ✅ 正确
3. 类型检查: ✅ 通过

## 📝 注意事项

### 项目目录结构

```
extension/src/content/
├── core/                      # ✅ 核心模块
│   ├── BaseAIAdapter.ts      # 基础适配器
│   └── ...
├── adapters/                  # 平台适配器
│   ├── chatgpt/
│   ├── gemini/
│   └── ...
└── inject/
    └── ArchiveButtonInject.ts
```

正确的导入路径应该指向 `core` 目录,而不是 `adapters/core`。

## 🚀 影响范围

此修复解决了:
- ✅ ArchiveService 无法初始化的问题
- ✅ 存档按钮无法正常工作的问题
- ✅ TypeScript 编译错误

## 🔄 后续建议

建议在项目中统一检查所有导入路径,避免类似问题:

```bash
# 搜索可能的错误导入
grep -r "@/content/adapters/core" extension/src/
```
