# Monica 平台支持实现完成总结

## 📋 实现概述

已成功实现 Monica 平台（https://monica.im）的完整支持，遵循项目统一的适配器架构模式。

**实现日期**: 2025-10-26  
**平台ID**: 10  
**平台名称**: Monica

## 🔄 更新记录

### 2025-10-26 更新
**变更内容**：
1. ✅ **启用复制按钮功能**：Monica 平台支持复制按钮，优先使用复制按钮获取内容
2. ✅ **使用属性选择器处理 hash**：采用 `[class*="xxx"]` 部分匹配，应对 CSS Modules 动态 hash
3. ✅ **添加 copyButton 选择器**：在 `monicaConfig.ts` 中添加复制按钮选择器
4. ✅ **注释 getClipboardContent**：在 `MonicaClipboardService.ts` 中注释掉覆盖方法，使用父类实现

**影响范围**：
- `monicaConfig.ts` - 所有选择器改用属性选择器（如 `[class*="chat-items"]`）
- `MonicaClipboardService.ts` - 注释掉 `getClipboardContent()` 方法

**技术细节**：
```typescript
// Monica 实际 DOM 类名
<div class="chat-items--etBvz">  // hash 会变化

// 错误方案1: 使用完整类名
'.chat-items--etBvz'  // ❌ 下次打包后失效

// 错误方案2: 直接去掉 hash
'.chat-items'  // ❌ 无法匹配实际类名 chat-items--etBvz

// 正确方案: 属性选择器部分匹配
'[class*="chat-items"]'  // ✅ 匹配任何包含 "chat-items" 的类名
```

---

## ✅ 完成的工作

### 1. 数据库配置（2个文件）

#### `extension/src/common/database/dexie.ts`
- 添加 Monica 平台配置（ID: 10）
- URL: `https://monica.im`
- 图标: `https://monica.im/favicon.ico`

#### `extension/src/common/types/database_entity.ts`
- 添加 `'Monica'` 到 `PlatformName` 类型定义

### 2. 选择器配置（2个文件）

#### `extension/src/content/configs/monicaConfig.ts`
创建 Monica 平台专用选择器配置：

**关键特点**：
1. ✅ **使用属性选择器**：`[class*="xxx"]` 部分匹配类名
2. ✅ **添加 copyButton 选择器**：支持复制按钮功能
3. ✅ **应对 hash 变化**：不受 CSS Modules 打包影响

```typescript
export const monicaSelector: SelectorConfig = {
  // 使用属性选择器匹配含 hash 的类名
  inputField: ['textarea[data-input_node="monica-chat-input"]', ...],
  sendButton: ['[class*="input-msg-btn"]', ...],           // 匹配 input-msg-btn--xxx
  chatContentList: ['[class*="chat-items"]', ...],         // 匹配 chat-items--xxx
  promptItem: ['[class*="chat-question"]', ...],           // 匹配 chat-question--xxx
  answerItem: ['[class*="chat-reply"]', ...],              // 匹配 chat-reply--xxx
  markdown: ['[class*="markdown"].__markdown', ...],       // 结合类名匹配
  
  // 添加复制按钮选择器
  copyButton: [
    '[class*="code-enhance-copy"]',                        // 匹配代码块复制
    '[class*="message-toolbar"] [class*="copy-group"]',    // 匹配消息复制
    'button[aria-label*="复制"]'                           // 通用复制
  ]
};
```

**属性选择器说明**：
- `[class*="chat-items"]` 会匹配 `class="chat-items--etBvz"`
- `[class*="chat-items"]` 会匹配 `class="chat-items--xyz123"`  
- 不管 hash 如何变化，只要包含 "chat-items" 就能匹配

#### `extension/src/content/configs/SelectorManager.ts`
- 导入 `monicaSelector`
- 在 `getPlatformSelectors()` 方法中添加 Monica case

### 3. 服务层实现（4个文件）

#### `MonicaPageService.ts`
页面状态管理服务，主要功能：
- **URL 匹配规则**：
  - Home页: `/home/<USER>/{provider}/{model}` 无 `convId` 参数
  - Chat页: `/home/<USER>/{provider}/{model}?convId={chatId}` 有 `convId` 参数
- **chatId 提取**：从 URL 查询参数 `convId` 提取并 URL 解码
- **页面类型检测**：支持 URL 检测和 DOM 降级检测

```typescript
// URL 正则表达式
home: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+\/?(\?(?!.*convId=).*)?$/i
chat: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+\/?\?.*convId=([^&]+)/i

// chatId 提取（带 URL 解码）
const convId = urlParams.get('convId');
return decodeURIComponent(convId);
```

#### `MonicaClipboardService.ts`
剪贴板管理服务，主要功能：
- **使用父类复制按钮实现**：优先尝试复制按钮获取内容
- **自动降级机制**：复制失败时自动使用 HTML 转 Markdown
- **自定义 Turndown 规则**：
  - 处理 Monica 代码块格式（`<code class="hljs language-xx">`）
  - 处理 `<pre>` 包裹的代码块
- **注释掉覆盖方法**：不覆盖 `getClipboardContent()`，使用父类实现

```typescript
// 注释掉此方法，使用父类的复制按钮实现
// public async getClipboardContent(answerElement: Element): Promise<string> {
//     return await this.getFallbackClipboardContent(answerElement);
// }

// 仅实现降级方法，供父类在复制失败时调用
protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    // HTML 转 Markdown 逻辑
}
```

#### `MonicaAnswerService.ts`
答案管理服务，主要功能：
- 继承 `BaseAnswerService` 的完整功能
- 使用 `MonicaClipboardService` 进行内容提取
- 依赖父类处理答案完成信号

#### `MonicaAnswerController.ts`
答案控制器，主要功能：
- 创建和管理服务实例（`MonicaAnswerService` + `MonicaPageService`）
- 继承父类的完整答案处理流程
- 协调答案监听、提取和存储

### 4. 主适配器（1个文件）

#### `monica.ts`
Monica 平台主适配器：
- 继承 `BaseAIAdapter`
- 创建并初始化 `MonicaAnswerController`
- 实现资源清理方法 `destroy()`
- 支持多模型切换（Monica、GPT-5、Claude 等）

```typescript
export class MonicaAdapter extends BaseAIAdapter {
    private monicaAnswerController: MonicaAnswerController | null = null;
    
    constructor(platform: PlatformEntity) {
        super(platform);
        this.monicaAnswerController = new MonicaAnswerController();
        this.monicaAnswerController.init(this);
    }
    
    destroy(): void {
        super.destroy();
        this.monicaAnswerController?.destroy();
    }
}
```

---

## 🎯 关键设计决策

### 1. URL 匹配策略
**问题**: Home 页和 Chat 页路径相同，如何区分？  
**解决**: 通过 `convId` 查询参数区分
- 有 `convId` = Chat 页
- 无 `convId` = Home 页

### 2. chatId 提取方式
**问题**: `convId` 值包含特殊字符（URL 编码）  
**解决**: 使用 `decodeURIComponent()` 解码
- 输入: `conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208`
- 输出: `conv:257771e1-5b9b-4a41-b217-2aa336cd3208`

### 3. 剪贴板策略
**问题**: 是否使用复制按钮？  
**决策**: 使用复制按钮优先，降级到 HTML 转 Markdown
- 优先使用父类的复制按钮实现
- 复制失败时自动降级到 HTML 转 Markdown
- 利用 Turndown 的强大转换能力作为备选方案

### 4. 选择器 Hash 问题
**问题**: Monica 使用 CSS Modules，类名包含动态 hash（如 `--etBvz`）  
**解决**: 使用 CSS 属性选择器 `[class*="xxx"]` 进行部分匹配
- ❌ 错误方案1: `.chat-items--etBvz` （完整类名，hash 变化后失效）
- ❌ 错误方案2: `.chat-items` （直接去掉 hash，无法匹配实际类名）
- ✅ 正确方案: `[class*="chat-items"]` （属性选择器，部分匹配）

**原理**:
- 实际 DOM 中的类名: `chat-items--etBvz`（带 hash）
- `[class*="chat-items"]` 可以匹配任何包含 "chat-items" 的 class
- 不受打包后 hash 变化影响

**示例**:
```typescript
// 修改前（会失效）
promptItem: ['.chat-question--yrvtP']

// 修改后（稳定可靠）
promptItem: ['[class*="chat-question"]']
```

### 5. 代码块处理
**问题**: Monica 代码块格式特殊（`hljs` + `language-xxx`）  
**解决**: 自定义 Turndown 规则
```typescript
// 匹配 <code class="hljs language-dart">
filter: (node) => 
  node.nodeName === 'CODE' && 
  /hljs/.test(node.className) && 
  /language-/.test(node.className)
```

---

## 📊 实现统计

| 类型 | 数量 | 说明 |
|------|------|------|
| 新增文件 | 6 | 5个适配器文件 + 1个配置文件 |
| 修改文件 | 3 | dexie.ts, database_entity.ts, SelectorManager.ts |
| 代码行数 | ~500 | 包含注释和文档 |
| 单元测试 | 0 | 待添加 |

---

## 🏗️ 架构特点

### 1. 完全遵循统一架构
```
MonicaAdapter (主适配器)
    └── MonicaAnswerController (控制器)
            ├── MonicaAnswerService (答案服务)
            │       └── MonicaClipboardService (剪贴板服务)
            └── MonicaPageService (页面服务)
```

### 2. 最大化代码复用
- 继承 `BaseAIAdapter` 的完整功能
- 继承 `BaseAnswerController` 的流程控制
- 继承 `BaseAnswerService` 的监听机制
- 继承 `BasePageService` 的页面检测逻辑
- 继承 `BaseClipboardService` 的剪贴板操作

### 3. 职责清晰分离
- **Adapter**: 生命周期管理
- **Controller**: 流程协调
- **AnswerService**: 答案监听和提取
- **ClipboardService**: 内容转换
- **PageService**: 页面状态检测

---

## 🔍 与其他平台对比

### Monica vs ChatGPT
| 特性 | Monica | ChatGPT |
|------|--------|---------|
| URL 结构 | `/home/<USER>/{provider}/{model}?convId=xxx` | `/c/{chatId}` |
| Home/Chat 区分 | 查询参数 `convId` | URL 路径 |
| 复制按钮 | 支持（优先使用） | 支持（优先使用） |
| 代码块格式 | `hljs language-xxx` | 标准格式 |
| 多模型支持 | 是（URL中体现） | 否 |
| CSS Modules | 是（需去掉 hash） | 否 |

### 共同点
- 都使用 Markdown 渲染答案
- 都支持流式输出
- 都有消息列表结构
- 都需要 DOM 监听

---

## 🧪 测试计划

### 基础功能测试
- [ ] 适配器加载和初始化
- [ ] Home 页识别
- [ ] Chat 页识别
- [ ] chatId 提取和解码

### 答案捕获测试
- [ ] 用户提问监听
- [ ] AI 答案监听
- [ ] 答案完成检测
- [ ] HTML 转 Markdown 准确性

### 数据存储测试
- [ ] 提示词存储
- [ ] 答案存储
- [ ] 提示词-答案关联
- [ ] 多轮对话支持

### 边界情况测试
- [ ] 模型切换（Monica → GPT-5 → Claude）
- [ ] 长答案处理
- [ ] 含代码块的答案
- [ ] 含复杂格式的答案（表格、列表等）
- [ ] 网络错误处理
- [ ] DOM 变化异常处理

### 性能测试
- [ ] 大量消息加载性能
- [ ] DOM 监听性能影响
- [ ] 内存占用
- [ ] 长时间运行稳定性

---

## 🐛 已知问题

目前无已知问题。

---

## 📝 后续优化建议

1. **性能优化**
   - 考虑使用 IntersectionObserver 优化答案监听
   - 添加选择器缓存机制

2. **功能增强**
   - 支持提取模型提供商信息（如需要）
   - 支持导出多模型对比结果

3. **测试完善**
   - 添加单元测试
   - 添加集成测试
   - 添加 E2E 测试

4. **文档完善**
   - 添加开发者文档
   - 添加用户使用指南

---

## 🎉 总结

Monica 平台支持已完全实现并通过编译检查。实现遵循了项目的统一架构模式，代码质量高，易于维护。

**核心优势**：
- ✅ 架构统一，易于理解
- ✅ 代码复用度高
- ✅ 职责划分清晰
- ✅ 注释文档完善
- ✅ 零编译错误

**下一步**: 进行实际测试和调试优化。

---

**实现者**: GitHub Copilot  
**审核者**: 待定  
**状态**: ✅ 实现完成，待测试
