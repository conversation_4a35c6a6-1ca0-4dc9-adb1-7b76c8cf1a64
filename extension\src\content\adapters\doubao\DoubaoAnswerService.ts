import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";
import { DoubaoClipboardService } from "./DoubaoClipboardService";
import { AnswerModel } from "../../model/AnswerModel";

/**
 * Doubao (豆包) 答案管理服务
 * 继承 BaseAnswerService，提供 Doubao 平台特定的答案处理逻辑
 * 
 * 主要特性：
 * - 监听 DOM 变化检测答案更新
 * - 操作栏存在即视为答案区域（包括隐藏状态）
 * - 不依赖复制按钮，直接从 DOM 提取内容
 * - 集成 DoubaoClipboardService 使用 Turndown 转换
 * 
 * @Doubao特殊处理
 * - 历史消息的操作栏是 opacity-0（隐藏状态），但仍然存在
 * - 不需要点击复制按钮，只使用操作栏位置插入导出按钮
 * - 所有答案（包括历史消息）都需要显示导出按钮
 */
export class DoubaoAnswerService extends BaseAnswerService {
    private clipboardService: DoubaoClipboardService;

    constructor() {
        super();
        this.clipboardService = new DoubaoClipboardService();
    }

    /**
     * 获取剪贴板服务实例（BaseAnswerService 抽象方法实现）
     * @returns DoubaoClipboardService 实例
     */
    protected getClipboardService(): DoubaoClipboardService {
        return this.clipboardService;
    }

    protected getPromptDivHash(promptElement: Element): string|null {   
        const doubaoId =  promptElement.getAttribute('data-message-id');
        console.log('【DoubaoAnswerService】 问题元素 data-message-id:', doubaoId);
        return doubaoId;
    }

    protected getAnswerDivHash(answerElement: Element): string|null {   
        // 首先获得answerElement 下 data-testid="message_content" 的element
        // 然后从该element中获得 data-message-id
        const messageContent = answerElement.querySelector('[data-testid="message_content"]');
        if (messageContent) {
            const doubaoId = messageContent.getAttribute('data-message-id');
            console.log('【DoubaoAnswerService】 答案元素 data-message-id:', doubaoId);
            return doubaoId;
        }
        return null;
    }

    /**
     * 重写：处理已存在的答案（Doubao 特殊实现）
     * Doubao 平台导出按钮放在 message_content 底部
     * @param answerElement 答案元素
     */
    protected async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            console.info('【DoubaoAnswerService】Existing模式: 开始提取答案', answerElement);
            const selectors = SelectorManager.getSelector();

            // 1. 查找答案完成组件（操作栏） - 用于判断答案是否完成
            let completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (completionNode == null) {
                completionNode = await DOMUtils.asyncSelectElementInContainer(answerElement, selectors.answerCompletion, this.ANSWER_TIMEOUT);
            }
            if (completionNode == null) {
                console.warn(`【DoubaoAnswerService】Existing模式: 未找到操作栏组件`);
                return;
            }

            // 2. 查找 message_content 元素 - Doubao 平台导出按钮的插入位置
            const messageContent = answerElement.querySelector('[data-testid="message_content"]');
            if (!messageContent) {
                console.warn(`【DoubaoAnswerService】Existing模式: 未找到 message_content 元素`);
                return;
            }

            // 3. Doubao 平台不需要复制按钮，直接从 DOM 提取内容
            console.info(`【DoubaoAnswerService】Existing模式: 使用 DOM 提取方案（不依赖复制按钮）`);

            // 4. 使用剪贴板服务获取内容（不需要 copyButton 参数）
            const clipboardService = this.getClipboardService();
            const answerContent = await clipboardService.simulateClickAndGetContent(answerElement);

            if (!answerContent?.trim()) {
                console.warn(`【DoubaoAnswerService】Existing模式: 提取的答案内容为空`);
                return;
            }

            // 5. 保存到数据模型
            const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
            console.info(`【DoubaoAnswerService】Existing模式: 已保存，下标: ${answerIndex}`);

            // 6. 触发事件通知（使用 messageContent 作为导出按钮插入位置）
            this.dispatchAnswerExtractedEvent(messageContent, answerIndex);

        } catch (error) {
            console.error(`【DoubaoAnswerService】Existing模式: 提取失败`, error);
        }
    }

    /**
     * 重写：提取答案内容（Doubao 特殊实现）
     * Doubao 平台导出按钮放在 message_content 底部
     * @param answerElement 答案元素
     * @param context 上下文描述（用于日志）
     * @returns 答案内容或 null
     */
    protected async extractAnswerContent(answerElement: Element, context: string): Promise<string | null> {
        try {
            const selectors = SelectorManager.getSelector();

            // 1. 查找答案完成组件（操作栏） - 用于判断答案是否完成
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn(`【DoubaoAnswerService】 ${context}: 未找到操作栏组件`);
                return null;
            }

            // 2. 查找 message_content 元素 - Doubao 平台导出按钮的插入位置
            const messageContent = answerElement.querySelector('[data-testid="message_content"]');
            if (!messageContent) {
                console.warn(`【DoubaoAnswerService】 ${context}: 未找到 message_content 元素`);
                return null;
            }

            // 3. Doubao 平台不需要复制按钮，直接使用 DOM 提取
            console.info(`【DoubaoAnswerService】 ${context}: 使用 DOM 提取方案（不依赖复制按钮）`);

            // 4. 使用剪贴板服务获取内容（不需要 copyButton 参数）
            const clipboardService = this.getClipboardService();
            const answerContent = await clipboardService.simulateClickAndGetContent(answerElement);

            if (!answerContent?.trim()) {
                console.warn(`【DoubaoAnswerService】 ${context}: 提取的答案内容为空`);
                return null;
            }

            // 5. 保存到数据模型
            const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
            console.info(`【DoubaoAnswerService】 ${context} 已保存，下标: ${answerIndex}`);

            // 6. 触发事件通知（使用 messageContent 作为导出按钮插入位置）
            this.dispatchAnswerExtractedEvent(messageContent, answerIndex);

            return answerContent;
        } catch (error) {
            console.error(`【DoubaoAnswerService】 ${context}: 提取失败`, error);
            return null;
        }
    }

  /**********************************  ⬇️⬇️⬇️ 处理新生成  ⬇️⬇️⬇️ ***********************************/
  /**
     * 处理新节点（Doubao 特定实现，覆盖基类）
     * 增加了对生成中答案的特殊处理
     * @param node 新增的 DOM 节点
     */
  protected async handleNewNode(node: Element): Promise<void> {
    const selectors = SelectorManager.getSelector();

    // 检测问题节点
    if (selectors.promptItem?.some(sel => node.matches(sel))) {
        console.info('【DoubaoAnswerService】 检测到新的问题元素');
        this.generatingPromptElement = node;
        // 立即处理问题节点（问题内容通常是完整的）
        await this.processExistingPrompt(this.generatingPromptElement);
    } else {
      // 查找子节点中的问题元素
      const childPromptElements = DOMUtils.findElementAllInContainer(node, selectors.promptItem);
      if (childPromptElements?.length > 0) {
        console.info('【DoubaoAnswerService】 在子节点中检测到问题元素');
        this.generatingPromptElement = childPromptElements[0]; // 取第一个
        // 立即处理问题节点（问题内容通常是完整的）
        await this.processExistingPrompt(this.generatingPromptElement);
      }
    }

    // 检测答案节点
    if (selectors.answerItem?.some(sel => node.matches(sel))) {
      console.info('【DoubaoAnswerService】 检测到新的答案元素', node);
      // 检查是否是完成状态的答案（包含 answerCompletion 组件）
      const hasCompletion = DOMUtils.findElementInContainer(node, selectors.answerCompletion);
      if (hasCompletion) {
        console.info('【DoubaoAnswerService】 检测到完成状态的答案元素（包含answerCompletion），跳过MutationObserver监听，直接处理');
        // 直接作为已存在的答案处理
        await this.processExistingAnswer(node);
        return;
      }

      console.info('【DoubaoAnswerService】 开始监听新答案的生成过程');
      this.generatingAnswerElement = node;
      await this.processGeneratingAnswer(this.generatingAnswerElement);
    } else {
      // 查找子节点中的答案元素
      const childAnswerElements = DOMUtils.findElementAllInContainer(node, selectors.answerItem);
      if (childAnswerElements?.length > 0) {
        console.info('【DoubaoAnswerService】 在子节点中检测到答案元素');
        const answerElement = childAnswerElements[0];
        console.info('【DoubaoAnswerService】 子节点答案元素信息:', answerElement);
        this.generatingAnswerElement = answerElement;
        await this.processGeneratingAnswer(this.generatingAnswerElement);
      }
    }
  }

  /**
       * 监听答案完成状态并在完成后提取内容
       * @param answerElement 答案元素
       */
  protected async processGeneratingAnswer(answerElement: Element): Promise<void> {
    console.info('【DoubaoAnswerService】 开始监听答案生成完成状态');
    const startTime = Date.now();
    let mutationCount = 0; // 用于调试

    // 创建专门的监听器来监听答案完成状态
    const completionObserver = new MutationObserver((mutations) => {
      mutationCount++;
      console.info(`【DoubaoAnswerService】 检测到 DOM 变化 (第${mutationCount}次)`, {
        mutationsCount: mutations.length,
        types: mutations.map(m => m.type).join(',')
      });

      // 检查是否超时
      if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
        console.warn('【DoubaoAnswerService】 答案生成监听超时');
        completionObserver.disconnect();
        this.clearGeneratingElements();
        return;
      }

      // 检查答案是否完成（answerCompletion + copyButton）
      if (this.isAnswerComplete(answerElement)) {
        console.info('【DoubaoAnswerService】 检测到答案生成完成');
        completionObserver.disconnect();

        // 答案完成，开始提取内容
        this.extractCompletedAnswer(answerElement).catch(error => {
          console.error('【DoubaoAnswerService】 提取完成答案失败', error);
        });
      } else {
        console.info('【DoubaoAnswerService】 答案仍在生成中，继续监听...');
      }
    });

    // 使用增强的配置来监听 answerElement 及其子树的变化
    completionObserver.observe(answerElement, this.ANSWER_GENERATION_CONFIG);
    console.info('【DoubaoAnswerService】 已设置答案生成监听器，配置:', this.ANSWER_GENERATION_CONFIG);

    // 立即检查一次（可能答案已经完成了）
    if (this.isAnswerComplete(answerElement)) {
      console.info('【DoubaoAnswerService】 答案已经完成，立即处理');
      completionObserver.disconnect();
      await this.extractCompletedAnswer(answerElement);
    } else {
      console.info('【DoubaoAnswerService】 答案尚未完成，等待生成...');
    }
  }

}
