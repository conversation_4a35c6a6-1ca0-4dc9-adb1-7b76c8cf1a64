<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三套视觉方案对比 - AI工具会员解锁页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #111827;
            background: #F9FAFB;
        }

        /* ============================================================ */
        /* 导航切换 */
        /* ============================================================ */
        .scheme-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            z-index: 1000;
            padding: 16px 0;
        }

        .scheme-nav-inner {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .scheme-btn {
            padding: 8px 24px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 14px;
        }

        .scheme-btn:hover {
            border-color: #2563EB;
            transform: translateY(-2px);
        }

        .scheme-btn.active {
            background: #2563EB;
            color: white;
            border-color: #2563EB;
        }

        /* ============================================================ */
        /* 方案容器 */
        /* ============================================================ */
        .scheme-container {
            display: none;
            padding-top: 80px;
            min-height: 100vh;
        }

        .scheme-container.active {
            display: block;
        }

        .scheme-header {
            text-align: center;
            padding: 64px 24px 48px;
        }

        .scheme-title {
            font-size: 48px;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .scheme-subtitle {
            font-size: 18px;
            opacity: 0.7;
            max-width: 600px;
            margin: 0 auto;
        }

        .pricing-grid {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 24px 64px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }

        /* ============================================================ */
        /* 方案 1: 极简科技风 (Minimal Tech) */
        /* ============================================================ */
        #scheme1 {
            background: linear-gradient(135deg, #F9FAFB 0%, #EFF6FF 100%);
        }

        #scheme1 .scheme-title {
            color: #1E293B;
        }

        #scheme1 .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #E5E7EB;
        }

        #scheme1 .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 24px rgba(37,99,235,0.15);
            border-color: #2563EB;
        }

        #scheme1 .card-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 24px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 16px;
        }

        #scheme1 .badge-free {
            background: #F3F4F6;
            color: #6B7280;
        }

        #scheme1 .badge-pro {
            background: #DBEAFE;
            color: #2563EB;
        }

        #scheme1 .badge-plus {
            background: #E0E7FF;
            color: #4F46E5;
        }

        #scheme1 .badge-max {
            background: #CFFAFE;
            color: #0891B2;
        }

        #scheme1 .card-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1E293B;
        }

        #scheme1 .card-price {
            font-size: 48px;
            font-weight: 700;
            color: #2563EB;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            gap: 8px;
        }

        #scheme1 .card-price .currency {
            font-size: 24px;
            color: #64748B;
        }

        #scheme1 .card-period {
            color: #64748B;
            font-size: 14px;
            margin-bottom: 24px;
        }

        #scheme1 .card-features {
            list-style: none;
            margin-bottom: 32px;
        }

        #scheme1 .card-features li {
            padding: 12px 0;
            border-bottom: 1px solid #F3F4F6;
            color: #475569;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #scheme1 .card-features li:last-child {
            border-bottom: none;
        }

        #scheme1 .feature-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #2563EB;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
        }

        #scheme1 .card-cta {
            width: 100%;
            padding: 16px;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        #scheme1 .cta-primary {
            background: #2563EB;
            color: white;
        }

        #scheme1 .cta-primary:hover {
            background: #1D4ED8;
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(37,99,235,0.3);
        }

        #scheme1 .cta-secondary {
            background: white;
            color: #2563EB;
            border: 2px solid #2563EB;
        }

        #scheme1 .cta-secondary:hover {
            background: #EFF6FF;
            transform: translateY(-2px);
        }

        /* Popular card highlight */
        #scheme1 .pricing-card.popular {
            border: 2px solid #2563EB;
            position: relative;
        }

        #scheme1 .pricing-card.popular::before {
            content: 'MOST POPULAR';
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            background: #2563EB;
            color: white;
            padding: 4px 16px;
            border-radius: 24px;
            font-size: 11px;
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* ============================================================ */
        /* 方案 2: 玻璃拟态风 (Glassmorphism) */
        /* ============================================================ */
        #scheme2 {
            background: linear-gradient(135deg, #667EEA 0%, #764BA2 50%, #F093FB 100%);
            position: relative;
            overflow: hidden;
        }

        #scheme2::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -10%;
            width: 800px;
            height: 800px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            border-radius: 50%;
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            50% { transform: translate(-100px, 100px) scale(1.1); }
        }

        #scheme2 .scheme-title {
            color: white;
            text-shadow: 0 2px 20px rgba(0,0,0,0.2);
        }

        #scheme2 .scheme-subtitle {
            color: rgba(255,255,255,0.9);
        }

        #scheme2 .pricing-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 32px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #scheme2 .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 600ms;
        }

        #scheme2 .pricing-card:hover::before {
            left: 100%;
        }

        #scheme2 .pricing-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        #scheme2 .card-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 24px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.8px;
            margin-bottom: 16px;
            background: rgba(255,255,255,0.25);
            color: white;
            backdrop-filter: blur(10px);
        }

        #scheme2 .card-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            color: white;
        }

        #scheme2 .card-price {
            font-size: 56px;
            font-weight: 800;
            color: white;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            gap: 8px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        #scheme2 .card-price .currency {
            font-size: 28px;
            opacity: 0.8;
        }

        #scheme2 .card-period {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
            margin-bottom: 24px;
        }

        #scheme2 .card-features {
            list-style: none;
            margin-bottom: 32px;
        }

        #scheme2 .card-features li {
            padding: 14px 0;
            color: white;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            opacity: 0.95;
        }

        #scheme2 .feature-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            flex-shrink: 0;
            backdrop-filter: blur(10px);
        }

        #scheme2 .card-cta {
            width: 100%;
            padding: 18px;
            border-radius: 12px;
            border: none;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #scheme2 .cta-primary {
            background: white;
            color: #667EEA;
        }

        #scheme2 .cta-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 24px rgba(255,255,255,0.3);
        }

        #scheme2 .cta-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.4);
            backdrop-filter: blur(10px);
        }

        #scheme2 .cta-secondary:hover {
            background: rgba(255,255,255,0.3);
            border-color: white;
            transform: translateY(-3px);
        }

        #scheme2 .pricing-card.popular {
            background: rgba(255, 255, 255, 0.25);
            border: 2px solid rgba(255, 255, 255, 0.6);
            transform: scale(1.05);
        }

        /* ============================================================ */
        /* 方案 3: 深邃专业风 (Deep Professional) */
        /* ============================================================ */
        #scheme3 {
            background: #0F172A;
            position: relative;
            overflow: hidden;
        }

        #scheme3::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 400px;
            background: radial-gradient(ellipse at top, #1E3A8A 0%, transparent 60%);
            opacity: 0.6;
        }

        #scheme3 .scheme-title {
            color: white;
            background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 50%, #2563EB 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        #scheme3 .scheme-subtitle {
            color: #94A3B8;
        }

        #scheme3 .pricing-card {
            background: linear-gradient(135deg, #1E293B 0%, #0F172A 100%);
            border-radius: 16px;
            padding: 32px;
            border: 1px solid #334155;
            box-shadow: 0 0 0 1px rgba(59,130,246,0);
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        #scheme3 .pricing-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 16px;
            padding: 1px;
            background: linear-gradient(135deg, #60A5FA, #3B82F6);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 300ms;
        }

        #scheme3 .pricing-card:hover {
            transform: translateY(-8px);
            box-shadow: 
                0 0 40px rgba(59,130,246,0.3),
                0 0 80px rgba(59,130,246,0.2),
                0 20px 40px rgba(0,0,0,0.4);
        }

        #scheme3 .pricing-card:hover::after {
            opacity: 1;
        }

        #scheme3 .card-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 16px;
            position: relative;
        }

        #scheme3 .badge-free {
            background: rgba(100,116,139,0.2);
            color: #94A3B8;
            border: 1px solid #334155;
        }

        #scheme3 .badge-pro {
            background: rgba(59,130,246,0.15);
            color: #60A5FA;
            border: 1px solid rgba(59,130,246,0.3);
            box-shadow: 0 0 20px rgba(59,130,246,0.2);
        }

        #scheme3 .badge-plus {
            background: rgba(99,102,241,0.15);
            color: #818CF8;
            border: 1px solid rgba(99,102,241,0.3);
            box-shadow: 0 0 20px rgba(99,102,241,0.2);
        }

        #scheme3 .badge-max {
            background: rgba(34,211,238,0.15);
            color: #22D3EE;
            border: 1px solid rgba(34,211,238,0.3);
            box-shadow: 0 0 20px rgba(34,211,238,0.2);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        #scheme3 .card-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #F1F5F9;
        }

        #scheme3 .card-price {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 4px;
            display: flex;
            align-items: baseline;
            gap: 8px;
            background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        #scheme3 .card-price .currency {
            font-size: 24px;
        }

        #scheme3 .card-period {
            color: #64748B;
            font-size: 14px;
            margin-bottom: 24px;
        }

        #scheme3 .card-features {
            list-style: none;
            margin-bottom: 32px;
        }

        #scheme3 .card-features li {
            padding: 12px 0;
            border-bottom: 1px solid #1E293B;
            color: #CBD5E1;
            font-size: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #scheme3 .card-features li:last-child {
            border-bottom: none;
        }

        #scheme3 .feature-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            box-shadow: 0 0 10px rgba(59,130,246,0.5);
        }

        #scheme3 .card-cta {
            width: 100%;
            padding: 16px;
            border-radius: 8px;
            border: none;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 250ms cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        #scheme3 .cta-primary {
            background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
            color: white;
            box-shadow: 0 4px 20px rgba(59,130,246,0.4);
        }

        #scheme3 .cta-primary::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            transform: translate(-50%, -50%);
            transition: width 600ms, height 600ms;
        }

        #scheme3 .cta-primary:hover::before {
            width: 300px;
            height: 300px;
        }

        #scheme3 .cta-primary:hover {
            box-shadow: 0 8px 40px rgba(59,130,246,0.6);
            transform: translateY(-2px);
        }

        #scheme3 .cta-secondary {
            background: transparent;
            color: #60A5FA;
            border: 2px solid #334155;
        }

        #scheme3 .cta-secondary:hover {
            background: rgba(59,130,246,0.1);
            border-color: #3B82F6;
            transform: translateY(-2px);
        }

        #scheme3 .pricing-card.popular {
            border: 1px solid #3B82F6;
            background: linear-gradient(135deg, #1E293B 0%, #1E3A8A 100%);
        }

        #scheme3 .pricing-card.popular::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #60A5FA, #3B82F6, #2563EB);
            border-radius: 16px 16px 0 0;
        }
    </style>
</head>
<body>
    <!-- 方案切换导航 -->
    <nav class="scheme-nav">
        <div class="scheme-nav-inner">
            <button class="scheme-btn active" onclick="switchScheme(1)">方案1: 极简科技风</button>
            <button class="scheme-btn" onclick="switchScheme(2)">方案2: 玻璃拟态风</button>
            <button class="scheme-btn" onclick="switchScheme(3)">方案3: 深邃专业风</button>
        </div>
    </nav>

    <!-- ============================================================ -->
    <!-- 方案 1: 极简科技风 (Minimal Tech) -->
    <!-- ============================================================ -->
    <div id="scheme1" class="scheme-container active">
        <header class="scheme-header">
            <h1 class="scheme-title">选择适合您的方案</h1>
            <p class="scheme-subtitle">解锁AI助手的全部潜力，提升工作效率</p>
        </header>

        <div class="pricing-grid">
            <!-- Free -->
            <div class="pricing-card">
                <span class="card-badge badge-free">Free</span>
                <h2 class="card-title">免费版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>0</span>
                </div>
                <p class="card-period">永久免费</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日10次AI对话</li>
                    <li><span class="feature-icon">✓</span> 基础内容导出</li>
                    <li><span class="feature-icon">✓</span> 7天历史记录</li>
                    <li><span class="feature-icon">✓</span> 社区支持</li>
                </ul>
                <button class="card-cta cta-secondary">开始使用</button>
            </div>

            <!-- Pro -->
            <div class="pricing-card popular">
                <span class="card-badge badge-pro">Pro</span>
                <h2 class="card-title">专业版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>29</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日100次AI对话</li>
                    <li><span class="feature-icon">✓</span> Markdown导出</li>
                    <li><span class="feature-icon">✓</span> 30天历史记录</li>
                    <li><span class="feature-icon">✓</span> 优先响应支持</li>
                    <li><span class="feature-icon">✓</span> 自定义提示词</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Plus -->
            <div class="pricing-card">
                <span class="card-badge badge-plus">Plus</span>
                <h2 class="card-title">增强版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>69</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 无限AI对话</li>
                    <li><span class="feature-icon">✓</span> 全格式导出</li>
                    <li><span class="feature-icon">✓</span> 无限历史记录</li>
                    <li><span class="feature-icon">✓</span> 1对1专属支持</li>
                    <li><span class="feature-icon">✓</span> API访问权限</li>
                    <li><span class="feature-icon">✓</span> 团队协作功能</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Max -->
            <div class="pricing-card">
                <span class="card-badge badge-max">Max</span>
                <h2 class="card-title">旗舰版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>199</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> Plus全部功能</li>
                    <li><span class="feature-icon">✓</span> GPT-4优先访问</li>
                    <li><span class="feature-icon">✓</span> 私有部署选项</li>
                    <li><span class="feature-icon">✓</span> 专属客户经理</li>
                    <li><span class="feature-icon">✓</span> 定制化开发</li>
                    <li><span class="feature-icon">✓</span> 企业级SLA保障</li>
                    <li><span class="feature-icon">✓</span> 高级数据分析</li>
                </ul>
                <button class="card-cta cta-primary">联系我们</button>
            </div>
        </div>
    </div>

    <!-- ============================================================ -->
    <!-- 方案 2: 玻璃拟态风 (Glassmorphism) -->
    <!-- ============================================================ -->
    <div id="scheme2" class="scheme-container">
        <header class="scheme-header">
            <h1 class="scheme-title">释放AI的无限可能</h1>
            <p class="scheme-subtitle">从入门到专业，我们为每个阶段的您准备了完美方案</p>
        </header>

        <div class="pricing-grid">
            <!-- Free -->
            <div class="pricing-card">
                <span class="card-badge">Free</span>
                <h2 class="card-title">免费版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>0</span>
                </div>
                <p class="card-period">永久免费</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日10次AI对话</li>
                    <li><span class="feature-icon">✓</span> 基础内容导出</li>
                    <li><span class="feature-icon">✓</span> 7天历史记录</li>
                    <li><span class="feature-icon">✓</span> 社区支持</li>
                </ul>
                <button class="card-cta cta-secondary">开始使用</button>
            </div>

            <!-- Pro -->
            <div class="pricing-card popular">
                <span class="card-badge">Pro</span>
                <h2 class="card-title">专业版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>29</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日100次AI对话</li>
                    <li><span class="feature-icon">✓</span> Markdown导出</li>
                    <li><span class="feature-icon">✓</span> 30天历史记录</li>
                    <li><span class="feature-icon">✓</span> 优先响应支持</li>
                    <li><span class="feature-icon">✓</span> 自定义提示词</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Plus -->
            <div class="pricing-card">
                <span class="card-badge">Plus</span>
                <h2 class="card-title">增强版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>69</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 无限AI对话</li>
                    <li><span class="feature-icon">✓</span> 全格式导出</li>
                    <li><span class="feature-icon">✓</span> 无限历史记录</li>
                    <li><span class="feature-icon">✓</span> 1对1专属支持</li>
                    <li><span class="feature-icon">✓</span> API访问权限</li>
                    <li><span class="feature-icon">✓</span> 团队协作功能</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Max -->
            <div class="pricing-card">
                <span class="card-badge">Max</span>
                <h2 class="card-title">旗舰版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>199</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> Plus全部功能</li>
                    <li><span class="feature-icon">✓</span> GPT-4优先访问</li>
                    <li><span class="feature-icon">✓</span> 私有部署选项</li>
                    <li><span class="feature-icon">✓</span> 专属客户经理</li>
                    <li><span class="feature-icon">✓</span> 定制化开发</li>
                    <li><span class="feature-icon">✓</span> 企业级SLA保障</li>
                    <li><span class="feature-icon">✓</span> 高级数据分析</li>
                </ul>
                <button class="card-cta cta-primary">联系我们</button>
            </div>
        </div>
    </div>

    <!-- ============================================================ -->
    <!-- 方案 3: 深邃专业风 (Deep Professional) -->
    <!-- ============================================================ -->
    <div id="scheme3" class="scheme-container">
        <header class="scheme-header">
            <h1 class="scheme-title">专业AI工具 · 精准定价</h1>
            <p class="scheme-subtitle">为不同规模的团队和个人提供灵活的解决方案</p>
        </header>

        <div class="pricing-grid">
            <!-- Free -->
            <div class="pricing-card">
                <span class="card-badge badge-free">Free</span>
                <h2 class="card-title">免费版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>0</span>
                </div>
                <p class="card-period">永久免费</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日10次AI对话</li>
                    <li><span class="feature-icon">✓</span> 基础内容导出</li>
                    <li><span class="feature-icon">✓</span> 7天历史记录</li>
                    <li><span class="feature-icon">✓</span> 社区支持</li>
                </ul>
                <button class="card-cta cta-secondary">开始使用</button>
            </div>

            <!-- Pro -->
            <div class="pricing-card popular">
                <span class="card-badge badge-pro">Pro</span>
                <h2 class="card-title">专业版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>29</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 每日100次AI对话</li>
                    <li><span class="feature-icon">✓</span> Markdown导出</li>
                    <li><span class="feature-icon">✓</span> 30天历史记录</li>
                    <li><span class="feature-icon">✓</span> 优先响应支持</li>
                    <li><span class="feature-icon">✓</span> 自定义提示词</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Plus -->
            <div class="pricing-card">
                <span class="card-badge badge-plus">Plus</span>
                <h2 class="card-title">增强版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>69</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> 无限AI对话</li>
                    <li><span class="feature-icon">✓</span> 全格式导出</li>
                    <li><span class="feature-icon">✓</span> 无限历史记录</li>
                    <li><span class="feature-icon">✓</span> 1对1专属支持</li>
                    <li><span class="feature-icon">✓</span> API访问权限</li>
                    <li><span class="feature-icon">✓</span> 团队协作功能</li>
                </ul>
                <button class="card-cta cta-primary">立即升级</button>
            </div>

            <!-- Max -->
            <div class="pricing-card">
                <span class="card-badge badge-max">Max</span>
                <h2 class="card-title">旗舰版</h2>
                <div class="card-price">
                    <span class="currency">¥</span>
                    <span>199</span>
                </div>
                <p class="card-period">每月</p>
                <ul class="card-features">
                    <li><span class="feature-icon">✓</span> Plus全部功能</li>
                    <li><span class="feature-icon">✓</span> GPT-4优先访问</li>
                    <li><span class="feature-icon">✓</span> 私有部署选项</li>
                    <li><span class="feature-icon">✓</span> 专属客户经理</li>
                    <li><span class="feature-icon">✓</span> 定制化开发</li>
                    <li><span class="feature-icon">✓</span> 企业级SLA保障</li>
                    <li><span class="feature-icon">✓</span> 高级数据分析</li>
                </ul>
                <button class="card-cta cta-primary">联系我们</button>
            </div>
        </div>
    </div>

    <script>
        function switchScheme(schemeNumber) {
            // 隐藏所有方案
            document.querySelectorAll('.scheme-container').forEach(container => {
                container.classList.remove('active');
            });

            // 移除所有按钮的active状态
            document.querySelectorAll('.scheme-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 显示选中的方案
            document.getElementById('scheme' + schemeNumber).classList.add('active');

            // 激活对应按钮
            document.querySelectorAll('.scheme-btn')[schemeNumber - 1].classList.add('active');
        }
    </script>
</body>
</html>