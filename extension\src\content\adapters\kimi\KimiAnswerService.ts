import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import { KimiClipboardService } from "./KimiClipboardService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Kimi 答案捕获服务
 * 继承 BaseAnswerService，提供 Kimi 平台特定的答案处理逻辑
 * 大部分逻辑已在基类实现，此处只保留 Kimi 特定的功能
 */
export class KimiAnswerService extends BaseAnswerService {
    // Kimi 平台的剪贴板服务实例
    private clipboardService: KimiClipboardService;

    constructor() {
        super();
        this.clipboardService = new KimiClipboardService();
    }

    /**
     * 获取剪贴板服务实例
     */
    protected getClipboardService(): BaseClipboardService {
        return this.clipboardService;
    }
}
