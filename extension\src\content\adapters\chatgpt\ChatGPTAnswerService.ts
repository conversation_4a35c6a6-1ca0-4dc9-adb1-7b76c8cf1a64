import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";
import { ChatGPTClipboardService } from "./ChatGPTClipboardService";

/**
 * ChatGPT 答案管理服务
 * 继承 BaseAnswerService，提供 ChatGPT 平台特定的答案处理逻辑
 * 
 * 主要特性：
 * - 监听 DOM 变化检测答案更新
 * - 使用复制按钮出现作为答案完成信号
 * - 集成 ChatGPTClipboardService 提取答案内容
 */
export class ChatGPTAnswerService extends BaseAnswerService {
    private clipboardService: ChatGPTClipboardService;
    // private lastProcessedAnswer: Element | null = null;  // 记录上次处理的答案元素

    constructor() {
        super();
        this.clipboardService = new ChatGPTClipboardService();
    }

    /**
     * 获取剪贴板服务实例（BaseAnswerService 抽象方法实现）
     * @returns ChatGPTClipboardService 实例
     */
    protected getClipboardService(): ChatGPTClipboardService {
        return this.clipboardService;
    }
}
