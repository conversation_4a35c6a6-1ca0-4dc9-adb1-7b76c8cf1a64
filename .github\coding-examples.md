# 编码规范示例

## 1. 事件监听器使用箭头函数

### ❌ 错误示例（会导致内存泄漏）
```typescript
export class MyComponent {
    constructor() {
        // 错误：使用 bind(this) 会在添加和移除时创建不同的函数引用
        document.addEventListener('click', this.handleClick.bind(this));
    }
    
    private handleClick(event: Event): void {
        console.log('Clicked');
    }
    
    public destroy(): void {
        // ⚠️ 这里无法移除事件监听器，因为 bind 创建了新的函数引用
        document.removeEventListener('click', this.handleClick.bind(this));
    }
}
```

### ✅ 正确示例（使用箭头函数）
```typescript
export class MyComponent {
    constructor() {
        // 正确：箭头函数自动绑定 this，且引用一致
        document.addEventListener('click', this.handleClick);
    }
    
    // 使用箭头函数作为类字段
    private handleClick = (event: Event): void => {
        console.log('Clicked');
    }
    
    public destroy(): void {
        // ✅ 可以正确移除事件监听器
        document.removeEventListener('click', this.handleClick);
    }
}
```

### 📝 要点说明
- 箭头函数会自动捕获定义时的 `this` 上下文
- 箭头函数作为类字段时，每个实例都有自己的函数副本
- 可以直接用于 `addEventListener` 和 `removeEventListener`
- 避免了 `.bind(this)` 带来的内存泄漏问题

---

## 2. DOM 选择器包含层级信息

### ❌ 错误示例（选择器过于简单）
```typescript
// 不推荐：选择器太简单，容易匹配错误元素
const selectors = {
    button: '.btn',
    content: '.content',
    title: 'h1'
};

// 问题：页面可能有多个 .btn，容易误选
const button = document.querySelector('.btn');
```

### ✅ 正确示例（包含层级关系）
```typescript
// 推荐：选择器包含父级和子级信息
const selectors = {
    // 包含容器和按钮的层级关系
    exportButton: 'div.answer-container > div.button-group > button.export-btn',
    
    // 包含多个层级确保精确定位
    answerContent: 'div.chat-message[data-type="answer"] > div.message-body > div.content',
    
    // 使用组合选择器提高准确性
    copyButton: [
        'div.completion-wrapper button[aria-label="Copy"]',
        'div.message-actions button.copy-button',
        'button.icon-button[data-action="copy"]'
    ]
};

// 使用时更精确
const button = document.querySelector(selectors.exportButton);
```

### 📝 要点说明
- 选择器应包含至少 2-3 层的 DOM 结构信息
- 优先使用类名组合：`div.parent > span.child`
- 可以使用属性选择器增强准确性：`button[aria-label="Copy"]`
- 复杂场景提供多个备选选择器（数组形式）
- 避免使用过于通用的选择器如 `div`、`.button`

### 🎯 选择器层级示例对比

| 场景 | ❌ 简单选择器 | ✅ 层级选择器 |
|-----|-------------|--------------|
| 复制按钮 | `.copy-btn` | `div.message-actions > button.copy-btn` |
| 答案内容 | `.content` | `div[data-message-type="ai"] > div.message-body > .content` |
| 导出按钮 | `button` | `div.completion-container button.export-markdown` |
| 问题文本 | `.question` | `div.chat-item[data-role="user"] > div.text-content > p.question` |

### 💡 最佳实践
1. 从容器开始，逐层向下定位
2. 使用语义化的类名和属性
3. 避免依赖索引（如 `:nth-child(2)`），因为页面结构可能变化
4. 提供多个备选方案处理不同页面版本
5. 使用 `data-*` 属性作为稳定的锚点

---

## 应用场景

这两条规则主要应用于以下场景：

### 事件监听器规则适用于：
- ✅ Content Script 中的 DOM 事件监听
- ✅ 导出注入器（ObsidianExportInject、NotionExportInject 等）
- ✅ 浮动气泡拖拽服务（FloatingBubbleDragService）
- ✅ 任何需要动态添加/移除事件监听的场景

### DOM 选择器规则适用于：
- ✅ SelectorManager 配置文件
- ✅ 平台适配器的选择器定义
- ✅ DOMUtils 查找元素
- ✅ 任何需要定位特定 DOM 元素的场景
