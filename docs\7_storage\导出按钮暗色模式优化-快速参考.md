# 🎨 导出按钮暗色模式优化 - 快速参考

## 问题
暗色模式下，Markdown 和 Notion 按钮的 SVG 图标为黑色，在暗色背景下不可见。

## 解决方案
**CSS filter + 动态颜色变量**，无需修改 SVG。

## 核心代码

```css
/* 定义颜色变量 */
:root {
  --icon-color: #374151;           /* 亮色模式 - 灰色 */
  --icon-hover: #000000;           /* 亮色模式 - 黑色 */
  --icon-stroke: rgba(255, 255, 255, 0.2);
}

@media (prefers-color-scheme: dark) {
  :root {
    --icon-color: #9CA3AF;         /* 暗色模式 - 浅灰 */
    --icon-hover: #FFFFFF;         /* 暗色模式 - 白色 */
    --icon-stroke: rgba(255, 255, 255, 0.15);
  }
}

/* 应用样式 */
.export-btn {
  color: var(--icon-color);
  filter: drop-shadow(0 0 1px var(--icon-stroke));  /* 关键：添加描边 */
}

.export-btn:hover {
  color: var(--icon-hover);
  filter: drop-shadow(0 0 2px var(--icon-stroke));  /* 悬浮加强 */
}
```

## 效果
- ✅ 自动适配亮色/暗色模式
- ✅ 图标始终清晰可见
- ✅ 悬浮时增强对比度
- ✅ 零性能开销

## 修改文件
- `MarkdownExportButton.css` ✅
- `NotionExportButton.css` ✅
- `ObsidianExportButton.css` （无需修改，已有彩色）

## 测试
```bash
cd extension && npm run dev
```
切换系统主题，检查按钮在亮色/暗色模式下的显示效果。
