# GitHub Copilot 项目指令 · 架构与命名规范

## 目录结构
```
extension/src/
├── background/       # Service Worker
├── content/
│   ├── adapters/     # 平台适配器（Kimi、ChatGPT等）
│   ├── configs/      # 配置文件
│   ├── core/         # 核心基础类
│   ├── inject/       # 注入组件（气泡、模态框等）
│   ├── model/        # 数据模型
│   ├── utils/        # 工具类
│   └── index.ts
├── popup/            # 弹窗页面
├── options/          # 设置页面
└── common/           # 共享代码
```

## 命名规范核心规则

| 后缀 | 模式 | 位置 | 职责 |
|------|------|------|------|
| `Manager` | 单例 | `core/` 或 `utils/` | 管理和协调组件 |
| `Service` | 非单例 | `adapters/` 或 `common/service/` | 业务逻辑实现 |
| `Controller` | 非单例 | `adapters/` | 流程控制，协调多个 Service |
| `Utils` | 静态工具 | `utils/` | 无状态工具方法 |
| `Inject` | 注入组件 | `inject/` | UI 注入到页面 |
| `Model` | 数据模型 | `model/` | 数据结构定义 |

## 架构模式
- **单例**: Manager 继承 `Singleton` 或自己实现
- **继承**: Service/Controller 使用基类+子类
- **适配器**: 不同平台用独立 adapter
- **消息传递**: 统一用 `MessagingService`
- **存储**: SettingsStorage (sync) / IndexedDB (大数据)
