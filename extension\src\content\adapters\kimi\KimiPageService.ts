import { BasePageService } from "@/content/core/BasePageService";

/**
 * Kimi 页面状态管理服务
 * 继承 BasePageService，提供 Kimi 平台特定的 URL 匹配规则
 */
export class KimiPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Kimi';
    }

    /**
     * 获取 Kimi 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://www.kimi.com 或 https://www.kimi.com/
            home: /^https:\/\/www\.kimi\.com\/?$/i,
            // Chat页模式: https://www.kimi.com/chat/{chat_id}
            chat: /^https:\/\/www\.kimi\.com\/chat\/([a-zA-Z0-9]+)$/i
        };
    }

    /**
     * 检测欢迎页面（Kimi 特定实现，可使用 DOM 降级检测）
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测
        return document.querySelector('.home-page') !== null;
    }
    
    /**
     * 检测聊天页面（Kimi 特定实现，可使用 DOM 降级检测）
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测
        return document.querySelector('.chat-page.chat') !== null;
    }
}
