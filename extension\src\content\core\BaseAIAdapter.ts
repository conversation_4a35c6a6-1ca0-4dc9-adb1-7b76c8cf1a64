import { PlatformEntity } from "@/common/types/database_entity";
import { FloatingBubbleInject } from "@/content/inject/FloatingBubbleInject";
import { ArchiveButtonInject } from "@/content/inject/ArchiveButtonInject";
import { InputCapture } from "@/content/capture/InputCapture";
import { DOMUtils } from "@/content/utils/DOMUtils";
import SelectorManager from "@/content/configs/SelectorManager";
import { InputModel } from "../model/InputModel";


/**
 * 重构后的AI适配器基类
 * 作为 content 模块的核心应用上下文，统一管理组件间交互
 * 集成依赖注入容器，支持模块解耦
 */
export abstract class BaseAIAdapter {
  // 平台配置
  protected currentPlatform: PlatformEntity = null;
  

  // 捕捉页面信息模块
  protected inputCapture: InputCapture;

  // 注入UI组件模块
  protected floatingBubbleInject: FloatingBubbleInject;
  protected archiveButtonInject: ArchiveButtonInject;

  constructor(platform: PlatformEntity) {
    this.currentPlatform = platform;
    SelectorManager.initSelector(this.currentPlatform);
    console.log('【BaseAIAdapter】created for:', this.currentPlatform.name);
  }


  /**
   * 公共初始化方法，等待DOM元素可用后再初始化
   */
  public async initialize(): Promise<void> {
    console.log('【BaseAIAdapter】Starting BaseAIAdapter initialization for:', this.currentPlatform.name);

    try {
      // 等待关键DOM元素可用
      await this.waitForKeyElements();

      // 初始化各个模块
      this.initCapture();
      this.initInject();

      console.log('【BaseAIAdapter】BaseAIAdapter initialization completed for:', this.currentPlatform.name);
    } catch (error) {
      console.error('【BaseAIAdapter】BaseAIAdapter initialization failed:', error);
    }
  }

  /**
   * 等待关键DOM元素可用
   */
  private async waitForKeyElements(): Promise<void> {
    await DOMUtils.waitForKeyElements(SelectorManager.getSelector().inputField);
    console.info("【BaseAIAdapter】Key elements found");
  }



  /**
   * 初始化捕捉模块
   */
  protected initCapture() {
    // 获取InputModel单例实例
    this.inputCapture = new InputCapture(this);
  }

  /**
   * 初始化注入模块
   */
  private initInject() {
    this.floatingBubbleInject = new FloatingBubbleInject(this);
    // 获取InputModel单例实例
    this.archiveButtonInject = new ArchiveButtonInject(this);
  }

  /**
   * 设置当前平台信息
   */
  setCurrentPlatform(platform: PlatformEntity): void {
    this.currentPlatform = platform;
  }
  public getCurrentPlatform(): PlatformEntity {
    return this.currentPlatform;
  }

  public getInputCapture(): InputCapture {
    return this.inputCapture;
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    console.log(`【BaseAIAdapter】Destroying ${this.currentPlatform.name} adapter...`);

    // 清理UI组件
    if (this.floatingBubbleInject) {
      this.floatingBubbleInject.destroy();
    }
    if (this.archiveButtonInject) {
      this.archiveButtonInject.destroy();
    }

    // 清理捕捉模块
    if (this.inputCapture) {
      this.inputCapture.destroy();
    }

    // Singleton基类会自动管理实例生命周期
    InputModel.getInstance().clear();
    console.log(`【EchoSync】${this.currentPlatform.name} adapter destroyed`);
  }
}
