import { BaseAIAdapter } from '@/content/core/BaseAIAdapter';
import { GeminiAnswerController } from '@/content/adapters/gemini/GeminiAnswerController';
import { PlatformEntity } from '@/common/types/database_entity';

/**
 * Gemini 平台适配器
 * 继承 BaseAIAdapter，提供 Gemini 平台的完整集成
 * 
 * 主要特性：
 * - 自动检测 Gemini 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 支持 Gemini 的 Angular Material 组件
 * - 处理 KaTeX 数学公式和代码块
 * - 优先使用复制按钮，自动降级到 DOM 提取
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new GeminiAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class GeminiAdapter extends BaseAIAdapter {
    private geminiAnswerController: GeminiAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【GeminiAdapter】GeminiAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.geminiAnswerController = new GeminiAnswerController();
        this.geminiAnswerController.init(this);
        
        console.log('【GeminiAdapter】GeminiAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【GeminiAdapter】GeminiAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 GeminiAnswerController
            if (this.geminiAnswerController) {
                this.geminiAnswerController.destroy();
                this.geminiAnswerController = null;
                console.log('【GeminiAdapter】GeminiAnswerController 清理完成');
            }
            
            console.log('【GeminiAdapter】GeminiAdapter 销毁完成');
        } catch (error) {
            console.error('【GeminiAdapter】销毁 GeminiAdapter 时出错:', error);
        }
    }
}
