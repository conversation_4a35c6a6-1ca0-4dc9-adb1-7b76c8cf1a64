import React, { useState, useEffect } from 'react';
import { localStorageService, NotionConfig } from '@/common/service/LocalStorageService';
import { MessageType } from '@/common/types/enums';
import { NotionConfigValidation } from '@/common/types/NotionTypes';
import { StatusIndicator } from './button';

/**
 * Notion 配置区域组件
 * 用于 Popup 设置页面，管理 Notion 导出配置
 */
export function NotionConfigSection() {
    const [config, setConfig] = useState<NotionConfig | null>(null);
    const [loading, setLoading] = useState(true);
    const [showEditForm, setShowEditForm] = useState(false);
    const [validating, setValidating] = useState(false);
    const [validationMessage, setValidationMessage] = useState<{
        type: 'success' | 'error';
        text: string;
    } | null>(null);

    // 表单状态
    const [formToken, setFormToken] = useState('');
    const [formPageUrl, setFormPageUrl] = useState('');

    // 加载配置
    useEffect(() => {
        loadConfig();
    }, []);

    const loadConfig = async () => {
        try {
            setLoading(true);
            const loadedConfig = await localStorageService.getNotionConfig();
            setConfig(loadedConfig);
        } catch (error) {
            console.error('[NotionConfigSection] 加载配置失败', error);
        } finally {
            setLoading(false);
        }
    };

    // 解析 Page URL
    const parsePageUrl = (url: string): { pageId: string; pageName: string } | null => {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            const segments = pathname.split('/').filter(Boolean);
            
            if (segments.length === 0) {
                return null;
            }

            const lastSegment = segments[segments.length - 1];
            const idMatch = lastSegment.match(/([a-f0-9]{32}|[a-f0-9-]{36})$/i);
            
            if (!idMatch) {
                return null;
            }

            const pageId = idMatch[1].replace(/-/g, '');
            let pageName = lastSegment.substring(0, lastSegment.length - idMatch[0].length);
            pageName = pageName.replace(/-$/, '').replace(/-/g, ' ');

            if (!pageName) {
                pageName = 'Notion Page';
            }

            return { pageId, pageName };
        } catch (error) {
            return null;
        }
    };

    // 验证配置
    const validateConfig = async (configToValidate: NotionConfig): Promise<NotionConfigValidation> => {
        return new Promise((resolve) => {
            chrome.runtime.sendMessage(
                {
                    type: MessageType.NOTION_VALIDATE_CONFIG,
                    payload: configToValidate
                },
                (response) => {
                    if (response?.success) {
                        resolve(response.data);
                    } else {
                        resolve({
                            valid: false,
                            error: response?.error || '验证失败'
                        });
                    }
                }
            );
        });
    };

    // 处理保存配置
    const handleSaveConfig = async () => {
        // 验证输入
        if (!formToken.trim() || !formPageUrl.trim()) {
            setValidationMessage({
                type: 'error',
                text: '请填写所有必填项'
            });
            return;
        }

        // 解析 URL
        const pageInfo = parsePageUrl(formPageUrl);
        if (!pageInfo) {
            setValidationMessage({
                type: 'error',
                text: 'Page URL 格式不正确'
            });
            return;
        }

        setValidating(true);
        setValidationMessage(null);

        try {
            // 构建配置
            const newConfig: NotionConfig = {
                token: formToken,
                pageId: pageInfo.pageId,
                pageName: pageInfo.pageName
            };

            // 验证配置
            const validation = await validateConfig(newConfig);

            if (validation.valid) {
                // 保存配置
                await localStorageService.saveNotionConfig(newConfig);
                setConfig(newConfig);
                setShowEditForm(false);
                setValidationMessage({
                    type: 'success',
                    text: '配置保存成功'
                });
                
                // 3秒后清除成功消息
                setTimeout(() => {
                    setValidationMessage(null);
                }, 3000);
            } else {
                setValidationMessage({
                    type: 'error',
                    text: validation.error || '验证失败'
                });
            }
        } catch (error) {
            setValidationMessage({
                type: 'error',
                text: error instanceof Error ? error.message : '保存失败'
            });
        } finally {
            setValidating(false);
        }
    };

    // 测试连接
    const handleTestConnection = async () => {
        if (!config) return;

        setValidating(true);
        setValidationMessage(null);

        try {
            const validation = await validateConfig(config);
            
            if (validation.valid) {
                setValidationMessage({
                    type: 'success',
                    text: '连接成功！'
                });
                // 更新验证时间
                await localStorageService.updateLastVerified();
            } else {
                setValidationMessage({
                    type: 'error',
                    text: validation.error || '连接失败'
                });
            }
        } catch (error) {
            setValidationMessage({
                type: 'error',
                text: '测试连接失败'
            });
        } finally {
            setValidating(false);
        }
    };

    // 清除配置
    const handleClearConfig = async () => {
        if (!confirm('确定要清除 Notion 配置吗？')) {
            return;
        }

        try {
            await localStorageService.clearNotionConfig();
            setConfig(null);
            setValidationMessage({
                type: 'success',
                text: '配置已清除'
            });
            setTimeout(() => {
                setValidationMessage(null);
            }, 3000);
        } catch (error) {
            setValidationMessage({
                type: 'error',
                text: '清除配置失败'
            });
        }
    };

    // 开始编辑
    const handleStartEdit = () => {
        if (config) {
            setFormToken(config.token);
            setFormPageUrl(`https://www.notion.so/${config.pageName}-${config.pageId}`);
        } else {
            setFormToken('');
            setFormPageUrl('');
        }
        setShowEditForm(true);
        setValidationMessage(null);
    };

    // 取消编辑
    const handleCancelEdit = () => {
        setShowEditForm(false);
        setFormToken('');
        setFormPageUrl('');
        setValidationMessage(null);
    };

    if (loading) {
        return <div style={{ padding: '16px', textAlign: 'center' }}>加载中...</div>;
    }

    return (
        <div style={{ marginTop: '16px' }}>
            {/* 配置状态显示 */}
            {!showEditForm && (
                <div>
                    {config ? (
                        // 已配置状态
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                            <div style={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                justifyContent: 'space-between',
                                padding: '12px',
                                background: 'var(--bg-secondary)',
                                borderRadius: '6px'
                            }}>
                                <div>
                                    <div style={{ fontWeight: 600, marginBottom: '4px' }}>
                                        📄 {config.pageName}
                                    </div>
                                    <div style={{ 
                                        fontSize: '12px', 
                                        color: 'var(--text-secondary)',
                                        fontFamily: 'monospace'
                                    }}>
                                        Token: {config.token.substring(0, 20)}...
                                    </div>
                                    {config.lastVerified && (
                                        <div style={{ 
                                            fontSize: '11px', 
                                            color: 'var(--text-tertiary)',
                                            marginTop: '4px'
                                        }}>
                                            最后验证: {new Date(config.lastVerified).toLocaleString('zh-CN')}
                                        </div>
                                    )}
                                </div>
                                <StatusIndicator type="success">已配置</StatusIndicator>
                            </div>

                            {/* 操作按钮 */}
                            <div style={{ display: 'flex', gap: '8px' }}>
                                <button
                                    className="btn btn-secondary btn-small"
                                    onClick={handleTestConnection}
                                    disabled={validating}
                                >
                                    {validating ? '测试中...' : '🔍 测试连接'}
                                </button>
                                <button
                                    className="btn btn-secondary btn-small"
                                    onClick={handleStartEdit}
                                >
                                    ⚙️ 重新配置
                                </button>
                                <button
                                    className="btn btn-secondary btn-small"
                                    onClick={handleClearConfig}
                                    style={{ marginLeft: 'auto' }}
                                >
                                    🗑️ 清除配置
                                </button>
                            </div>
                        </div>
                    ) : (
                        // 未配置状态
                        <div style={{ 
                            padding: '16px', 
                            textAlign: 'center',
                            background: 'var(--bg-secondary)',
                            borderRadius: '6px'
                        }}>
                            <div style={{ marginBottom: '12px', color: 'var(--text-secondary)' }}>
                                未配置 Notion 导出
                            </div>
                            <button
                                className="btn btn-primary btn-small"
                                onClick={handleStartEdit}
                            >
                                ➕ 配置 Notion
                            </button>
                        </div>
                    )}

                    {/* 验证消息 */}
                    {validationMessage && (
                        <div style={{
                            marginTop: '12px',
                            padding: '10px 12px',
                            borderRadius: '6px',
                            fontSize: '14px',
                            background: validationMessage.type === 'success' 
                                ? 'rgba(15, 123, 108, 0.1)' 
                                : 'rgba(224, 62, 62, 0.1)',
                            color: validationMessage.type === 'success' 
                                ? '#0F7B6C' 
                                : '#E03E3E'
                        }}>
                            {validationMessage.text}
                        </div>
                    )}
                </div>
            )}

            {/* 编辑表单 */}
            {showEditForm && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {/* Token 输入 */}
                    <div>
                        <label style={{ 
                            display: 'block', 
                            marginBottom: '6px',
                            fontSize: '13px',
                            fontWeight: 500
                        }}>
                            Integration Token *
                        </label>
                        <input
                            type="password"
                            value={formToken}
                            onChange={(e) => setFormToken(e.target.value)}
                            placeholder="secret_xxxxxxxxxxxx"
                            style={{
                                width: '100%',
                                padding: '8px 10px',
                                border: '1px solid var(--border)',
                                borderRadius: '6px',
                                fontSize: '14px'
                            }}
                        />
                        <div style={{ 
                            fontSize: '11px', 
                            color: 'var(--text-secondary)',
                            marginTop: '4px'
                        }}>
                            在 Notion Integration 页面获取
                        </div>
                    </div>

                    {/* Page URL 输入 */}
                    <div>
                        <label style={{ 
                            display: 'block', 
                            marginBottom: '6px',
                            fontSize: '13px',
                            fontWeight: 500
                        }}>
                            页面 URL *
                        </label>
                        <input
                            type="text"
                            value={formPageUrl}
                            onChange={(e) => setFormPageUrl(e.target.value)}
                            placeholder="https://www.notion.so/Page-Title-xxxxx"
                            style={{
                                width: '100%',
                                padding: '8px 10px',
                                border: '1px solid var(--border)',
                                borderRadius: '6px',
                                fontSize: '14px'
                            }}
                        />
                        <div style={{ 
                            fontSize: '11px', 
                            color: 'var(--text-secondary)',
                            marginTop: '4px'
                        }}>
                            复制要保存笔记的 Notion 页面 URL
                        </div>
                    </div>

                    {/* 验证消息 */}
                    {validationMessage && (
                        <div style={{
                            padding: '10px 12px',
                            borderRadius: '6px',
                            fontSize: '14px',
                            background: validationMessage.type === 'success' 
                                ? 'rgba(15, 123, 108, 0.1)' 
                                : 'rgba(224, 62, 62, 0.1)',
                            color: validationMessage.type === 'success' 
                                ? '#0F7B6C' 
                                : '#E03E3E'
                        }}>
                            {validationMessage.text}
                        </div>
                    )}

                    {/* 操作按钮 */}
                    <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
                        <button
                            className="btn btn-secondary btn-small"
                            onClick={handleCancelEdit}
                            disabled={validating}
                        >
                            取消
                        </button>
                        <button
                            className="btn btn-primary btn-small"
                            onClick={handleSaveConfig}
                            disabled={validating}
                        >
                            {validating ? '验证中...' : '保存配置'}
                        </button>
                    </div>

                    {/* 帮助信息 */}
                    <div style={{
                        padding: '12px',
                        background: 'var(--bg-secondary)',
                        borderRadius: '6px',
                        fontSize: '12px',
                        color: 'var(--text-secondary)'
                    }}>
                        <div style={{ fontWeight: 600, marginBottom: '8px' }}>📘 配置说明：</div>
                        <ol style={{ margin: 0, paddingLeft: '20px' }}>
                            <li>1. 访问 <a href="https://www.notion.so/my-integrations" target="_blank" rel="noopener noreferrer" style={{ color: '#0066CC', textDecoration: 'underline' }}>Notion Integrations</a> 创建 Integration</li>
                            <li>2. Configuration中启用 "Read content" 和 "Insert content" 权限</li>
                            <li>3. Access中配置Page and database access，选择你要保存的Page</li>
                            <li>4. https://www.notion.so 页面找到你要保存的Page</li>
                            <li>5. 复制 Token 和页面 URL 到这里</li>
                        </ol>
                    </div>
                </div>
            )}
        </div>
    );
}
