# GitHub Copilot 项目指令 · 调试与工作流

## 调试步骤

| 目标 | 步骤 |
|------|------|
| Content Script 日志 | 打开页面控制台 |
| Background 日志 | chrome://extensions → 查看视图 → Service Worker |
| Popup 日志 | 右键 Popup → 检查 |
| 重新加载扩展 | chrome://extensions → 重新加载 |

## 常见任务流程

| 任务 | 步骤 |
|------|------|
| 添加功能 | 实现代码 → 添加类型 → 测试 → 提醒重载 |
| 修复 Bug | 查看日志 → 修改代码 → 验证 → 提醒重载 |
| 添加消息类型 | 添加 MessageType → 添加 handler → 创建 Proxy → 使用 |

## 文件命名

- Service: `XxxService.ts`
- Proxy: `XxxProxy.ts`
- Component: `XxxComponent.tsx`
- Types: `xxx.ts`

