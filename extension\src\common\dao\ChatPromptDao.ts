import { Singleton } from '../base'
import { dexieDatabase } from '../database/dexie'
import { ChatPromptEntity } from '@/common/types/database_entity'

/**
 * 聊天提示词数据访问对象
 * 只负责数据库CRUD操作，不包含业务逻辑
 */
export class ChatPromptDao extends Singleton<ChatPromptDao>{k
  
  /**
   * 创建聊天提示词记录
   */
  async create(chatPrompt: Omit<ChatPromptEntity, 'id'>): Promise<ChatPromptEntity> {
    await dexieDatabase.initialize()
    
    const id = await dexieDatabase.chatPrompt.add({
      ...chatPrompt,
      create_time: chatPrompt.create_time || Date.now(),
      is_synced: chatPrompt.is_synced || 0,
      is_delete: 0
    })
    
    const created = await dexieDatabase.chatPrompt.get(id)
    if (!created) {
      throw new Error('Failed to create chat prompt')
    }
    
    return created
  }

  /**
   * 根据ID查找聊天提示词记录
   */
  async findById(id: number): Promise<ChatPromptEntity | null> {
    await dexieDatabase.initialize()
    
    const chatPrompt = await dexieDatabase.chatPrompt.get(id)
    return chatPrompt || null
  }

  /**
   * 根据prompt_uid查找聊天提示词记录
   */
  async findByPromptUid(promptUid: string): Promise<ChatPromptEntity | null> {
    await dexieDatabase.initialize()
    
    const chatPrompt = await dexieDatabase.chatPrompt
      .where('prompt_uid')
      .equals(promptUid)
      .and(item => item.is_delete === 0)
      .first()
    
    return chatPrompt || null
  }

  /**
   * 根据提示词内容查找记录
   * @param limitLast 限制最近的记录数量，如果为0或undefined，则不限制
   */
  async findByPrompt(prompt: string): Promise<ChatPromptEntity | null> {
    await dexieDatabase.initialize()
    
    let query = dexieDatabase.chatPrompt
      .where('chat_prompt')
      .equals(prompt)
      .and(item => item.is_delete === 0)

    // if (limitLast) {
      // 先按创建时间倒序获取最近的limitLast条记录
      const recentRecords = await dexieDatabase.chatPrompt
        .where('is_delete')
        .equals(0)
        .reverse()
        // .limit(limitLast)
        .toArray()

      // 在最近记录中查找匹配的提示词
      const matchedPrompt = recentRecords.find(item => item.chat_prompt === prompt)
      return matchedPrompt || null
    // }
    
    // return await query.first()
  }

  /**
   * 查找所有聊天提示词记录
   */
  async findAll(options: {
    limit?: number
    offset?: number
    orderBy?: 'create_time' | 'id'
    orderDirection?: 'ASC' | 'DESC'
  } = {}): Promise<ChatPromptEntity[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50, offset = 0 } = options
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .offset(offset)
      .limit(limit)
      .reverse() // 默认按时间倒序
      .toArray()
  }

  /**
   * 更新聊天提示词记录
   */
  async update(id: number, updates: Partial<ChatPromptEntity>): Promise<ChatPromptEntity> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatPrompt.get(id)
    if (!existing) {
      throw new Error(`Chat prompt with id ${id} not found`)
    }
    
    await dexieDatabase.chatPrompt.update(id, updates)
    
    const updated = await dexieDatabase.chatPrompt.get(id)
    if (!updated) {
      throw new Error('Failed to update chat prompt')
    }
    
    return updated
  }

  /**
   * 软删除聊天提示词记录
   */
  async softDelete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const existing = await dexieDatabase.chatPrompt.get(id)
    if (!existing) {
      return false
    }
    
    await dexieDatabase.chatPrompt.update(id, { is_delete: 1 })
    return true
  }

  /**
   * 硬删除聊天提示词记录
   */
  async delete(id: number): Promise<boolean> {
    await dexieDatabase.initialize()
    
    await dexieDatabase.chatPrompt.delete(id)
    return true
  }

  /**
   * 根据prompt_uid软删除记录
   */
  async softDeleteByPromptUid(promptUid: string): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const record = await this.findByPromptUid(promptUid)
    if (!record) {
      return false
    }
    
    await dexieDatabase.chatPrompt.update(record.id!, { is_delete: 1 })
    return true
  }

  /**
   * 统计聊天提示词记录数量
   */
  async count(): Promise<number> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .count()
  }

  /**
   * 批量创建聊天提示词记录
   */
  async bulkCreate(chatPrompts: Omit<ChatPromptEntity, 'id'>[]): Promise<ChatPromptEntity[]> {
    await dexieDatabase.initialize()
    
    const now = Date.now()
    const records = chatPrompts.map(prompt => ({
      ...prompt,
      create_time: prompt.create_time || now,
      is_synced: prompt.is_synced || 0,
      is_delete: 0
    }))
    
    const ids = await dexieDatabase.chatPrompt.bulkAdd(records, { allKeys: true })
    
    const created = await dexieDatabase.chatPrompt
      .where('id')
      .anyOf(ids as number[])
      .toArray()
    
    return created
  }

  /**
   * 搜索聊天提示词记录（按提示词内容）
   */
  async searchByPrompt(searchTerm: string, options: {
    limit?: number
  } = {}): Promise<ChatPromptEntity[]> {
    await dexieDatabase.initialize()
    
    const { limit = 50 } = options
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .filter(item => 
        item.chat_prompt.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .limit(limit)
      .toArray()
  }

  /**
   * 检查提示词是否存在
   */
  async exists(prompt: string): Promise<boolean> {
    await dexieDatabase.initialize()
    
    const count = await dexieDatabase.chatPrompt
      .where('chat_prompt')
      .equals(prompt)
      .and(item => item.is_delete === 0)
      .count()
    
    return count > 0
  }

  /**
   * 获取最新的提示词记录
   */
  async findLatest(limit: number = 10): Promise<ChatPromptEntity[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .reverse() // 按时间倒序
      .limit(limit)
      .toArray()
  }

  /**
   * 根据时间范围查找提示词记录
   */
  async findByTimeRange(startTime: number, endTime: number): Promise<ChatPromptEntity[]> {
    await dexieDatabase.initialize()
    
    return await dexieDatabase.chatPrompt
      .where('create_time')
      .between(startTime, endTime)
      .and(item => item.is_delete === 0)
      .toArray()
  }

  /**
   * 获取所有唯一的prompt_uid
   */
  async getAllPromptUids(): Promise<string[]> {
    await dexieDatabase.initialize()

    const prompts = await dexieDatabase.chatPrompt
      .where('is_delete')
      .equals(0)
      .toArray()

    const uniqueUids = [...new Set(prompts.map(p => p.prompt_uid))]
    return uniqueUids
  }

  /**
   * 清空表中所有数据（硬删除）
   */
  async clear(): Promise<number> {
    await dexieDatabase.initialize()

    const count = await dexieDatabase.chatPrompt.count()
    await dexieDatabase.chatPrompt.clear()
    return count
  }
}

// 导出单例实例
export const chatPromptDao = ChatPromptDao.getInstance()
