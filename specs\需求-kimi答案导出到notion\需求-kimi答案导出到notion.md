# 需求-kimi答案导出到notion

导出到notion使用方案 官方 Notion API + 用户授权 (推荐)。
原理：
用户提供 token，插件通过 Notion API 创建页面/块。因此需要需要用户手动获取 token。为了安全这个notion token只存在本地，不需要同步。
因此流程为：
0. notion的logo使用 `src/content/inject/components/Notion_Symbol.svg`
1. 类似于obsidian，同样在`src/content/inject/components`目录生成notion导出按钮。
2. 用户点击插件的notion按钮，进行判断，如果local storage存在token,打开notion导出Modal，类似于obsidian的模态页，提供编辑标题等notion元数据的功能。
3. 如果本地不存在token，那么打开notion引导Modal，在这个模态页内，引导用户输入token。比如：
    2.1 打开 https://www.notion.so/my-integrations

    2.2 创建一个新 Integration；

    2.3 配置Access访问page的权限，必须要配置，不然无法把笔记新建到对应的page。

    2.3 复制其 “Internal Integration Token”, 把 token 粘贴到token输入框。

    2.4 在page输入框录入要保存的到page页地址，比如`https://www.notion.so/AIQ-2850fce3d30280c2b6cfd2f3db8b075f`,从这个地址获得pageName和pageId,pageId为末尾的32位字符串，pageName为id之前横线和斜杠之间的内容。

    2.5 点击下一步，token,pageName,pageId保存到local storage，同时在`src/common/service/`目录新建LocalStorageService,这个类专门用来处理不需要同步的保存。

4. 如果本地存在token，则直接打卡notion导出Modal，提供编辑标题等notion元数据的功能。
5. 导出时，其实使用Notion API进行新建笔记，api最新版本可以使用context7查询，也可以查看 https://developers.notion.com/docs/upgrade-guide-2025-09-03 
6. Notion API 不直接接受 Markdown 文本，你需要转成“Block”结构。推荐使用开源工具库：https://github.com/tryfabric/martian。实现markdown转换为notion
7. 在`src/content/service/NotionExportService.ts`中实现导出功能。


最后. 修改popup中的设置页面，把notion的配置从sync中摘除，notion也不存在导出路径，只有配置token和pageId,pageName的功能，而且这些存储在local storage而不是sync storage。

# 需求-kimi答案导出到notion

## 原始需求

导出到notion使用方案 官方 Notion API + 用户授权 (推荐)。
原理：
用户提供 token，插件通过 Notion API 创建页面/块。因此需要需要用户手动获取 token。为了安全这个notion token只存在本地，不需要同步。
因此流程为：
0. notion的logo使用 `src/content/inject/components/Notion_Symbol.svg`
1. 类似于obsidian，同样在`src/content/inject/components`目录生成notion导出按钮。
2. 用户点击插件的notion按钮，进行判断，如果local storage存在token,打开notion导出Modal，类似于obsidian的模态页，提供编辑标题等notion元数据的功能。
3. 如果本地不存在token，那么打开notion引导Modal，在这个模态页内，引导用户输入token。比如：
    2.1 打开 https://www.notion.so/my-integrations

    2.2 创建一个新 Integration；

    2.3 配置Access访问page的权限，必须要配置，不然无法把笔记新建到对应的page。

    2.3 复制其 "Internal Integration Token", 把 token 粘贴到token输入框。

    2.4 在page输入框录入要保存的到page页地址，比如`https://www.notion.so/AIQ-2850fce3d30280c2b6cfd2f3db8b075f`,从这个地址获得pageName和pageId,pageId为末尾的32位字符串，pageName为id之前横线和斜杠之间的内容。

    2.5 点击下一步，token,pageName,pageId保存到local storage，同时在`src/common/service/`目录新建LocalStorageService,这个类专门用来处理不需要同步的保存。

4. 如果本地存在token，则直接打卡notion导出Modal，提供编辑标题等notion元数据的功能。
5. 导出时，其实使用Notion API进行新建笔记，api最新版本可以使用context7查询，也可以查看 https://developers.notion.com/docs/upgrade-guide-2025-09-03 
6. Notion API 不直接接受 Markdown 文本，你需要转成"Block"结构。推荐使用开源工具库：https://github.com/tryfabric/martian。实现markdown转换为notion
7. 在`src/content/service/NotionExportService.ts`中实现导出功能。


最后. 修改popup中的设置页面，把notion的配置从sync中摘除，notion也不存在导出路径，只有配置token和pageId,pageName的功能，而且这些存储在local storage而不是sync storage。

请基于以上需求设计方案，并与我讨论，以本文件为媒介，确认哪些不明确的地方。

---

## 需求分析与讨论

### 已确认信息
✅ Notion_Symbol.svg 已存在于 `src/content/inject/components/` 目录
✅ 可以参考 ObsidianExportButton 和 ObsidianExportModal 的实现
✅ 使用 Notion 官方 API
✅ Token 存储在 LocalStorage（不同步）

### 需要明确的问题

#### 问题 1: Martian 库集成方式
您提到使用 https://github.com/tryfabric/martian 来转换 Markdown 为 Notion Block。

**需要确认**:
1. 这个库如何安装？
   - 查看该项目，它是一个 npm 包 `@tryfabric/martian`
   - 需要运行 `npm install @tryfabric/martian`
   
2. 该库在 Content Script 环境下是否能正常工作？
   - Martian 依赖 Node.js 环境，可能无法直接在浏览器环境使用
   - **建议方案 A**: 使用轻量级的 `notion-to-md` 的反向逻辑
   - **建议方案 B**: 自己实现简单的 Markdown → Notion Block 转换器
   - **建议方案 C**: 使用 `turndown` 的反向库（项目已安装 turndown）

**我的建议**: 自己实现一个简单的转换器，因为：
- 我们的 Markdown 格式固定（问题 + 答案）
- 不需要支持复杂的 Markdown 语法
- 避免引入大型依赖

#### 问题 2: Notion API 版本和调用方式
您提到使用最新版本 API (2025-09-03)。

**需要确认**:
1. API 调用方式：
   - 直接在 Content Script 中调用？（可能有 CORS 问题）
   - 通过 Background Script 代理？（推荐）
   
2. API Endpoint:
   - 创建页面: `POST https://api.notion.com/v1/pages`
   - 创建块: `POST https://api.notion.com/v1/blocks/{block_id}/children`

**我的建议**: 
- 在 Background Script 中处理 API 调用（避免 CORS）
- Content Script → Background → Notion API
- 需要在 manifest.json 添加 `host_permissions`: `["https://api.notion.com/*"]`

#### 问题 3: NotionSetupModal 的详细流程
您提到的步骤 2.4 解析 Page URL。

**需要确认**:
1. Page URL 格式解析：
   ```
   https://www.notion.so/AIQ-2850fce3d30280c2b6cfd2f3db8b075f
   pageName = "AIQ"
   pageId = "2850fce3d30280c2b6cfd2f3db8b075f"
   ```
   
   但 Notion URL 格式多样：
   - `https://www.notion.so/Page-Title-123abc456def`
   - `https://www.notion.so/username/Page-Title-123abc456def`
   - `https://www.notion.so/123abc456def`

**我的建议**:
- pageId: 提取 URL 最后的 32 位十六进制字符串（支持带横线和不带横线）
- pageName: 提取 pageId 前面的部分，如果为空则使用 "Notion Page"
- 提供测试按钮验证 Token 和 Page ID 是否有效

#### 问题 4: NotionExportModal 的元数据
您提到"提供编辑标题等notion元数据的功能"。

**需要确认**: Notion 页面元数据包括哪些？
- 标题（必需）
- 标签/Tags（可选）
- 图标 Emoji（可选）
- 封面图片（可选）

**我的建议**: 
- 标题：必填，默认使用问题前 50 字符
- 标签：可选，使用 TagSelector 组件
- 图标：可选，提供常用 Emoji 选择
- 封面：暂不支持（简化）

#### 问题 5: LocalStorageService 数据结构
需要存储的数据：

**需要确认**: 数据结构设计
```typescript
interface NotionConfig {
  token: string;          // Integration Token
  pageId: string;         // Parent Page ID (32位)
  pageName: string;       // Page 名称（显示用）
  lastVerified?: number;  // 最后验证时间
}
```

**问题**:
1. 是否支持多个 Workspace？（一个 token 只能对应一个 workspace）
2. 是否支持多个 Parent Page？（用户可能想保存到不同的 Page）

**我的建议**:
- 初期只支持单个配置（简化）
- 后期可扩展为多配置

#### 问题 6: 错误处理
**需要确认**: 如何处理以下错误？
1. Token 无效或过期
2. Page ID 不存在或无权限
3. API 调用失败（网络问题、速率限制）
4. Markdown 转换失败

**我的建议**:
- Token 错误：提示重新配置
- Page 错误：提示检查权限和 URL
- 网络错误：显示详细错误信息，提供重试按钮
- 转换错误：降级为纯文本保存

#### 问题 7: Popup 设置页面修改
**需要确认**: Popup 中 Notion 配置的 UI 设计
1. 是否需要"重新配置"按钮？
2. 是否显示当前配置的 Page 名称？
3. 是否提供"测试连接"功能？

**我的建议**:
- 显示当前配置状态（已配置/未配置）
- 显示 Page 名称（隐藏 Token）
- 提供"重新配置"按钮
- 提供"测试连接"按钮

---

## 我的实现方案建议

### 方案概述

#### 架构设计
```
NotionExportButton
    ↓ (点击)
检查 LocalStorage 是否有 Token
    ↓
有 Token → NotionExportModal (导出设置)
无 Token → NotionSetupModal (首次配置)
    ↓
NotionExportService
    ↓
Background Script (API Proxy)
    ↓
Notion API
```

#### 文件清单
1. **LocalStorageService.ts** - 本地存储服务（新建）
2. **NotionExportButton.ts** - Notion 导出按钮（新建）
3. **NotionExportButton.css** - 按钮样式（新建）
4. **NotionSetupModal.ts** - 首次配置 Modal（新建）
5. **NotionSetupModal.css** - 配置 Modal 样式（新建）
6. **NotionExportModal.ts** - 导出设置 Modal（新建）
7. **NotionExportModal.css** - 导出 Modal 样式（新建）
8. **NotionExportService.ts** - 导出服务（新建）
9. **NotionApiProxy.ts** - Background API 代理（新建）
10. **MarkdownToNotionBlock.ts** - Markdown 转换器（新建）
11. **NotionExportInject.ts** - 注入器（新建）
12. **KimiAnswerController.ts** - 集成（修改）
13. **manifest.json** - 添加权限（修改）
14. **Popup Settings** - 修改设置页面（修改）

#### 简化的 Markdown 转 Notion Block
由于我们的格式固定，自己实现转换器：

```typescript
function markdownToNotionBlocks(markdown: string): NotionBlock[] {
  const blocks: NotionBlock[] = [];
  
  // 解析 "## 问题" 和 "## 答案"
  const sections = markdown.split(/^## /gm).filter(Boolean);
  
  sections.forEach(section => {
    const [title, ...content] = section.split('\n');
    
    // 添加标题 Block
    blocks.push({
      type: 'heading_2',
      heading_2: {
        rich_text: [{ text: { content: title.trim() } }]
      }
    });
    
    // 添加内容 Block（支持段落、代码块等）
    const contentText = content.join('\n').trim();
    if (contentText) {
      blocks.push({
        type: 'paragraph',
        paragraph: {
          rich_text: [{ text: { content: contentText } }]
        }
      });
    }
  });
  
  return blocks;
}
```

### 实现步骤（分阶段）

#### 阶段 1: 基础设施（推荐先完成）
- [ ] LocalStorageService
- [ ] MarkdownToNotionBlock 转换器
- [ ] NotionApiProxy (Background)
- [ ] manifest.json 权限配置

#### 阶段 2: 配置流程
- [ ] NotionSetupModal（引导配置）
- [ ] Token 和 Page 验证逻辑

#### 阶段 3: 导出功能
- [ ] NotionExportButton
- [ ] NotionExportModal
- [ ] NotionExportService
- [ ] NotionExportInject

#### 阶段 4: Popup 集成
- [ ] 修改 Popup 设置页面
- [ ] 移除 Notion 的 Sync 配置

---

**请您确认以上分析和方案是否符合您的预期，并回答需要明确的问题。确认后我将开始实现。**

---

## 🚀 实施进度

### ✅ 已完成

#### 阶段 1: 基础设施 (100%)
- ✅ 安装 @tryfabric/martian 库
- ✅ 创建 LocalStorageService.ts
- ✅ 创建 NotionTypes.ts (类型定义)
- ✅ 创建 NotionApiService.ts (Background API 代理)
- ✅ 修改 messageHandler.ts (添加 Notion 消息处理)
- ✅ 修改 manifest.json (添加 Notion API 权限)
- ✅ 添加 MessageType.NOTION_VALIDATE_CONFIG 和 NOTION_CREATE_PAGE

#### 阶段 2: 配置流程 (100%)
- ✅ 创建 NotionSetupModal.ts (引导配置 Modal)
- ✅ 创建 NotionSetupModal.css (Modal 样式)

#### 阶段 3: 导出功能 (进行中... 20%)
- ✅ 创建 NotionExportButton.ts
- ✅ 创建 NotionExportButton.css
- ⏳ 创建 NotionExportModal.ts
- ⏳ 创建 NotionExportModal.css
- ⏳ 创建 NotionExportService.ts
- ⏳ 创建 NotionExportInject.ts
- ⏳ 集成到 KimiAnswerController.ts

#### 阶段 4: Popup 集成 (未开始)
- ⏳ 修改 Popup 设置页面

### 正在进行...
创建 NotionExportButton 样式和其他导出组件...

---

## ⏸️ 当前状态

由于实现内容较多且复杂，已完成基础设施和部分UI组件。

### 已创建文件清单 (8个)
1. ✅ LocalStorageService.ts - 本地存储服务
2. ✅ NotionTypes.ts - 类型定义
3. ✅ NotionApiService.ts - Background API 代理
4. ✅ NotionSetupModal.ts - 配置引导 Modal
5. ✅ NotionSetupModal.css - 配置 Modal 样式
6. ✅ NotionExportButton.ts - 导出按钮
7. ✅ NotionExportButton.css - 按钮样式
8. ✅ manifest.json - 已添加 Notion API 权限

### 已修改文件 (2个)
1. ✅ enums.ts - 添加 NOTION_VALIDATE_CONFIG 和 NOTION_CREATE_PAGE
2. ✅ messageHandler.ts - 添加 Notion 消息处理

### 剩余工作清单
1. ⏳ NotionExportModal.ts - 导出设置 Modal（类似 ObsidianExportModal，但更简单）
2. ⏳ NotionExportModal.css - 导出 Modal 样式
3. ⏳ NotionExportService.ts - 导出服务（核心业务逻辑 + Martian 集成）
4. ⏳ NotionExportInject.ts - 注入器
5. ⏳ KimiAnswerController.ts - 集成
6. ⏳ Popup Settings - 修改设置页面

### 下一步建议
建议分两次完成：
- **第一次（本次）**: 完成基础设施和配置流程 ✅ 已完成
- **第二次（下次）**: 完成导出功能和 Popup 集成

这样可以：
1. 先测试配置流程是否正常
2. 验证 Notion API 连接
3. 确保 Token 验证功能可用

**请确认是否继续完成剩余部分，或先测试已完成的功能？**