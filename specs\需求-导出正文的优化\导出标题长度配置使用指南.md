# 导出标题长度配置使用指南

## 配置说明

导出标题截断长度存储在 `chrome.storage.sync` 中，配置键为 `export_title_len`。

### 默认值

```typescript
export_title_len: 40  // 默认40个字符
```

## API 使用

### 1. SettingsStorageService（通用服务）

```typescript
import { settingsStorage } from '@/common/service/SettingsStorageService'

// 获取导出标题截断长度
const titleLength = await settingsStorage.getExportTitleLength()
console.log('当前截断长度:', titleLength) // 输出: 40

// 设置导出标题截断长度
await settingsStorage.setExportTitleLength(50)
console.log('截断长度已更新为 50')
```

### 2. ContentSettingsService（Content 模块）

```typescript
import { contentSettingsService } from '@/content/service/ContentSettingsService'

// 在 Content Script 中获取配置
const titleLength = await contentSettingsService.getExportTitleLength()
console.log('导出标题截断长度:', titleLength)
```

### 3. TextUtils（文本处理工具）

```typescript
import { TextUtils } from '@/content/utils/TextUtils'

// 使用默认长度（40）
const title1 = TextUtils.truncateTitle('这是一个很长的标题需要被智能截断处理以便符合显示要求')
console.log(title1) // '这是一个很长的标题需要被智能截断处理以便符合显示要求...'

// 使用自定义长度
const title2 = TextUtils.truncateTitle('这是标题', 20)
console.log(title2) // '这是标题'

// 结合配置使用
const titleLength = await contentSettingsService.getExportTitleLength()
const title3 = TextUtils.truncateTitle(prompt, titleLength)
```

## 在导出组件中使用

### ObsidianExportModal.ts

```typescript
private createTitleInput(defaultTitle: string): HTMLInputElement {
    this.titleInput = document.createElement('input');
    this.titleInput.type = 'text';
    this.titleInput.className = 'form-input';
    
    // 使用智能截断，默认使用配置的长度（40）
    const truncatedTitle = TextUtils.truncateTitle(defaultTitle);
    this.titleInput.value = truncatedTitle;
    this.titleInput.placeholder = '请输入标题';

    return this.titleInput;
}
```

### NotionExportModal.ts

```typescript
private createTitleInput(defaultTitle: string): HTMLInputElement {
    this.titleInput = document.createElement('input');
    this.titleInput.type = 'text';
    this.titleInput.className = 'form-input';
    
    // 使用智能截断，默认使用配置的长度（40）
    const truncatedTitle = TextUtils.truncateTitle(defaultTitle);
    this.titleInput.value = truncatedTitle;
    this.titleInput.placeholder = '请输入标题';

    return this.titleInput;
}
```

## 高级用法

### 动态获取配置并截断

如果需要在运行时动态获取配置：

```typescript
// 方案 A：在 Modal 初始化时获取（推荐）
export class ObsidianExportModal {
    private titleLength: number = 40; // 默认值

    constructor(private answerIndex: number) {
        // 异步初始化配置
        this.initConfig();
    }

    private async initConfig() {
        this.titleLength = await contentSettingsService.getExportTitleLength();
    }

    private createTitleInput(defaultTitle: string): HTMLInputElement {
        // 使用实例变量
        const truncatedTitle = TextUtils.truncateTitle(defaultTitle, this.titleLength);
        this.titleInput.value = truncatedTitle;
        return this.titleInput;
    }
}

// 方案 B：在显示 Modal 前获取（简单）
export class ObsidianExportButton {
    async handleExport() {
        const titleLength = await contentSettingsService.getExportTitleLength();
        // 传递给 Modal 或直接使用
        const modal = new ObsidianExportModal(answerIndex);
        modal.show();
    }
}
```

### 在 Popup 中修改配置

```typescript
// popup/SettingsPage.ts
import { settingsStorage } from '@/common/service/SettingsStorageService'

async function handleTitleLengthChange(newLength: number) {
    try {
        await settingsStorage.setExportTitleLength(newLength);
        console.log(`[Settings] 导出标题截断长度已更新为: ${newLength}`);
        
        // 显示成功提示
        showNotification('设置已保存', 'success');
    } catch (error) {
        console.error('[Settings] 保存设置失败:', error);
        showNotification('保存失败', 'error');
    }
}
```

## 配置存储位置

```
chrome.storage.sync
└── echosync_settings_v2
    ├── platforms: { ... }
    └── export_title_len: 40  // 导出标题截断长度
```

## 注意事项

1. **跨设备同步**: 配置存储在 `chrome.storage.sync`，自动同步到用户的所有 Chrome 设备

2. **默认值**: 如果配置未设置或读取失败，默认使用 40

3. **范围限制**: 建议设置范围为 20-100 字符
   - 太短（<20）：标题信息不足
   - 太长（>100）：失去截断意义

4. **性能**: `getExportTitleLength()` 是异步操作，避免频繁调用
   - 推荐：在组件初始化时获取一次
   - 不推荐：每次截断时都获取

## 测试

### 手动测试

```typescript
// 在 Chrome DevTools Console 中测试

// 1. 获取当前配置
chrome.storage.sync.get('echosync_settings_v2', (result) => {
    console.log('当前配置:', result.echosync_settings_v2);
});

// 2. 修改配置
chrome.storage.sync.get('echosync_settings_v2', (result) => {
    const settings = result.echosync_settings_v2;
    settings.export_title_len = 50;
    chrome.storage.sync.set({ 'echosync_settings_v2': settings }, () => {
        console.log('配置已更新');
    });
});

// 3. 验证配置
chrome.storage.sync.get('echosync_settings_v2', (result) => {
    console.log('新配置:', result.echosync_settings_v2.export_title_len);
});
```

### 单元测试

```typescript
import { settingsStorage } from '@/common/service/SettingsStorageService'

describe('导出标题长度配置', () => {
    it('应该返回默认值 40', async () => {
        const length = await settingsStorage.getExportTitleLength();
        expect(length).toBe(40);
    });

    it('应该成功设置新值', async () => {
        await settingsStorage.setExportTitleLength(50);
        const length = await settingsStorage.getExportTitleLength();
        expect(length).toBe(50);
    });

    it('应该处理无效值', async () => {
        // 如果需要，可以添加值验证逻辑
        await settingsStorage.setExportTitleLength(-1);
        const length = await settingsStorage.getExportTitleLength();
        expect(length).toBe(40); // 应回退到默认值
    });
});
```

## FAQ

### Q: 为什么选择 40 作为默认长度？

A: 40 字符是一个平衡点：
- 足够显示标题的核心信息
- 不会在输入框中显示过长
- 适合大多数文件系统的文件名长度限制
- 支持中英文混合场景

### Q: 如何在 Popup 中添加配置界面？

A: 在 Popup 的设置页面添加一个数字输入框：

```html
<div class="setting-item">
    <label>导出标题最大长度</label>
    <input type="number" id="titleLength" min="20" max="100" value="40" />
    <span class="hint">建议范围：20-100 字符</span>
</div>
```

```typescript
const input = document.getElementById('titleLength') as HTMLInputElement;
input.value = String(await settingsStorage.getExportTitleLength());

input.addEventListener('change', async () => {
    const newLength = parseInt(input.value);
    if (newLength >= 20 && newLength <= 100) {
        await settingsStorage.setExportTitleLength(newLength);
    }
});
```

### Q: 配置修改后需要重新加载扩展吗？

A: 不需要。配置存储在 `chrome.storage.sync` 中，修改后立即生效。但如果某个组件已经缓存了配置值，可能需要刷新页面或重新打开 Modal。

## 相关文档

- [TextUtils API 文档](../extension/src/content/utils/TextUtils.ts)
- [SettingsStorageService API](../extension/src/common/service/SettingsStorageService.ts)
- [ContentSettingsService API](../extension/src/content/service/ContentSettingsService.ts)
- [导出标题智能截断优化](./导出标题智能截断优化.md)
