# Deepseek HTML 转 Markdown 快速参考

## 核心优化点

### 1. 代码块处理
- **目标元素**: `.md-code-block` 容器
- **语言标签**: `.d813de27` 元素
- **代码内容**: `<pre>` 元素的 `textContent`
- **需移除**: 横幅 (`.md-code-block-banner`)、按钮、图标

### 2. 表格处理
- **结构**: `<thead>` + `<tbody>`
- **单元格清理**: 
  - 移除换行符 → 空格
  - 合并多个空格
  - 转义管道符 `|` → `\\|`

### 3. HTML 清理选择器
```typescript
const selectorsToRemove = [
    '.md-code-block-banner',
    '.md-code-block-banner-wrap',
    '.md-code-block-banner-lite',
    '.efa13877',
    'button',
    '.ds-atom-button',
    '.ds-icon-button',
    '[data-placeholder="svg-icon"]',
];
```

### 4. Markdown 后处理规则
1. 移除代码块前的按钮文本: `([复制下载运行]+)```` → ` ``` `
2. 代码块前后空行: 确保有 `\n\n`
3. 表格前后空行: 确保有 `\n\n`
4. 标题前后空行
5. 列表前后空行
6. 多余空行: `\n{3,}` → `\n\n`

## 关键方法

### extractCodeText
```typescript
// 优先使用 textContent
let code = preElement.textContent || '';
// 清理空白
code = code.trim();
// 降级: 递归提取
if (!code) {
    code = this.extractCodeTextRecursive(preElement);
}
```

### cleanCellText
```typescript
text
    .trim()
    .replace(/\n+/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/\|/g, '\\|')
```

## 测试检查点

- [ ] 代码块语言正确识别
- [ ] 代码内容无 UI 元素
- [ ] 代码缩进保持
- [ ] 表格格式正确
- [ ] 表头分隔线存在
- [ ] 单元格内容无换行
- [ ] 标题/列表/段落间距正确
- [ ] 无多余空行

## 常见问题

### Q: 代码块包含"复制下载"文字
**A**: 检查 `postProcessMarkdown` 中的正则是否正确移除

### Q: 表格格式错误
**A**: 确认 `cleanCellText` 正确转义管道符

### Q: 代码块不闭合
**A**: 查看控制台警告,检查 HTML 清理是否移除了 `<pre>` 元素

### Q: 内联代码被错误识别
**A**: 检查 `deepseekInlineCode` 规则的 `closest('.md-code-block')` 判断

## 调试技巧

1. **启用详细日志**:
```typescript
console.info('[DeepseekClipboardService] 使用 Deepseek 优化的降级方法');
console.info('[DeepseekClipboardService] Markdown 转换成功', markdown.substring(0, 200));
```

2. **检查中间结果**:
```typescript
// 清理后的 HTML
console.log('Cleaned HTML:', cleanedHtml);
// 转换后的 Markdown
console.log('Raw Markdown:', rawMarkdown);
// 后处理后的 Markdown
console.log('Processed Markdown:', markdown);
```

3. **验证选择器**:
```typescript
// 在浏览器控制台测试
document.querySelectorAll('.md-code-block');
document.querySelectorAll('.d813de27');
```

## 性能建议

- 优先使用 `textContent` 而非递归遍历
- 批量移除 DOM 元素
- 缓存重复查询的元素
- 避免在循环中使用正则表达式

## 相关文档

- [完整优化总结](./Deepseek降级HTML转Markdown优化.md)
- [BaseClipboardService](../../extension/src/content/core/BaseClipboardService.ts)
- [SelectorManager](../../extension/src/content/configs/SelectorManager.ts)
