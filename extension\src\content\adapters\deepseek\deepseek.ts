import { BaseAIAdapter } from '@/content/core/BaseAIAdapter'
import { DeepseekAnswerController } from './DeepseekAnswerController'
import { PlatformEntity } from '@/common/types/database_entity';


export class DeepSeekAdapter extends BaseAIAdapter {

  private deepseekAnswerController: DeepseekAnswerController | null = null;
  constructor(platform: PlatformEntity) { 
    console.log('【DeepSeekAdapter】DeepSeekAdapter constructor called')
    super(platform)
    this.deepseekAnswerController = new DeepseekAnswerController()
    this.deepseekAnswerController.init(this);    
    console.log('【DeepSeekAdapter】DeepSeekAdapter initialized with platform:', platform)
  }

  destroy(): void {
    try {
      console.log('【DeepSeekAdapter】DeepSeekAdapter destroy called');
      // 调用父类销毁方法
      super.destroy();
      
      // 清理 DeepseekAnswerController
      if (this.deepseekAnswerController) {
        this.deepseekAnswerController.destroy();
        this.deepseekAnswerController = null;
        console.log('【DeepSeekAdapter】DeepseekAnswerController 清理完成');
      }
      console.log('【DeepSeekAdapter】DeepSeekAdapter destroy 完成');
      
    } catch (error) {
      console.error('【DeepSeekAdapter】DeepSeekAdapter destroy 失败:', error);
    }
  }

  // /**
  //  * 获取 DeepSeek 平台特定的选择器配置
  //  */
  // getSelectors(): SelectorConfig {
  //   return {
  //     inputField: [
  //       '#chat-input',
  //       'textarea[placeholder*="DeepSeek"]',
  //       'textarea[placeholder*="发送消息"]',
  //       'textarea[placeholder*="输入"]',
  //       'textarea[placeholder*="message"]',
  //       'textarea[placeholder*="请输入"]'
  //     ],
  //     sendButton: [
  //       '.ds-button--primary',
  //       '[role="button"].ds-button',
  //       'button[aria-label*="发送"]',
  //       'button[aria-label*="Send"]',
  //       '.send-button',
  //       '[data-testid*="send"]'
  //     ]
  //   }
  // }

}
