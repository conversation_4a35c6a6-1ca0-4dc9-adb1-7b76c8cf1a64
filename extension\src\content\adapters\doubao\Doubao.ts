import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { DoubaoAnswerController } from "./DoubaoAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * Doubao (豆包) 平台适配器
 * 继承 BaseAIAdapter，提供 Doubao 平台的完整集成
 * 
 * 主要特性：
 * - 支持 www.doubao.com 域名
 * - 自动检测 Doubao 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 使用操作栏 opacity 变化检测答案完成
 * - 优先使用复制按钮，自动降级到 DOM 提取
 * 
 * URL 模式：
 * - Home页: https://www.doubao.com/chat/ 或 https://www.doubao.com/chat/?from_login=xxx
 * - Chat页: https://www.doubao.com/chat/{chatId}
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new DoubaoAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class DoubaoAdapter extends BaseAIAdapter {
    private doubaoAnswerController: DoubaoAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【DoubaoAdapter】DoubaoAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.doubaoAnswerController = new DoubaoAnswerController();
        this.doubaoAnswerController.init(this);
        
        console.log('【DoubaoAdapter】DoubaoAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【DoubaoAdapter】DoubaoAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 DoubaoAnswerController
            if (this.doubaoAnswerController) {
                this.doubaoAnswerController.destroy();
                this.doubaoAnswerController = null;
                console.log('【DoubaoAdapter】DoubaoAnswerController 清理完成');
            }
            
            console.log('【DoubaoAdapter】DoubaoAdapter destroy 完成');
            
        } catch (error) {
            console.error('【DoubaoAdapter】DoubaoAdapter destroy 失败:', error);
        }
    }
}
