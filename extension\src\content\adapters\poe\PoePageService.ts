import { BasePageService } from "@/content/core/BasePageService";

/**
 * Poe 页面状态管理服务
 * 继承 BasePageService，提供 Poe 平台特定的 URL 匹配规则
 * 
 * Poe URL模式：
 * - Home页: https://poe.com/ 或 https://poe.com
 * - Chat页: https://poe.com/chat/{chatId}
 * - 机器人页: https://poe.com/{botName}
 */
export class PoePageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Poe';
    }

    /**
     * 获取 Poe 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://poe.com 或 https://poe.com/ 或 https://poe.com/?xxx
            home: /^https:\/\/poe\.com\/?(\?.*)?$/i,
            // Chat页模式: https://poe.com/chat/{chatId}
            // chatId 通常是字母数字组合
            chat: /^https:\/\/poe\.com\/chat\/([a-zA-Z0-9-_]+)$/i
        };
    }

    /**
     * 检测欢迎页面（Poe 特定实现）
     * Poe的欢迎页面特征：
     * - URL是根路径
     * - 可能显示机器人列表或欢迎信息
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（Poe 特有的欢迎页面元素）- 使用模糊匹配
        // 检查是否存在探索页面或创建按钮
        const hasExploreSection = document.querySelector('[class*="SidebarCard_link"][href="/explore"]') !== null;
        const hasCreateButton = document.querySelector('[class*="SidebarCard_link"][href="/create_bot"]') !== null;
        
        // 同时检查是否没有聊天内容
        const hasChatContent = document.querySelector('[class*="ChatMessagesView_infiniteScroll"]') !== null;
        
        return (hasExploreSection || hasCreateButton) && !hasChatContent;
    }
    
    /**
     * 检测聊天页面（Poe 特定实现）
     * Poe的聊天页面特征：
     * - URL包含 /chat/{chatId}
     * - 存在聊天消息容器
     * - 存在输入框
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测 - 使用模糊匹配
        // 检查是否存在聊天消息容器和输入框
        const hasChatContainer = document.querySelector('[class*="ChatMessagesView_infiniteScroll"]') !== null;
        const hasInputField = document.querySelector('[class*="GrowingTextArea_textArea"]') !== null;
        const hasSendButton = document.querySelector('button[data-button-send="true"]') !== null;
        
        return hasChatContainer || (hasInputField && hasSendButton);
    }

    /**
     * 检测是否是机器人页面（Poe特有）
     * 机器人页面URL模式: https://poe.com/{botName}
     * @returns 是否是机器人页面
     */
    public isBotPage(): boolean {
        const url = window.location.href;
        
        // 匹配机器人页面URL: https://poe.com/{botName}
        // 排除一些特殊路径
        const botPagePattern = /^https:\/\/poe\.com\/([a-zA-Z0-9-_]+)$/i;
        const specialPaths = ['chat', 'explore', 'create_bot', 'chats', 'settings', 'leaderboard', 'creators', 'download', 'about', 'blog', 'privacy', 'tos'];
        
        const match = url.match(botPagePattern);
        if (!match) {
            return false;
        }
        
        const pathName = match[1].toLowerCase();
        
        // 排除特殊路径
        if (specialPaths.includes(pathName)) {
            return false;
        }
        
        // DOM检测：机器人页面应该有机器人信息卡片 - 使用模糊匹配
        const hasBotInfo = document.querySelector('[class*="BotInfoCard_sectionContainer"]') !== null;
        
        return hasBotInfo;
    }

    /**
     * 获取当前聊天ID（Poe特定）
     * @returns 聊天ID或null
     */
    public getChatId(): string | null {
        const url = window.location.href;
        const match = url.match(/^https:\/\/poe\.com\/chat\/([a-zA-Z0-9-_]+)$/i);
        return match ? match[1] : null;
    }

    /**
     * 获取当前机器人名称（Poe特定）
     * @returns 机器人名称或null
     */
    public getBotName(): string | null {
        // 从URL获取
        const url = window.location.href;
        const match = url.match(/^https:\/\/poe\.com\/([a-zA-Z0-9-_]+)$/i);
        
        if (match) {
            const pathName = match[1];
            const specialPaths = ['chat', 'explore', 'create_bot', 'chats', 'settings', 'leaderboard', 'creators', 'download', 'about', 'blog', 'privacy', 'tos'];
            
            if (!specialPaths.includes(pathName.toLowerCase())) {
                return pathName;
            }
        }
        
        // 从DOM获取（聊天页面中的机器人名称）- 使用模糊匹配
        const botHeader = document.querySelector('[class*="BotHeader_title"] p');
        return botHeader?.textContent?.trim() || null;
    }
}
