/* Notion 导出 Modal 样式 */

/* 遮罩层 */
.notion-export-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notion-export-modal-overlay.show {
  opacity: 1;
}

/* Modal 容器 */
.notion-export-modal {
  /* 莫兰迪蓝色系配色 */
  --notion-bg: #f8f9fb;
  --notion-bg-card: #ffffff;
  --notion-bg-hover: #f0f3f7;
  --notion-border: #d8dfe8;
  --notion-text: #3d4b5c;
  --notion-text-light: #7a8a9e;
  --notion-primary: #7c9cbf;
  --notion-secondary: #9fb4cd;
  background: var(--notion-bg-card);
  color: var(--notion-text);
  color-scheme: only light;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(124, 156, 191, 0.15), 0 2px 8px rgba(124, 156, 191, 0.08);
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
  position: relative;
  border: 1px solid rgba(124, 156, 191, 0.12);
}

.notion-export-modal * {
  color-scheme: only light;
}

.notion-export-modal-overlay.show .notion-export-modal {
  transform: scale(1);
}

/* Header */
.notion-export-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--notion-border, #d8dfe8);
  background: linear-gradient(135deg, rgba(124, 156, 191, 0.04), rgba(159, 180, 205, 0.02));
}

.notion-export-modal .header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notion-export-modal .notion-logo {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notion-export-modal .notion-logo svg {
  width: 100%;
  height: 100%;
}

.notion-export-modal .modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--notion-text, #3d4b5c);
  letter-spacing: -0.01em;
}

.notion-export-modal .close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--notion-text-light, #7a8a9e);
  font-size: 24px;
  line-height: 1;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notion-export-modal .close-btn:hover {
  background: rgba(124, 156, 191, 0.1);
  color: var(--notion-text, #3d4b5c);
}

/* Body */
.notion-export-modal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  padding-bottom: 96px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: var(--notion-bg, #f8f9fb);
}

.notion-export-modal .form-group {
  margin: 0;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid rgba(124, 156, 191, 0.15);
  background: var(--notion-bg-card);
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.06);
  transition: all 0.2s ease;
}

.notion-export-modal .form-group:hover {
  border-color: rgba(124, 156, 191, 0.25);
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.1);
}

.notion-export-modal .form-group:last-child {
  margin-bottom: 0;
}

.notion-export-modal .form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
  font-weight: 500;
  color: var(--notion-text-light, #7a8a9e);
  letter-spacing: 0.01em;
}

.notion-export-modal .form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--notion-border, #d8dfe8);
  border-radius: 8px;
  font-size: 14px;
  color: var(--notion-text, #3d4b5c);
  transition: all 0.2s ease;
  box-sizing: border-box;
  background: #FFFFFF;
}

.notion-export-modal .form-input:focus {
  outline: none;
  border-color: var(--notion-primary, #7c9cbf);
  box-shadow: 0 0 0 3px rgba(124, 156, 191, 0.12);
}

.notion-export-modal .emoji-input {
  max-width: 100px;
  text-align: center;
  font-size: 24px;
}

.notion-export-modal .content-preview {
  padding: 16px;
  background: rgba(124, 156, 191, 0.04);
  border-radius: 12px;
  font-size: 14px;
  color: var(--notion-text, #3d4b5c);
  border: 1px solid rgba(124, 156, 191, 0.12);
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.04);
  max-height: none;
  overflow: visible;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Markdown 样式 - Notion 风格 */
.notion-export-modal .markdown-body h1,
.notion-export-modal .markdown-body h2,
.notion-export-modal .markdown-body h3,
.notion-export-modal .markdown-body h4,
.notion-export-modal .markdown-body h5,
.notion-export-modal .markdown-body h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #37352F;
  line-height: 1.3;
}

.notion-export-modal .markdown-body h1:first-child,
.notion-export-modal .markdown-body h2:first-child,
.notion-export-modal .markdown-body h3:first-child {
  margin-top: 0;
}

.notion-export-modal .markdown-body h1 { font-size: 24px; }
.notion-export-modal .markdown-body h2 { font-size: 20px; }
.notion-export-modal .markdown-body h3 { font-size: 18px; }
.notion-export-modal .markdown-body h4 { font-size: 16px; }
.notion-export-modal .markdown-body h5 { font-size: 14px; }
.notion-export-modal .markdown-body h6 { font-size: 14px; color: #6F6F6F; }

.notion-export-modal .markdown-body p {
  margin: 0 0 12px 0;
  color: #37352F;
}

.notion-export-modal .markdown-body strong {
  font-weight: 600;
  color: #37352F;
}

.notion-export-modal .markdown-body em {
  font-style: italic;
}

.notion-export-modal .markdown-body code.inline-code {
  background: rgba(135, 131, 120, 0.15);
  color: #EB5757;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 85%;
}

.notion-export-modal .markdown-body pre {
  background: #F7F6F3;
  border: 1px solid #E3E2E0;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 12px 0;
}

.notion-export-modal .markdown-body pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 13px;
  color: #37352F;
}

.notion-export-modal .markdown-body ul,
.notion-export-modal .markdown-body ol {
  margin: 0 0 12px 0;
  padding-left: 24px;
}

.notion-export-modal .markdown-body li {
  margin-bottom: 4px;
  color: #37352F;
}

.notion-export-modal .markdown-body blockquote {
  border-left: 3px solid #D3D1CB;
  padding-left: 16px;
  margin: 12px 0;
  color: #6F6F6F;
}

.notion-export-modal .markdown-body a {
  color: #2383E2;
  text-decoration: none;
}

.notion-export-modal .markdown-body a:hover {
  text-decoration: underline;
}

.notion-export-modal .markdown-body hr {
  border: none;
  border-top: 1px solid #E3E2E0;
  margin: 16px 0;
}

.notion-export-modal .markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.notion-export-modal .markdown-body table th,
.notion-export-modal .markdown-body table td {
  border: 1px solid #E3E2E0;
  padding: 8px 12px;
  text-align: left;
}

.notion-export-modal .markdown-body table th {
  background: #F7F6F3;
  font-weight: 600;
}

/* KaTeX 数学公式样式 */
.notion-export-modal .markdown-body .katex {
  font-size: 1.1em;
}

.notion-export-modal .markdown-body .katex-display {
  margin: 16px 0;
  overflow-x: auto;
  overflow-y: hidden;
}

.notion-export-modal .markdown-body .math-error {
  color: #EB5757;
  background: rgba(235, 87, 87, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

/* Footer */
.notion-export-modal .modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid #E3E2E0;
}

.notion-export-modal .modal-scroll-top {
  position: absolute;
  right: 24px;
  bottom: 72px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 999px;
  border: none;
  background: linear-gradient(135deg, var(--notion-primary, #7c9cbf), var(--notion-secondary, #9fb4cd));
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.25);
  opacity: 0;
  transform: translateY(12px);
  pointer-events: none;
  transition: opacity 0.25s ease, transform 0.25s ease, box-shadow 0.2s ease;
}

.notion-export-modal .modal-scroll-top:hover {
  box-shadow: 0 6px 16px rgba(124, 156, 191, 0.35);
}

.notion-export-modal .modal-scroll-top:active {
  transform: translateY(0) scale(0.98);
}

.notion-export-modal .modal-scroll-top.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.notion-export-modal .modal-scroll-top .scroll-top-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.18);
  font-size: 12px;
  line-height: 1;
}

.notion-export-modal .modal-scroll-top .scroll-top-text {
  letter-spacing: 0.4px;
}

.notion-export-modal .modal-scroll-top .scroll-top-hint {
  font-size: 11px;
  opacity: 0.85;
}

.notion-export-modal .btn-primary,
.notion-export-modal .btn-secondary {
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.notion-export-modal .btn-primary {
  background: linear-gradient(135deg, var(--notion-primary, #7c9cbf), var(--notion-secondary, #9fb4cd));
  color: white;
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.25);
}

.notion-export-modal .btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.35);
}

.notion-export-modal .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.notion-export-modal .btn-secondary {
  background: transparent;
  color: var(--notion-text, #3d4b5c);
  border: 1px solid var(--notion-border, #d8dfe8);
}

.notion-export-modal .btn-secondary:hover {
  background: rgba(124, 156, 191, 0.06);
}

/* 响应式 */
@media (max-width: 640px) {
  .notion-export-modal {
    width: 95vw;
    max-height: 90vh;
  }

  .notion-export-modal .modal-header,
  .notion-export-modal .modal-body,
  .notion-export-modal .modal-footer {
    padding: 16px;
  }

  .notion-export-modal .modal-body {
    padding-bottom: 88px;
  }

  .notion-export-modal .modal-scroll-top {
    right: 16px;
    bottom: 88px;
  }
}
