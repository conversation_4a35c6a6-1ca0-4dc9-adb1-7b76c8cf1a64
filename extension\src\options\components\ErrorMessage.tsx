import React from 'react'
import { AlertCircle, RefreshCw, X } from 'lucide-react'

interface ErrorMessageProps {
  message: string
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  variant?: 'error' | 'warning' | 'info'
}

/**
 * 错误消息组件
 */
export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  onRetry,
  onDismiss,
  className = '',
  variant = 'error'
}) => {
  const variantClasses = {
    error: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800'
  }

  const iconClasses = {
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500'
  }

  return (
    <div className={`
      border rounded-lg p-4 
      ${variantClasses[variant]} 
      ${className}
    `}>
      <div className="flex items-start gap-3">
        {/* 错误图标 */}
        <AlertCircle className={`w-5 h-5 flex-shrink-0 mt-0.5 ${iconClasses[variant]}`} />
        
        {/* 错误内容 */}
        <div className="flex-1 min-w-0">
          <div className="font-medium mb-1">
            {variant === 'error' && '操作失败'}
            {variant === 'warning' && '警告'}
            {variant === 'info' && '提示'}
          </div>
          <div className="text-sm break-words">
            {message}
          </div>
          
          {/* 操作按钮 */}
          {(onRetry || onDismiss) && (
            <div className="flex items-center gap-2 mt-3">
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="
                    inline-flex items-center gap-1 px-3 py-1.5 
                    text-sm font-medium rounded-md
                    bg-white border border-gray-300
                    hover:bg-gray-50 transition-colors
                  "
                >
                  <RefreshCw className="w-3 h-3" />
                  重试
                </button>
              )}
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className="
                    inline-flex items-center gap-1 px-3 py-1.5 
                    text-sm font-medium rounded-md
                    text-gray-600 hover:text-gray-800 
                    hover:bg-white transition-colors
                  "
                >
                  <X className="w-3 h-3" />
                  关闭
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ErrorMessage
