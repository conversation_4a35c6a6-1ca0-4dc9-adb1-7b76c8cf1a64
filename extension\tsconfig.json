{"compilerOptions": {"target": "ES2018", "lib": ["ES2018", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "node", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "useDefineForClassFields": false, "strict": false, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": false, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["chrome", "jest", "@testing-library/jest-dom", "node"]}, "include": ["src", "tests", "../docs/bak/EventBus.ts", "../docs/bak/EventEnum.ts", "../specs/需求-导出正文的优化/DeepseekClipboardService.ts"], "exclude": ["node_modules", "dist"], "references": [{"path": "./tsconfig.node.json"}]}