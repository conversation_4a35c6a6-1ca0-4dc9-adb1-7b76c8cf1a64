# CSS 设计规范

> 本文档是所有 CSS 变量、样式代码、组件模板的唯一来源

---

## 🎨 色彩系统

### CSS 变量定义
```css
:root {
  /* === 主色系（蓝色 - 交互/选中） === */
  --primary: #3B82F6;
  --primary-dark: #2563EB;
  --primary-light: #60A5FA;
  --primary-50: #EFF6FF;
  --primary-100: #DBEAFE;
  
  /* === 辅助色（紫色 - 强化/强调） === */
  --purple: #8B5CF6;
  --purple-dark: #7C3AED;
  --purple-50: #F5F3FF;
  --purple-100: #EDE9FE;
  
  /* === 中性色（基础 UI） === */
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6B7280;
  
  --bg-base: #FAFBFC;
  --bg-elevated: #FFFFFF;
  --bg-subtle: #F9FAFB;
  
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
}
```
## 📐 间距系统（8pt Grid）

### 间距变量
```css
:root {
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-6: 24px;
  --space-8: 32px;
  --space-12: 48px;
  --space-16: 64px;
}
```

### 使用场景
| 场景 | 间距值 | CSS 变量 |
|------|--------|----------|
| 卡片内边距 | 32px | `var(--space-8)` |
| 卡片间距 | 16px | `var(--space-4)` |
| 功能列表项间距 | 12px | `var(--space-3)` |
| Badge 内边距 | 6px 12px | `var(--space-1.5) var(--space-3)` |
| 按钮内边距 | 12px 20px | `var(--space-3) var(--space-5)` |

### 强制规则
- ✅ **所有 padding/margin 必须是 4 的倍数**
- ✅ **优先使用 CSS 变量**
- ❌ **禁止使用奇数值**（如 15px, 23px）

---

## 🔤 字体系统

### 字体家族
```css
:root {
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 Roboto, sans-serif;
}
```

### 字体大小层级
```css
:root {
  --font-xs: 12px;      /* Badge、标签 */
  --font-sm: 13px;      /* 辅助文字 */
  --font-base: 14px;    /* 正文、按钮 */
  --font-md: 16px;      /* 强调正文 */
  --font-lg: 20px;      /* 卡片标题 */
  --font-xl: 24px;      /* 区块标题 */
  --font-2xl: 32px;     /* 页面标题 */
  --font-3xl: 48px;     /* 价格数字 */
}
```

### 字重
```css
:root {
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### Letter Spacing（Linear 特色）
```css
:root {
  --tracking-tight: -0.03em;   /* 价格数字 */
  --tracking-normal: -0.02em;  /* 标题 */
  --tracking-wide: 0.5px;      /* Badge 大写字母 */
}
```

### 行高
```css
:root {
  --leading-none: 1;        /* 价格 */
  --leading-tight: 1.25;    /* 标题 */
  --leading-normal: 1.5;    /* 正文 */
  --leading-relaxed: 1.6;   /* 长文本 */
}
```

---

## 🎭 圆角系统

```css
:root {
  --radius-sm: 6px;    /* Badge、小按钮 */
  --radius-md: 8px;    /* 按钮 */
  --radius-lg: 12px;   /* 卡片 */
  --radius-xl: 16px;   /* 容器 */
  --radius-full: 9999px; /* 圆形元素 */
}
```

**应用规则**：
- 卡片固定使用 `12px`
- 按钮固定使用 `8px`
- ❌ 避免使用 `20px+` 圆角（过于圆润）

---

## 🌓 阴影系统

### 基础阴影
```css
:root {
  --shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
}
```

### 悬停阴影（带颜色）
```css
:root {
  --shadow-hover-blue: 
    0 0 0 1px var(--primary),
    0 12px 24px -8px rgba(59, 130, 246, 0.15),
    0 24px 48px -8px rgba(0, 0, 0, 0.08);
}
```

### 焦点阴影
```css
:root {
  --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

---

## ⚡ 动效系统

### 时长
```css
:root {
  --duration-instant: 100ms;  /* 即时反馈（按钮按下） */
  --duration-fast: 150ms;     /* 快速反馈（悬停） */
  --duration-normal: 200ms;   /* 标准动画（大部分场景） */
  --duration-slow: 300ms;     /* 慢速动画（复杂变化） */
}
```

### 缓动函数
```css
:root {
  --easing: cubic-bezier(0.4, 0, 0.2, 1);      /* 标准 */
  --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1); /* 弹性（Apple 风格） */
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
}
```
---

## 🎯 核心组件样式模板

### 卡片（Card）
```css
.card {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  transition: all var(--duration-normal) var(--easing);
  position: relative;
}

/* Linear 特色：顶部彩色边框 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--purple));
  opacity: 0;
  transition: opacity var(--duration-normal);
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  transform: translateY(-4px);
  border-color: var(--primary);
  box-shadow: var(--shadow-hover-blue);
}
```

### Badge（标签）
```css
.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
  border: 1px solid;
}

/* 渐变背景变体 */
.badge-primary {
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  color: var(--primary-dark);
  border-color: var(--primary);
}

.badge-purple {
  background: linear-gradient(135deg, var(--purple-50), var(--purple-100));
  color: var(--purple-dark);
  border-color: var(--purple);
}
```

### 按钮（Button）

#### Primary 按钮
```css
.btn-primary {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: var(--font-semibold);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  transition: all var(--duration-fast) var(--easing);
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-focus);
}

.btn-primary:active {
  transform: translateY(0);
}
```

#### Secondary 按钮
```css
.btn-secondary {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: var(--font-semibold);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1.5px solid var(--border-light);
  transition: all var(--duration-fast) var(--easing);
}

.btn-secondary:hover {
  background: var(--bg-subtle);
  border-color: var(--border-medium);
  transform: translateY(-1px);
}
```

### 功能图标（Feature Icon）
```css
.feature-icon {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  flex-shrink: 0;
  margin-top: 2px;
}
```

---

## 📱 响应式断点

```css
/* 大屏（1400px+）：4列 */
@media (min-width: 1400px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-4);
  }
}

/* 中屏（768px-1400px）：2列 */
@media (min-width: 768px) and (max-width: 1399px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

/* 小屏（<768px）：1列 */
@media (max-width: 767px) {
  .grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .card {
    padding: var(--space-6);
  }
}
```

---

## 🚫 禁止规则 - CSS 示例

### 缩放比例参考表
| 场景 | 推荐比例 | 用途 | 示例代码 |
|------|---------|------|----------|
| 按下 | 0.95-0.98 | 反馈按压感 | `scale(0.98)` |
| 悬停 | 1.02-1.05 | 提示可交互 | `scale(1.03)` |
| 图标 | 1.1-1.15 | 强调吸引注意 | `scale(1.1)` |
| 焦点 | 1.01 | 微妙强调 | `scale(1.01)` |
| 弹性动画 | 1.15 | 心跳、脉动效果 | `scale(1.15)` |

---

## 🎨 微交互代码示例

### 按钮点击反馈
```css
.button {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  background: var(--primary);
  color: white;
  border: none;
  transition: all 150ms var(--easing-spring);
  cursor: pointer;
}

/* 悬停：轻微上浮 */
.button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-focus);
}

/* 按下：轻微缩小 + 下沉 */
.button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--shadow-xs);
}
```

### 卡片交互
```css
.card {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  transition: all 200ms var(--easing);
  position: relative;
}

/* 悬停：上浮 + 阴影增强 */
.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-hover-blue);
  border-color: var(--primary);
}

/* 顶部边框显现 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--purple));
  opacity: 0;
  transition: opacity var(--duration-normal);
}

.card:hover::before {
  opacity: 1;
}

/* 点击反馈：轻微缩小 */
.card:active {
  transform: translateY(-2px) scale(0.99);
}
```

### 输入框焦点
```css
.input {
  padding: 12px 16px;
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  transition: all 200ms var(--easing);
}

/* 悬停提示 */
.input:hover {
  border-color: var(--border-medium);
}

/* 焦点反馈 */
.input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: var(--shadow-focus);
  transform: scale(1.01);  /* 微妙放大 */
}
```

### 加载状态
```css
.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-subtle) 0%,
    var(--border-light) 50%,
    var(--bg-subtle) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

### 通知/Toast 提示
```css
.toast {
  padding: 16px 24px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary);
  animation: slideIn 300ms var(--easing);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 退出动画 */
.toast.exit {
  animation: slideOut 300ms var(--easing) forwards;
}

@keyframes slideOut {
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* 不同状态 */
.toast-success { border-left-color: #10B981; }
.toast-error { border-left-color: #EF4444; }
.toast-warning { border-left-color: #F59E0B; }
```

### Modal/弹窗
```css
/* 背景遮罩 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0);
  transition: background 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay.open {
  background: rgba(0, 0, 0, 0.5);
}

/* 弹窗内容 */
.modal-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  max-width: 500px;
  width: 90%;
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all 300ms var(--easing-spring);
}

.modal-content.open {
  opacity: 1;
  transform: scale(1) translateY(0);
}
```


