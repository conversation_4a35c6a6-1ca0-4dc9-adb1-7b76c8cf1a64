# CSS 设计规范 - Linear 现代极简风

> 本文档是所有 CSS 变量、样式代码、组件模板的唯一来源  
> **其他文档仅引用本文档，不重复 CSS 代码**

---

## 📚 文档关系

- **CSSDesignTokens.md**（当前文档）- 所有 CSS 代码的权威来源
- **UIAgentRules.md** - 设计原则、禁止规则、AI 提示词
- **MicrointeractionsGuide.md** - 微交互场景详解（10+ 种场景）

---

## 🎨 色彩系统

### CSS 变量定义
```css
:root {
  /* === 主色系（蓝色 - 交互/选中） === */
  --primary: #3B82F6;
  --primary-dark: #2563EB;
  --primary-light: #60A5FA;
  --primary-50: #EFF6FF;
  --primary-100: #DBEAFE;
  
  /* === 辅助色（紫色 - 强化/强调） === */
  --purple: #8B5CF6;
  --purple-dark: #7C3AED;
  --purple-50: #F5F3FF;
  --purple-100: #EDE9FE;
  
  /* === 中性色（基础 UI） === */
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6B7280;
  
  --bg-base: #FAFBFC;
  --bg-elevated: #FFFFFF;
  --bg-subtle: #F9FAFB;
  
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
}
```

### 使用规则
```css
/* ✅ 正确 - 使用 CSS 变量 */
.button {
  background: var(--primary);
  color: white;
}

/* ❌ 错误 - 硬编码颜色值 */
.button {
  background: #3B82F6;
}
```

---

## 📐 间距系统（8pt Grid）

### 间距变量
```css
:root {
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-6: 24px;
  --space-8: 32px;
  --space-12: 48px;
  --space-16: 64px;
}
```

### 使用场景
| 场景 | 间距值 | CSS 变量 |
|------|--------|----------|
| 卡片内边距 | 32px | `var(--space-8)` |
| 卡片间距 | 16px | `var(--space-4)` |
| 功能列表项间距 | 12px | `var(--space-3)` |
| Badge 内边距 | 6px 12px | `var(--space-1.5) var(--space-3)` |
| 按钮内边距 | 12px 20px | `var(--space-3) var(--space-5)` |

### 强制规则
- ✅ **所有 padding/margin 必须是 4 的倍数**
- ✅ **优先使用 CSS 变量**
- ❌ **禁止使用奇数值**（如 15px, 23px）

---

## 🔤 字体系统

### 字体家族
```css
:root {
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 Roboto, sans-serif;
}
```

### 字体大小层级
```css
:root {
  --font-xs: 12px;      /* Badge、标签 */
  --font-sm: 13px;      /* 辅助文字 */
  --font-base: 14px;    /* 正文、按钮 */
  --font-md: 16px;      /* 强调正文 */
  --font-lg: 20px;      /* 卡片标题 */
  --font-xl: 24px;      /* 区块标题 */
  --font-2xl: 32px;     /* 页面标题 */
  --font-3xl: 48px;     /* 价格数字 */
}
```

### 字重
```css
:root {
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

### Letter Spacing（Linear 特色）
```css
:root {
  --tracking-tight: -0.03em;   /* 价格数字 */
  --tracking-normal: -0.02em;  /* 标题 */
  --tracking-wide: 0.5px;      /* Badge 大写字母 */
}
```

### 行高
```css
:root {
  --leading-none: 1;        /* 价格 */
  --leading-tight: 1.25;    /* 标题 */
  --leading-normal: 1.5;    /* 正文 */
  --leading-relaxed: 1.6;   /* 长文本 */
}
```

---

## 🎭 圆角系统

```css
:root {
  --radius-sm: 6px;    /* Badge、小按钮 */
  --radius-md: 8px;    /* 按钮 */
  --radius-lg: 12px;   /* 卡片 */
  --radius-xl: 16px;   /* 容器 */
  --radius-full: 9999px; /* 圆形元素 */
}
```

**应用规则**：
- 卡片固定使用 `12px`
- 按钮固定使用 `8px`
- ❌ 避免使用 `20px+` 圆角（过于圆润）

---

## 🌓 阴影系统

### 基础阴影
```css
:root {
  --shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
}
```

### 悬停阴影（带颜色）
```css
:root {
  --shadow-hover-blue: 
    0 0 0 1px var(--primary),
    0 12px 24px -8px rgba(59, 130, 246, 0.15),
    0 24px 48px -8px rgba(0, 0, 0, 0.08);
}
```

### 焦点阴影
```css
:root {
  --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

---

## ⚡ 动效系统

### 时长
```css
:root {
  --duration-instant: 100ms;  /* 即时反馈（按钮按下） */
  --duration-fast: 150ms;     /* 快速反馈（悬停） */
  --duration-normal: 200ms;   /* 标准动画（大部分场景） */
  --duration-slow: 300ms;     /* 慢速动画（复杂变化） */
}
```

### 缓动函数
```css
:root {
  --easing: cubic-bezier(0.4, 0, 0.2, 1);      /* 标准 */
  --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1); /* 弹性（Apple 风格） */
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
}
```

---

## 🎯 核心组件样式模板

### 卡片（Card）
```css
.card {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  transition: all var(--duration-normal) var(--easing);
  position: relative;
}

/* Linear 特色：顶部彩色边框 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--purple));
  opacity: 0;
  transition: opacity var(--duration-normal);
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  transform: translateY(-4px);
  border-color: var(--primary);
  box-shadow: var(--shadow-hover-blue);
}
```

### Badge（标签）
```css
.badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: var(--tracking-wide);
  border: 1px solid;
}

/* 渐变背景变体 */
.badge-primary {
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  color: var(--primary-dark);
  border-color: var(--primary);
}

.badge-purple {
  background: linear-gradient(135deg, var(--purple-50), var(--purple-100));
  color: var(--purple-dark);
  border-color: var(--purple);
}
```

### 按钮（Button）

#### Primary 按钮
```css
.btn-primary {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: var(--font-semibold);
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  border: none;
  transition: all var(--duration-fast) var(--easing);
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-focus);
}

.btn-primary:active {
  transform: translateY(0);
}
```

#### Secondary 按钮
```css
.btn-secondary {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: var(--font-semibold);
  background: var(--bg-elevated);
  color: var(--text-secondary);
  border: 1.5px solid var(--border-light);
  transition: all var(--duration-fast) var(--easing);
}

.btn-secondary:hover {
  background: var(--bg-subtle);
  border-color: var(--border-medium);
  transform: translateY(-1px);
}
```

### 功能图标（Feature Icon）
```css
.feature-icon {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  flex-shrink: 0;
  margin-top: 2px;
}
```

---

## 📱 响应式断点

```css
/* 大屏（1400px+）：4列 */
@media (min-width: 1400px) {
  .grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-4);
  }
}

/* 中屏（768px-1400px）：2列 */
@media (min-width: 768px) and (max-width: 1399px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
}

/* 小屏（<768px）：1列 */
@media (max-width: 767px) {
  .grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
  
  .card {
    padding: var(--space-6);
  }
}
```

---

## 📏 标准尺寸参考

| 元素 | 尺寸 | CSS 变量 |
|------|------|----------|
| 卡片内边距 | 32px | `var(--space-8)` |
| 按钮内边距 | 12px 20px | `var(--space-3) var(--space-5)` |
| Badge 内边距 | 6px 12px | - |
| 功能图标 | 18x18px | - |
| 卡片间距 | 16px | `var(--space-4)` |
| 顶部边框高度 | 3px | - |
| 卡片边框 | 1.5px | - |
| 卡片圆角 | 12px | `var(--radius-lg)` |
| 按钮圆角 | 8px | `var(--radius-md)` |

---

## 🎯 平权设计 - 状态颜色映射

### 设计理念
**核心思想**：体现包容性价值观，所有等级使用统一色系（蓝色 + 紫色），通过功能数量而非颜色强度来区分等级。

### 状态颜色映射
```css
/* 选中状态 - 蓝色 */
.tier-selected {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary),
              0 12px 24px -8px rgba(59, 130, 246, 0.15),
              0 24px 48px -8px rgba(0, 0, 0, 0.08);
}

.tier-selected::before {
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  opacity: 1;
}

/* 已解锁标识 - 紫色 */
.tier-unlocked-badge {
  background: linear-gradient(135deg, var(--purple-50), var(--purple-100));
  color: var(--purple-dark);
  border-color: var(--purple);
}

.tier-unlocked-icon {
  color: var(--purple);
}

/* 默认状态 - 中性色 */
.tier-default {
  border-color: var(--border-light);
}

/* 悬停状态 - 统一蓝色 */
.tier-card:hover {
  border-color: var(--primary);
  box-shadow: 0 0 0 1px var(--primary),
              0 12px 24px -8px rgba(59, 130, 246, 0.15),
              0 24px 48px -8px rgba(0, 0, 0, 0.08);
}

.tier-card:hover::before {
  background: linear-gradient(90deg, var(--primary), var(--primary-dark));
  opacity: 1;
}
```

### 设计原则
| 状态/场景 | 颜色 | CSS 变量 | 用途 |
|----------|------|----------|------|
| 选中状态 | 蓝色 | `--primary` | 用户当前选中的卡片 |
| 已解锁标识 | 紫色 | `--purple` | 标识用户已解锁的等级（Badge/图标） |
| 默认状态 | 中性色 | `--border-light` | 未选中的卡片边框 |
| 悬停状态 | 蓝色 | `--primary` | 统一使用蓝色悬停效果 |
| 顶部边框 | 蓝色渐变 | `--primary → --primary-dark` | 所有卡片统一 |

**包容性设计理念**：
- ✅ **平等对待**：所有等级视觉权重相同，无颜色层级暗示
- ✅ **功能区分**：通过功能列表长度体现价值差异
- ✅ **清晰反馈**：蓝色表示选中/交互，紫色表示已拥有

---

## 🚫 禁止规则 - CSS 示例

### 绝对禁止
```css
/* ❌ 玻璃拟态效果 */
.forbidden {
  backdrop-filter: blur(20px);
}

/* ❌ 过度缩放（超过 1.15 倍）*/
.forbidden {
  transform: scale(1.3);
}

/* ❌ 过度圆角（超过 16px） */
.forbidden {
  border-radius: 24px;
}

/* ❌ 纯黑色 */
.forbidden {
  color: #000000;  /* 使用 var(--text-primary) */
}

/* ❌ 奇数间距 */
.forbidden {
  padding: 15px;  /* 必须是 4 的倍数 */
}

/* ❌ 过长动画（超过 300ms） */
.forbidden {
  transition: all 500ms;
}

/* ❌ 硬编码颜色 */
.forbidden {
  background: #3B82F6;  /* 使用 var(--primary) */
}

/* ❌ 卡片纯缩放（应配合位移）*/
.forbidden:hover {
  transform: scale(1.1);  /* 应该用 translateY(-4px) */
}
```

### ✅ 正确写法
```css
/* ✅ 使用 CSS 变量 */
.correct {
  background: var(--primary);
  color: var(--text-primary);
  padding: var(--space-4);  /* 16px，4 的倍数 */
  border-radius: var(--radius-md);  /* 8px */
  transition: all var(--duration-fast) var(--easing);  /* 150ms */
}

/* ✅ 卡片交互（位移 + 微缩放）*/
.correct:hover {
  transform: translateY(-4px);
}

.correct:active {
  transform: translateY(-2px) scale(0.99);
}
```

---

## 📏 缩放动画使用规范

### 允许的缩放范围
```css
/* ✅ 按钮按下 (0.95-1.00) */
.button:active {
  transform: scale(0.98);
}

/* ✅ 图标悬停 (1.00-1.15) */
.icon:hover {
  transform: scale(1.1);
}

/* ✅ 输入框焦点 (1.00-1.02) */
.input:focus {
  transform: scale(1.01);
}

/* ✅ 组合动画（缩放 + 位移） */
.card:active {
  transform: translateY(-2px) scale(0.99);
}

/* ✅ 弹性动画（心跳效果） */
@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.15); }
}

.icon-favorite.active {
  animation: heartBeat 300ms ease;
}
```

### 缩放比例参考表
| 场景 | 推荐比例 | 用途 | 示例代码 |
|------|---------|------|----------|
| 按下 | 0.95-0.98 | 反馈按压感 | `scale(0.98)` |
| 悬停 | 1.02-1.05 | 提示可交互 | `scale(1.03)` |
| 图标 | 1.1-1.15 | 强调吸引注意 | `scale(1.1)` |
| 焦点 | 1.01 | 微妙强调 | `scale(1.01)` |
| 弹性动画 | 1.15 | 心跳、脉动效果 | `scale(1.15)` |

---

## 🎨 微交互完整代码示例

> 以下是 10+ 种常见场景的完整实现代码

### 1. 按钮点击反馈

#### 方案A：缩放 + 阴影（推荐）
```css
.button {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  background: var(--primary);
  color: white;
  border: none;
  transition: all 150ms var(--easing-spring);
  cursor: pointer;
}

/* 悬停：轻微上浮 */
.button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-focus);
}

/* 按下：轻微缩小 + 下沉 */
.button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--shadow-xs);
}
```

#### 方案B：涟漪扩散（Material Design 风格）
```css
.button-ripple {
  position: relative;
  overflow: hidden;
  padding: 12px 20px;
  border-radius: var(--radius-md);
  background: var(--primary);
  color: white;
}

.button-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.4s, height 0.4s;
}

.button-ripple:active::after {
  width: 200px;
  height: 200px;
}
```

### 2. 卡片交互
```css
.card {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  transition: all 200ms var(--easing);
  position: relative;
}

/* 悬停：上浮 + 阴影增强 */
.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-hover-blue);
  border-color: var(--primary);
}

/* 顶部边框显现 */
.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--purple));
  opacity: 0;
  transition: opacity var(--duration-normal);
}

.card:hover::before {
  opacity: 1;
}

/* 点击反馈：轻微缩小 */
.card:active {
  transform: translateY(-2px) scale(0.99);
}
```

### 3. 开关/切换按钮
```css
.toggle {
  position: relative;
  width: 48px;
  height: 28px;
  background: var(--border-light);
  border-radius: 14px;
  transition: background 200ms var(--easing);
  cursor: pointer;
}

.toggle::after {
  content: '';
  position: absolute;
  left: 2px;
  top: 2px;
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 50%;
  transition: all 200ms var(--easing-spring);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* 激活状态 */
.toggle.active {
  background: var(--primary);
}

.toggle.active::after {
  transform: translateX(20px);
}

/* 点击时的微缩放 */
.toggle:active::after {
  transform: scale(0.95);
}

.toggle.active:active::after {
  transform: translateX(20px) scale(0.95);
}
```

### 4. 输入框焦点
```css
.input {
  padding: 12px 16px;
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  transition: all 200ms var(--easing);
}

/* 悬停提示 */
.input:hover {
  border-color: var(--border-medium);
}

/* 焦点反馈 */
.input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: var(--shadow-focus);
  transform: scale(1.01);  /* 微妙放大 */
}
```

### 5. 图标变化

#### 收藏/点赞图标
```css
.icon-favorite {
  color: var(--text-tertiary);
  transition: all 200ms var(--easing-spring);
  cursor: pointer;
}

.icon-favorite:hover {
  transform: scale(1.1);
  color: var(--pink);
}

.icon-favorite:active {
  transform: scale(0.95);
}

.icon-favorite.active {
  color: var(--pink);
  animation: heartBeat 300ms ease;
}

@keyframes heartBeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.15); }
}
```

#### 菜单图标（汉堡菜单）
```css
.menu-icon {
  cursor: pointer;
}

.menu-icon span {
  display: block;
  width: 24px;
  height: 2px;
  background: var(--text-primary);
  transition: all 200ms var(--easing);
  transform-origin: center;
}

.menu-icon span:nth-child(2) {
  margin: 6px 0;
}

/* 展开状态：变成 X */
.menu-icon.open span:nth-child(1) {
  transform: translateY(8px) rotate(45deg);
}

.menu-icon.open span:nth-child(2) {
  opacity: 0;
}

.menu-icon.open span:nth-child(3) {
  transform: translateY(-8px) rotate(-45deg);
}
```

### 6. 加载状态

#### 骨架屏动画
```css
.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-subtle) 0%,
    var(--border-light) 50%,
    var(--bg-subtle) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s ease-in-out infinite;
  border-radius: var(--radius-md);
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

#### 脉动效果
```css
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

#### 旋转加载器
```css
.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
```

### 7. 通知/Toast 提示
```css
.toast {
  padding: 16px 24px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  border-left: 4px solid var(--primary);
  animation: slideIn 300ms var(--easing);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 退出动画 */
.toast.exit {
  animation: slideOut 300ms var(--easing) forwards;
}

@keyframes slideOut {
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* 不同状态 */
.toast-success { border-left-color: #10B981; }
.toast-error { border-left-color: #EF4444; }
.toast-warning { border-left-color: #F59E0B; }
```

### 8. 下拉菜单
```css
.dropdown-menu {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--space-2);
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
  transition: all 200ms var(--easing);
  pointer-events: none;
}

.dropdown-menu.open {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: auto;
}

/* 菜单项 */
.dropdown-item {
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-sm);
  transition: background 150ms var(--easing);
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--bg-subtle);
}

.dropdown-item:active {
  transform: scale(0.98);
}
```

### 9. Modal/弹窗
```css
/* 背景遮罩 */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0);
  transition: background 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay.open {
  background: rgba(0, 0, 0, 0.5);
}

/* 弹窗内容 */
.modal-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  max-width: 500px;
  width: 90%;
  opacity: 0;
  transform: scale(0.9) translateY(20px);
  transition: all 300ms var(--easing-spring);
}

.modal-content.open {
  opacity: 1;
  transform: scale(1) translateY(0);
}
```

### 10. 滚动触发动画
```css
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 600ms var(--easing);
}

.fade-in-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 交错动画 */
.fade-in-up:nth-child(1) { transition-delay: 0ms; }
.fade-in-up:nth-child(2) { transition-delay: 100ms; }
.fade-in-up:nth-child(3) { transition-delay: 200ms; }
.fade-in-up:nth-child(4) { transition-delay: 300ms; }
```

---

## ✨ 性能优化代码

### GPU 加速
```css
/* ✅ 使用 transform 和 opacity（GPU 加速）*/
.optimized {
  transform: translateY(-4px);
  opacity: 0.8;
  will-change: transform;  /* 谨慎使用 */
}

/* ❌ 避免触发重排的属性 */
.not-optimized {
  width: 100px;  /* 会触发重排 */
  height: 100px;
  top: 10px;
  left: 10px;
}
```

### 可访问性支持
```css
/* 尊重用户的减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
```

---

**完整规范参考**：
- `UIAgentRules.md` - 设计原则和 AI 提示词
- `MicrointeractionsGuide.md` - 微交互场景详解
