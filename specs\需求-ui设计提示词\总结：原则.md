掌握CRAP四原则：对比、重复、对齐、亲密性

视觉上的秩序感：清晰的层级、留白、比例、字体节奏；

交互上的克制感：用户能预测结果，操作有即时反馈；

统一的设计语言：颜色、圆角、阴影、动效风格的一致性；

空间感与呼吸感：界面留白+轻微动效形成的节奏；

内容即设计：视觉服务于信息，而非喧宾夺主。

1. 网格与对齐

使用 8pt Grid System（即所有边距、圆角、间距是8的倍数）

元素必须对齐（垂直线、水平线）

保持视觉平衡：左重右轻，上重下轻

2. 留白与层级

少即是多：去掉无关元素，让视觉焦点集中

使用空间层级区分信息（卡片、阴影、模糊、深浅）

3. 字体与排版

不要混用太多字体：最多两种字体（标题/正文）

字体大小层级固定：H1/H2/H3/Text Small

适当增大行高（1.5倍）提升阅读舒适度

4. 色彩

使用中性色（灰、白、浅米、蓝灰）作为主色调，点缀色有限制

高级感往往来自 低饱和度 + 高对比度

多用渐变、玻璃拟态、或轻微发光边缘取代纯色块

5. 动效与交互

动效要有“物理感”：缓入缓出 (ease-in-out)

动效时间 150ms~300ms 之间最自然

反馈明确：悬浮高亮、点击波纹、禁用状态灰化

6. 一致性

所有按钮形状、阴影、交互动效保持统一风格

状态变化有一致的视觉语言（色彩变化或动效变化）