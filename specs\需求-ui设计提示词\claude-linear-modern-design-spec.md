# Linear 现代极简风 - 设计规范文档

## 📋 设计概述

**设计目标**：为C端专业用户打造的PC端AI工具定价页面，强调清晰、专业、舒适的长时间使用体验。

**核心理念**：
- 去除玻璃拟态的视觉疲劳
- 保留现代感和科技感
- 参考 Linear 的极简美学
- 适配PC大屏幕场景

---

## 🎨 色彩系统

### 主色系（蓝色 - 核心功能）
```css
/* 主蓝色 */
--primary-blue: #3B82F6;      /* 主要操作、链接 */
--primary-blue-dark: #2563EB; /* 渐变终点、深色状态 */
--primary-blue-light: #60A5FA; /* 浅色状态 */

/* 蓝色背景色阶 */
--blue-50: #EFF6FF;
--blue-100: #DBEAFE;
--blue-200: #BFDBFE;
--blue-500: #3B82F6;
--blue-600: #2563EB;
```

### 辅助色系（紫色 - Plus等级强调）
```css
/* 紫色 */
--purple-500: #8B5CF6;
--purple-600: #7C3AED;
--purple-700: #6D28D9;

/* 紫色背景色阶 */
--purple-50: #F5F3FF;
--purple-100: #EDE9FE;
--purple-200: #DDD6FE;
```

### 点缀色系（粉红色 - Max等级强调）
```css
/* 粉红色 */
--pink-500: #EC4899;
--pink-600: #DB2777;
--pink-700: #C026D3;

/* 粉红色背景色阶 */
--pink-50: #FDF4FF;
--pink-100: #FCE7F3;
--pink-200: #FBCFE8;
```

### 中性色系（基础UI）
```css
/* 文字色 */
--text-primary: #111827;      /* 标题、价格 */
--text-secondary: #374151;    /* 正文、功能列表 */
--text-tertiary: #6B7280;     /* 辅助信息、说明文字 */

/* 背景色 */
--bg-base: #FAFBFC;           /* 页面底色 */
--bg-elevated: #FFFFFF;       /* 卡片背景 */
--bg-subtle: #F9FAFB;         /* 悬停背景 */
--bg-gradient: linear-gradient(180deg, #FAFBFC 0%, #F5F7FA 100%);

/* 边框色 */
--border-light: #E5E7EB;      /* 默认边框 */
--border-medium: #D1D5DB;     /* 悬停边框 */
--border-strong: #9CA3AF;     /* 强调边框 */
```

---

## 📐 间距系统（8pt Grid）

```css
/* 基础间距单位 */
--space-1: 4px;    /* 0.25rem */
--space-2: 8px;    /* 0.5rem */
--space-3: 12px;   /* 0.75rem */
--space-4: 16px;   /* 1rem */
--space-5: 20px;   /* 1.25rem */
--space-6: 24px;   /* 1.5rem */
--space-8: 32px;   /* 2rem */
--space-10: 40px;  /* 2.5rem */
--space-12: 48px;  /* 3rem */
--space-16: 64px;  /* 4rem */

/* 应用场景 */
卡片内边距：32px (--space-8)
卡片间距：16px (--space-4)
功能列表项间距：12px (--space-3)
Badge内边距：6px 12px (--space-1.5 --space-3)
按钮内边距：12px 20px (--space-3 --space-5)
```

---

## 🔤 字体系统

### 字体家族
```css
font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
             'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
```

### 字体大小与层级
```css
/* 标题 */
--font-title-xl: 32px;    /* 页面主标题 */
--font-title-lg: 24px;    /* 区块标题 */
--font-title-md: 20px;    /* 卡片标题 */

/* 价格 */
--font-price: 48px;       /* 价格数字 */
--font-price-currency: 24px; /* 货币符号 */

/* 正文 */
--font-base: 14px;        /* 正文、按钮 */
--font-sm: 13px;          /* 辅助文字 */
--font-xs: 12px;          /* Badge、标签 */
--font-2xs: 10px;         /* Popular标签 */

/* 字重 */
--font-regular: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;

/* Letter Spacing（Linear特色） */
--tracking-tight: -0.03em;  /* 价格数字 */
--tracking-normal: -0.02em; /* 标题 */
--tracking-wide: 0.5px;     /* Badge大写字母 */
```

### 行高
```css
--leading-none: 1;        /* 价格 */
--leading-tight: 1.25;    /* 标题 */
--leading-normal: 1.5;    /* 正文 */
--leading-relaxed: 1.6;   /* 长文本 */
```

---

## 🎭 圆角系统

```css
/* Linear风格：较小的圆角 */
--radius-sm: 6px;    /* Badge、小按钮 */
--radius-md: 8px;    /* 按钮 */
--radius-lg: 12px;   /* 卡片 */
--radius-xl: 16px;   /* 容器 */
--radius-full: 9999px; /* 圆形元素 */
```

**设计原则**：
- 相比方案1（8/16px），这里使用更小的圆角（6/8/12px）
- 更贴近 Linear 的精致感
- 避免过度圆润导致的"卡通感"

---

## 🌓 阴影系统

```css
/* Linear特色：轻盈的阴影 */
--shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
--shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
--shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
--shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);

/* 悬停状态：带颜色的阴影 */
--shadow-hover-blue: 
  0 0 0 1px #3B82F6,
  0 12px 24px -8px rgba(59, 130, 246, 0.15),
  0 24px 48px -8px rgba(0, 0, 0, 0.08);

--shadow-hover-purple:
  0 0 0 1px #8B5CF6,
  0 12px 24px -8px rgba(139, 92, 246, 0.15),
  0 24px 48px -8px rgba(0, 0, 0, 0.08);

--shadow-hover-pink:
  0 0 0 1px #EC4899,
  0 12px 24px -8px rgba(236, 72, 153, 0.15),
  0 24px 48px -8px rgba(0, 0, 0, 0.08);

/* 按钮焦点阴影 */
--shadow-focus-blue: 0 0 0 3px rgba(59, 130, 246, 0.1);
--shadow-focus-pink: 0 0 0 3px rgba(236, 72, 153, 0.1);
```

**设计原则**：
- 默认状态使用极轻的阴影（几乎不可见）
- 悬停状态使用彩色阴影增强反馈
- 多层阴影叠加产生景深感

---

## ⚡ 动效系统

### 时长
```css
--duration-instant: 100ms;  /* 即时反馈（按钮按下） */
--duration-fast: 150ms;     /* 快速反馈（悬停） */
--duration-normal: 200ms;   /* 标准动画（大部分场景） */
--duration-slow: 300ms;     /* 慢速动画（复杂变化） */
```

### 缓动函数
```css
/* Linear使用的缓动 */
--easing-linear: cubic-bezier(0, 0, 1, 1);
--easing-ease: cubic-bezier(0.4, 0, 0.2, 1);  /* 标准 */
--easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
--easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
```

### 动效应用

#### 卡片悬停
```css
.pricing-card {
  transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.pricing-card:hover {
  transform: translateY(-4px);
  border-color: #3B82F6;
  box-shadow: var(--shadow-hover-blue);
}
```

#### 按钮交互
```css
.button {
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-focus-blue);
}

.button:active {
  transform: translateY(0);
}
```

#### 顶部彩色边框动画
```css
.card::before {
  content: '';
  height: 3px;
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
  opacity: 0;
  transition: opacity 200ms;
}

.card:hover::before {
  opacity: 1;
}
```

---

## 🎯 核心组件设计

### 1. 定价卡片（Pricing Card）

#### 基础结构
```html
<div class="pricing-card">
  <span class="card-badge">Badge</span>
  <h3 class="card-title">标题</h3>
  <div class="card-price">
    <span class="currency">¥</span>
    <span>29</span>
  </div>
  <p class="card-period">每月</p>
  <ul class="card-features">
    <li><span class="feature-icon">✓</span> 功能描述</li>
  </ul>
  <button class="card-cta">按钮文字</button>
</div>
```

#### 样式细节
```css
.pricing-card {
  background: white;
  border-radius: 12px;
  padding: 32px;
  border: 1.5px solid #E5E7EB;  /* 注意：1.5px精细边框 */
  position: relative;
}

/* Linear特色：顶部彩色边框 */
.pricing-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3B82F6, #8B5CF6);
  opacity: 0;
  transition: opacity 200ms;
}

.pricing-card:hover::before {
  opacity: 1;
}
```

#### 四种卡片变体

**1. Free 卡片（灰色系）**
```css
.badge-free {
  background: #F9FAFB;
  color: #6B7280;
  border: 1px solid #E5E7EB;
}
```

**2. Pro 卡片（蓝色系 + Popular标签）**
```css
.card.popular {
  border-color: #3B82F6;
  background: linear-gradient(180deg, #FAFBFF 0%, #FFFFFF 100%);
}

.card.popular::before {
  opacity: 1;  /* 始终显示顶部边框 */
}

.card.popular::after {
  content: 'POPULAR';
  position: absolute;
  top: 12px;
  right: 12px;
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: white;
  padding: 4px 10px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 700;
}

.badge-pro {
  background: linear-gradient(135deg, #EFF6FF, #DBEAFE);
  color: #2563EB;
  border: 1px solid #93C5FD;
}
```

**3. Plus 卡片（紫色强调）**
```css
.card.highlight-purple::before {
  background: linear-gradient(90deg, #8B5CF6, #C026D3);
}

.card.highlight-purple:hover {
  border-color: #8B5CF6;
  box-shadow: var(--shadow-hover-purple);
}

.badge-plus {
  background: linear-gradient(135deg, #F5F3FF, #EDE9FE);
  color: #7C3AED;
  border: 1px solid #C4B5FD;
}
```

**4. Max 卡片（粉色强调）**
```css
.card.highlight-pink::before {
  background: linear-gradient(90deg, #EC4899, #C026D3);
}

.card.highlight-pink:hover {
  border-color: #EC4899;
  box-shadow: var(--shadow-hover-pink);
}

.card.highlight-pink .cta-primary {
  background: linear-gradient(135deg, #EC4899, #C026D3);
}

.badge-max {
  background: linear-gradient(135deg, #FDF4FF, #FCE7F3);
  color: #C026D3;
  border: 1px solid #F0ABFC;
}
```

---

### 2. Badge（等级标签）

#### 设计特点
- 使用渐变背景（双色渐变）
- 1px彩色边框增强层次
- 大写字母 + 0.5px letter-spacing
- 6px小圆角

```css
.card-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
}
```

---

### 3. 按钮（CTA Button）

#### 主要按钮（Primary）
```css
.cta-primary {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: -0.01em;
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  color: white;
  border: none;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.cta-primary:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 0 0 3px rgba(59, 130, 246, 0.1),
    0 4px 12px rgba(59, 130, 246, 0.3);
}

.cta-primary:active {
  transform: translateY(0);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

#### 次要按钮（Secondary）
```css
.cta-secondary {
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  background: white;
  color: #374151;
  border: 1.5px solid #E5E7EB;
  transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

.cta-secondary:hover {
  background: #F9FAFB;
  border-color: #D1D5DB;
  transform: translateY(-1px);
}
```

---

### 4. 功能图标（Feature Icon）

```css
.feature-icon {
  width: 18px;
  height: 18px;
  border-radius: 4px;  /* 方形圆角，不是圆形 */
  background: linear-gradient(135deg, #3B82F6, #2563EB);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  flex-shrink: 0;
  margin-top: 2px;  /* 与文字基线对齐 */
}
```

**设计考量**：
- 使用方形圆角而非圆形（更现代）
- 渐变填充增加质感
- 2px上边距与文字对齐

---

## 🎨 Linear 风格核心特征

### 1. 微妙但精致的细节
- **1.5px 边框**：比1px更明显，比2px更精致
- **3px 顶部边框**：细小但有仪式感
- **渐变背景 Badge**：双色渐变 + 边框 = 立体感
- **方形圆角图标**：4px圆角矩形比圆形更硬朗

### 2. 克制的动效
- **悬停距离短**：translateY(-4px) 而非 (-8px)
- **快速反馈**：150-200ms 而非 300ms
- **颜色化阴影**：悬停时阴影带有品牌色

### 3. 清晰的视觉层级
- **Free**: 灰色系，低优先级
- **Pro**: 蓝色系 + Popular标签，高优先级
- **Plus**: 紫色强调，进阶选择
- **Max**: 粉色强调，顶级方案

### 4. 高可读性
- **纯白背景**：无透明度
- **高对比度文字**：#111827 vs #FFFFFF
- **充足行高**：1.5 倍行高确保舒适

---

## 📊 与玻璃拟态风格对比

| 特性 | 玻璃拟态风格 | Linear 现代极简风 |
|------|-------------|------------------|
| **背景透明度** | 15% | 0%（纯白） |
| **毛玻璃效果** | backdrop-blur(20px) | 无 |
| **边框粗细** | 1px | 1.5px |
| **圆角大小** | 20px | 12px |
| **悬停位移** | -8px scale(1.02) | -4px |
| **动画时长** | 300ms | 200ms |
| **阴影层次** | 单层模糊 | 多层彩色阴影 |
| **色彩系统** | 渐变背景喧宾夺主 | 白色底+彩色点缀 |
| **可读性** | 中（透明度影响） | 高 |
| **性能** | 差（blur开销大） | 优秀 |
| **长时间使用** | 易疲劳 | 舒适 |

---

## 🚀 实现要点

### 1. CSS 变量组织
```css
/* theme.css */
:root {
  /* Colors */
  --primary: #3B82F6;
  --purple: #8B5CF6;
  --pink: #EC4899;
  
  /* Spacing */
  --space-3: 12px;
  --space-8: 32px;
  
  /* Radius */
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* Shadow */
  --shadow-hover-blue: 
    0 0 0 1px #3B82F6,
    0 12px 24px -8px rgba(59, 130, 246, 0.15),
    0 24px 48px -8px rgba(0, 0, 0, 0.08);
  
  /* Animation */
  --duration-fast: 150ms;
  --easing: cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 2. 组件化开发建议
```typescript
// PricingCard.tsx
interface PricingCardProps {
  tier: 'free' | 'pro' | 'plus' | 'max';
  isPopular?: boolean;
}

const tierStyles = {
  free: { badge: 'badge-free', highlight: null },
  pro: { badge: 'badge-pro', highlight: 'popular' },
  plus: { badge: 'badge-plus', highlight: 'highlight-purple' },
  max: { badge: 'badge-max', highlight: 'highlight-pink' },
};
```

### 3. 响应式适配
```css
/* 大屏（1400px+）：4列 */
@media (min-width: 1400px) {
  .pricing-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }
}

/* 中屏（768px-1400px）：2列 */
@media (min-width: 768px) and (max-width: 1399px) {
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

/* 小屏（<768px）：1列 */
@media (max-width: 767px) {
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .pricing-card {
    padding: 24px;
  }
}
```

---

## 🎯 设计决策记录

### 为什么选择 1.5px 边框？
- 1px 在高分辨率屏幕上过细
- 2px 在某些场景下过粗
- 1.5px 是最佳平衡点

### 为什么使用顶部 3px 彩色边框？
- Linear 的经典设计语言
- 提供微妙的视觉层次
- 悬停时出现，增加互动感

### 为什么 Plus 用紫色、Max 用粉色？
- 蓝色（Pro）→ 紫色（Plus）→ 粉色（Max）形成自然过渡
- 紫色代表"高级"，粉色代表"极致"
- 避免全部使用蓝色导致单调

### 为什么按钮使用渐变而非纯色？
- 微妙的渐变增加质感
- 与 Badge 的渐变形成呼应
- 比纯色更现代

---

## 📚 参考资源

### 设计系统参考
- **Linear**: https://linear.app
- **Vercel**: https://vercel.com/pricing
- **Notion**: https://notion.so/pricing

### 技术实现参考
- Tailwind CSS 官方文档
- Radix UI 组件库
- CSS Tricks: "A Complete Guide to Flexbox"

---

## ✅ 验收标准

### 视觉质量
- [ ] 所有间距都是 8 的倍数
- [ ] 颜色对比度 ≥ 4.5:1（WCAG AA）
- [ ] 边框粗细一致（1.5px）
- [ ] 圆角大小一致（12px）
- [ ] 渐变方向一致（135deg）

### 交互体验
- [ ] 悬停动画流畅（200ms）
- [ ] 按钮有明确的按下状态
- [ ] 所有交互元素有焦点状态
- [ ] 键盘可访问

### 性能指标
- [ ] 无 backdrop-filter 导致的卡顿
- [ ] 动画帧率 ≥ 60fps
- [ ] 首次内容绘制 < 1.5s

### 响应式适配
- [ ] 1920px 下 4 列布局完美
- [ ] 1024px 下 2 列布局舒适
- [ ] 375px 下 1 列布局无横向滚动

---

## 🔄 后续优化方向

1. **暗色模式**：为夜间使用优化
2. **微交互**：Price 数字滚动动画
3. **对比表格**：详细功能对比视图
4. **推荐算法**：根据用户行为推荐套餐
5. **社会证明**：添加用户数、评价等元素

---

**设计完成日期**：2025年10月31日  
**设计师**：AI Assistant  
**审核状态**：待用户确认
