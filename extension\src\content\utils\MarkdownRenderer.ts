import { marked } from 'marked';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js';
import katex from 'katex';

/**
 * Markdown 渲染工具类
 * 支持代码高亮和数学公式渲染
 */
export class MarkdownRenderer {
    private static instance: MarkdownRenderer;

    private constructor() {
        this.configureMarked();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): MarkdownRenderer {
        if (!MarkdownRenderer.instance) {
            MarkdownRenderer.instance = new MarkdownRenderer();
        }
        return MarkdownRenderer.instance;
    }

    /**
     * 配置 marked 选项
     */
    private configureMarked(): void {
        marked.setOptions({
            breaks: true,
            gfm: true,
            pedantic: false,
            silent: false,
        });

        // 自定义渲染器
        const renderer = new marked.Renderer();

        // 代码块渲染（带高亮）
        renderer.code = ({ text, lang }: { text: string; lang?: string }) => {
            const validLanguage = lang && hljs.getLanguage(lang) ? lang : 'plaintext';
            const highlighted = hljs.highlight(text, { language: validLanguage }).value;
            return `<pre><code class="hljs language-${validLanguage}">${highlighted}</code></pre>`;
        };

        // 行内代码渲染
        renderer.codespan = ({ text }: { text: string }) => {
            return `<code class="inline-code">${text}</code>`;
        };

        marked.use({ renderer });
    }

    /**
     * 渲染 Markdown 为 HTML
     * @param markdown Markdown 文本
     * @returns 渲染后的安全 HTML
     */
    public render(markdown: string): string {
        if (!markdown) {
            return '';
        }

        try {
            // 1. 预处理数学公式（$$...$$  和 $...$）
            let processed = this.processMathFormulas(markdown);

            // 2. 使用 marked 渲染 Markdown
            const rawHtml = marked.parse(processed) as string;

            // 3. 使用 DOMPurify 清理 HTML（防 XSS）
            const cleanHtml = DOMPurify.sanitize(rawHtml, {
                ALLOWED_TAGS: [
                    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                    'p', 'br', 'hr',
                    'strong', 'em', 'u', 's', 'code', 'pre',
                    'ul', 'ol', 'li',
                    'blockquote',
                    'a',
                    'table', 'thead', 'tbody', 'tr', 'th', 'td',
                    'span', 'div',
                    'img'
                ],
                ALLOWED_ATTR: [
                    'href', 'title', 'target', 'rel',
                    'class', 'id',
                    'src', 'alt',
                    'style' // KaTeX 需要 style 属性
                ],
                ALLOW_DATA_ATTR: false,
            });

            return cleanHtml;
        } catch (error) {
            console.error('[MarkdownRenderer] 渲染失败', error);
            return `<p style="color: red;">渲染失败: ${error instanceof Error ? error.message : '未知错误'}</p>`;
        }
    }

    /**
     * 预处理数学公式
     * 将 LaTeX 公式转换为 HTML
     */
    private processMathFormulas(text: string): string {
        // 处理块级公式 $$...$$
        text = text.replace(/\$\$([\s\S]+?)\$\$/g, (match, formula) => {
            try {
                return katex.renderToString(formula.trim(), {
                    displayMode: true,
                    throwOnError: false,
                    output: 'html',
                });
            } catch (error) {
                console.error('[MarkdownRenderer] 块级公式渲染失败', error);
                return `<div class="math-error">${match}</div>`;
            }
        });

        // 处理行内公式 $...$（避免误匹配，如 $100）
        text = text.replace(/\$([^$\n]+?)\$/g, (match, formula) => {
            // 简单的验证：如果包含数学符号，才认为是公式
            if (/[a-zA-Z\\{}^_]/.test(formula)) {
                try {
                    return katex.renderToString(formula.trim(), {
                        displayMode: false,
                        throwOnError: false,
                        output: 'html',
                    });
                } catch (error) {
                    console.error('[MarkdownRenderer] 行内公式渲染失败', error);
                    return `<span class="math-error">${match}</span>`;
                }
            }
            return match; // 不是公式，保持原样
        });

        return text;
    }

    /**
     * 获取所需的 CSS 样式链接
     * 返回需要注入到页面的 CSS 链接
     */
    public static getRequiredStyles(): string[] {
        return [
            // Highlight.js 样式（可选不同主题）
            'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css',
            // KaTeX 样式
            'https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css',
        ];
    }
}

// 导出单例实例
export const markdownRenderer = MarkdownRenderer.getInstance();
