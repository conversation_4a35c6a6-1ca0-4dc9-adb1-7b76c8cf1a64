/* HistoryBubble 模态页样式 */

/* 模态页容器 */
.echosync-history-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2147483647;
  display: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* 背景遮罩 */
.echosync-modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 降级方案：不支持backdrop-filter的浏览器 */
@supports not (backdrop-filter: blur(8px)) {
  .echosync-modal-backdrop {
    background: rgba(0, 0, 0, 0.6);
  }
}

/* 内容容器 */
.echosync-modal-content {
  position: absolute;
  /* 位置将由 TypeScript 动态设置 */
  width: 80vw !important;
  height: 80vh !important;
  max-width: none !important;
  max-height: none !important;
  display: flex !important;
  flex-direction: column !important;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  overflow: hidden;
  will-change: transform, opacity;
  
  /* 初始状态：缩小到0，位置和transform-origin由JS设置 */
  opacity: 0;
  transform: scale(0);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 显示状态 */
.echosync-history-modal.show .echosync-modal-backdrop {
  opacity: 1;
}

.echosync-history-modal.show .echosync-modal-content {
  opacity: 1;
  transform: scale(1);
  /* 最终位置：屏幕中心 */
  top: 50% !important;
  left: 50% !important;
  margin-left: -40vw !important;
  margin-top: -40vh !important;
}

/* 标题区域 */
.echosync-history-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(139, 92, 246, 0.03);
  position: sticky;
  top: 0;
  z-index: 1;
}

.echosync-history-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  text-align: center;
}

/* 历史记录列表容器 */
.echosync-history-list {
  flex: 1 !important;
  overflow-y: auto !important;
  min-height: 0 !important;
  padding: 8px 0;
}

/* 滚动条样式 */
.echosync-history-list::-webkit-scrollbar {
  width: 6px;
}

.echosync-history-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.echosync-history-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.echosync-history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 历史记录项 */
.echosync-history-item {
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.echosync-history-item:last-child {
  border-bottom: none;
}

.echosync-history-item:hover {
  background: rgba(139, 92, 246, 0.05);
  transform: translateX(4px);
}

.echosync-history-item:active {
  background: rgba(139, 92, 246, 0.1);
  transform: translateX(2px) scale(0.98);
}

/* 历史记录项内容 */
.echosync-history-item-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.echosync-history-item-text {
  flex: 1;
  min-width: 0;
}

.echosync-history-item-prompt {
  color: #374151;
  font-weight: 500;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  line-height: 1.5;
}

.echosync-history-item-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

.echosync-history-item-meta .echosync-history-item-time {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 6px !important;
}



.echosync-history-item-platforms {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 空状态 */
.echosync-history-empty {
  padding: 40px 24px;
  text-align: center;
  color: #6b7280;
  font-size: 14px;
}

/* Toast 提示 */
.echosync-toast {
  position: fixed;
  z-index: 2147483648;
  background: rgba(34, 197, 94, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transform: translateY(10px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  white-space: nowrap;
}

.echosync-toast.show {
  opacity: 1;
  transform: translateY(0);
}

.echosync-toast.error {
  background: rgba(239, 68, 68, 0.95);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .echosync-modal-content {
    width: 95%;
    max-width: none;
    max-height: 85vh;
    border-radius: 12px;
  }
  
  .echosync-history-header {
    padding: 16px 20px 12px;
  }
  
  .echosync-history-title {
    font-size: 16px;
  }
  
  .echosync-history-item {
    padding: 14px 20px;
  }
  
  .echosync-history-item-prompt {
    -webkit-line-clamp: 2;
  }
  
  .echosync-history-list {
    max-height: 65vh;
  }
}

@media (max-width: 480px) {
  .echosync-modal-content {
    width: 98%;
    max-height: 90vh;
    border-radius: 8px;
  }
  
  .echosync-history-header {
    padding: 12px 16px 8px;
  }
  
  .echosync-history-item {
    padding: 12px 16px;
  }
  
  .echosync-history-item-content {
    gap: 8px;
  }
}

/* 动画关键帧 */
@keyframes echosync-modal-show {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes echosync-modal-hide {
  from {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

@keyframes echosync-backdrop-show {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes echosync-backdrop-hide {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 搜索和筛选区域 */
.echosync-history-filters {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.5);
}

.echosync-filters-row {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.echosync-search-box {
  flex: 1;
  min-width: 200px;
  /* max-width: 400px; */
  position: relative;
}

.echosync-search-input {
  width: 95%;
  padding: 8px 12px 8px 36px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
}

.echosync-search-input:focus {
  outline: none;
  border-color: #8b5cf6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.echosync-search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.echosync-time-filter {
  display: flex;
  gap: 4px;
  /* flex-shrink: 0; */
}

.echosync-filter-btn {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.echosync-filter-btn:hover {
  background: rgba(139, 92, 246, 0.1);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.echosync-filter-btn.active {
  background: #8b5cf6;
  border-color: #8b5cf6;
  color: white;
}

/* 统计信息 */
.echosync-history-stats {
  padding: 8px 24px;
  font-size: 12px;
  color: #6b7280;
  background: rgba(249, 250, 251, 0.8);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

/* 分页控件 */
.echosync-pagination {
  padding: 16px 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.5);
}

.echosync-pagination-btn {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  text-align: center;
}

.echosync-pagination-btn:hover:not(:disabled) {
  background: rgba(139, 92, 246, 0.1);
  border-color: #8b5cf6;
  color: #8b5cf6;
}

.echosync-pagination-btn.active {
  background: #8b5cf6;
  border-color: #8b5cf6;
  color: white;
}

.echosync-pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.echosync-pagination-info {
  margin: 0 12px;
  font-size: 12px;
  color: #6b7280;
}

/* 优化时间显示 - 强制单行显示 */
.echosync-history-item-time {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 6px !important;
  font-size: 11px !important;
  color: #9ca3af !important;
  flex-shrink: 0 !important;
  white-space: nowrap !important;
}

.echosync-time-relative {
  font-weight: 500 !important;
  display: inline !important;
}

.echosync-time-absolute {
  font-size: 10px !important;
  opacity: 0.7 !important;
  display: inline !important;
}
