import { BasePageService } from "@/content/core/BasePageService";

/**
 * Dola 页面状态管理服务
 * 继承 BasePageService，提供 Dola 平台特定的 URL 匹配规则
 * 
 * @特殊说明
 * - 支持两个域名: www.dola.com 和 www.cici.com (旧版，会重定向到 dola.com)
 * - Home页: /chat/ 或 /chat/?from_login=xxx
 * - Chat页: /chat/{chatId}，其中 chatId 是数字
 */
export class DolaPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Dola';
    }

    /**
     * 获取 Dola 的 URL 匹配模式
     * 支持 dola.com 和 cici.com 两个域名
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://www.dola.com/chat/ 或 https://www.dola.com/chat/?from_login=xxx
            // 同时支持 cici.com (会重定向)
            home: /^https:\/\/www\.(dola|cici)\.com\/chat\/(\?.*)?$/i,
            
            // Chat页模式: https://www.dola.com/chat/{chatId}
            // chatId 是纯数字，例如: /chat/214073385654545
            chat: /^https:\/\/www\.(dola|cici)\.com\/chat\/(\d+)$/i
        };
    }

    /**
     * 检测欢迎页面（Dola 特定实现）
     * 
     * @returns {boolean} 是否为欢迎页
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（Dola 特有的欢迎页面元素）
        // 欢迎页有 "新对话" 按钮且没有消息历史
        return document.querySelector('[data-testid="create_conversation_button"]') !== null &&
               document.querySelector('[data-testid="message-list"]') === null;
    }
    
    /**
     * 检测聊天页面（Dola 特定实现）
     * 
     * @returns {boolean} 是否为聊天页
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测（存在消息列表）
        return document.querySelector('[data-testid="message-list"]') !== null ||
               document.querySelector('[data-testid="union_message"]') !== null;
    }

    /**
     * 从 URL 中提取 chatId
     * 
     * @returns {string | null} chatId 或 null（如果不在聊天页）
     * 
     * @example
     * // URL: https://www.dola.com/chat/214073385654545
     * getChatId() // 返回 "214073385654545"
     */
    public getChatId(): string | null {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat && urlResult.chatId) {
            return urlResult.chatId;
        }
        
        // 降级从 DOM 获取 chatId（从 URL 或数据属性）
        const match = window.location.pathname.match(/\/chat\/(\d+)/);
        if (match) {
            return match[1];
        }
        
        // 尝试从激活的对话项获取
        const activeChat = document.querySelector('[data-testid="chat_list_thread_item"].active-link');
        if (activeChat) {
            const idMatch = activeChat.id.match(/conversation_(\d+)/);
            if (idMatch) {
                return idMatch[1];
            }
        }
        
        return null;
    }

    /**
     * 检测是否为 Dola 平台页面
     * 支持 dola.com 和 cici.com 两个域名
     * 
     * @returns {boolean} 是否为 Dola 页面
     */
    public isDolaPage(): boolean {
        const hostname = window.location.hostname.toLowerCase();
        return hostname.includes('dola.com') || hostname.includes('cici.com');
    }

    /**
     * 获取当前机器人/模型信息
     * 
     * @returns {string | null} 机器人名称或 null
     */
    public getBotName(): string | null {
        // 从头部获取机器人名称
        const headerTitle = document.querySelector('[data-testid="chat_route_layout"] .title');
        if (headerTitle && headerTitle.textContent) {
            return headerTitle.textContent.trim();
        }
        
        // 从工作区图标获取
        const workspaceIcon = document.querySelector('[data-testid="workspace_icon"] .title');
        if (workspaceIcon && workspaceIcon.textContent) {
            return workspaceIcon.textContent.trim();
        }
        
        return 'Dola'; // 默认返回平台名称
    }
}
