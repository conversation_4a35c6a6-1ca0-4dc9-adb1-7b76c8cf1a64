import { SelectorConfig } from "./SelectorManager";

/**
 * <PERSON><PERSON><PERSON> (豆包) 适配器的选择器配置
 * 整合了所有 Doubao 平台特有的选择器
 * 
 * @特殊说明
 * - Doubao 域名: www.doubao.com
 * - Home页: /chat/ 或 /chat/?from_login=xxx
 * - Chat页: /chat/{chatId}，其中 chatId 是数字
 * - 使用 data-testid 属性提供稳定的选择器
 * - 答案完成标志: 操作栏(message_action_bar)的 opacity 变为 100
 * - 页面逻辑与 Dola 完全一致
 */
export const doubaoSelector: SelectorConfig = {
  /**
   * 输入框选择器
   * Doubao 使用 textarea 组件作为输入框
   */
  inputField: [
    '[data-testid="chat_input_input"]',           // 主输入框 (textarea)
    'textarea[placeholder*="发消息"]',             // 通过 placeholder 识别
    '.chat-input-container textarea',              // 通过父容器识别
  ],
  
  /**
   * 发送按钮选择器
   */
  sendButton: [
    '[data-testid="chat_input_send_button"]',     // 主发送按钮标识 (稳定)
    'button[aria-label="发送"]',                   // 中文发送按钮
    'button[aria-label="Send"]',                   // 英文发送按钮
    '#flow-end-msg-send',                          // 发送按钮 ID
  ],
  
  /**
   * 页面头部内容选择器（对话标题等）
   */
  headerContent: [
    // '[data-testid="chat_route_layout"]',          // 聊天路由布局
    // '.simple-header',                              // 简单头部样式
    '[class*="group/title"]',                      // 包含 group/title 的头部
  ],

  /**
   * 聊天内容列表容器选择器
   * Doubao 使用滚动视图布局
   */
  chatContentList: [
    '[data-testid="message-list"]:has([class*="inter-"])',  // 消息列表容器（包含动态hash类）
    '[data-testid="message-list"]',               // 消息列表容器（降级）
    // '[data-testid="scroll_view"]',                // 滚动视图容器
    // '.message-list',                               // 消息列表样式类
  ],

  /**
   * 问答对容器选择器
   * Doubao 使用 union_message 作为问答对的基本单元
   */
  messageTuple: [
    '[data-testid="union_message"]',              // 统一消息容器 (推荐)
    '[data-testid="message-block-container"]',    // 消息块容器
  ],

  /**
   * 用户提问项选择器
   * 在 Doubao 中，用户消息用 send_message 标识
   */
  promptItem: [
    // 选择所有位于 data-testid="send_message" 元素内部的 data-testid="message_content" 元素
    // message_content中携带了 data-message-id="212965496389393" 属性，可以获得消息id
    '[data-testid="send_message"] [data-testid="message_content"]',              
    // '.flex.flex-row.w-full.justify-end',          // 右对齐的消息行
  ],

  /**
   * 用户提问内容选择器
   */
  promptContent: [
    // 直接获得内容文本
    '[data-testid="message_text_content"]',  
  ],

  /**
   * 用户提问操作区选择器
   */
  promptAction: [
    '[data-testid="send_message"] [data-testid="message_action_bar"]',    // 发送消息的操作栏
    '[data-testid="send_message"] button',                                 // 发送消息中的按钮
  ],

  /**
   * AI回答项选择器
   * 在 Doubao 中，AI答案用 receive_message 标识
   */
  answerItem: [
    '[data-testid="receive_message"]:has([data-testid="message_content"])',  // 接收消息容器 (AI 答案)，必须包含 message_action_bar 子节点
    // '.flex.flex-row.w-full:not(.justify-end)',    // 左对齐的消息行 (非右对齐)
  ],

  /**
   * 答案完成标志选择器 ⚠️ 关键
   * 当答案完成时，操作栏的 opacity 会从 0 变为 100
   * 
   * ⚠️ Doubao特殊处理：
   * - 历史消息的操作栏也存在但是 opacity-0 (隐藏状态)
   * - 我们接受所有状态的操作栏，因为只需要它的位置来插入导出按钮
   * - 不需要模拟点击复制按钮，直接从 DOM 提取内容
   */
  answerCompletion: [
    '[data-testid="message_action_bar"]',              // 操作栏 (包括隐藏状态)
    '[class*="message-action-bar"]',                   // 动态类名匹配 (如 message-action-bar-raqbg0)
  ],

  /**
   * 复制按钮选择器
   */
  copyButton: [
    '[data-testid="message_action_copy"]',        // 复制操作按钮 (稳定)
    // 'button[aria-label*="复制"]',                  // 中文"复制"按钮
    // 'button[aria-label*="Copy"]',                  // 英文"Copy"按钮
  ],

  /**
   * Markdown内容选择器
   * 用于提取答案的Markdown内容
   */
  markdown: [
    '[data-testid="message_text_content"]',                           // 消息文本内容
    // '[data-testid="receive_message"] .flow-markdown-body',            // Flow Markdown 主体
    // '[data-testid="receive_message"] .mdbox-theme-next',              // Markdown 主题容器
    // '.container-P2rR72',                                              // Markdown 容器类
  ],

  /**
   * 代码块选择器
   * Doubao 使用标准的代码块组件
   */
  codeBlock: [
    'pre code',                                   // 标准代码块
    '[class*="code-block"]',                      // 代码块样式类
    '.hljs',                                      // 高亮代码块
  ],

  /**
   * 消息元素选择器
   * 用于识别单个消息（问题或答案）
   */
  messageElement: [
    '[data-testid="union_message"]',              // 统一消息元素
    '[data-message-id]',                          // 包含消息ID的元素
    '[data-testid="message-block-container"]',    // 消息块容器
  ],

  /**
   * 机器人头像/信息选择器
   * 用于识别当前使用的AI模型
   */
  botInfo: [
    '[data-testid="chat_header_avatar_button"]',  // 聊天头部头像按钮
    '[data-testid="workspace_icon"]',             // 工作区图标
  ],

};
