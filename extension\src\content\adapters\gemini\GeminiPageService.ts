import { BasePageService } from "@/content/core/BasePageService";

/**
 * Gemini 页面状态管理服务
 * 继承 BasePageService，提供 Gemini 平台特定的 URL 匹配规则
 * 
 * Gemini URL 模式:
 * - Home页: https://gemini.google.com/app
 * - Chat页: https://gemini.google.com/app/{chatId}
 */
export class GeminiPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Gemini';
    }

    /**
     * 获取 Gemini 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://gemini.google.com/app
            home: /^https:\/\/gemini\.google\.com\/app\/?$/i,
            // Chat页模式: https://gemini.google.com/app/{chat_id}
            chat: /^https:\/\/gemini\.google\.com\/app\/([a-zA-Z0-9]+)$/i
        };
    }

    /**
     * 检测欢迎页面（Gemini 特定实现）
     * Gemini 欢迎页面的特征:
     * - URL 是 /app 且没有 chatId
     * - 或者页面中不存在对话历史
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（Gemini 特有的欢迎页面特征）
        // 欢迎页面通常不会有 model-response 元素
        const hasConversation = document.querySelector('model-response') !== null;
        return !hasConversation;
    }
    
    /**
     * 检测聊天页面（Gemini 特定实现）
     * Gemini 聊天页面的特征:
     * - URL 包含 chatId
     * - 或者页面中存在对话历史 (model-response 或 user-query)
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测（存在对话历史记录）
        return document.querySelector('model-response') !== null ||
               document.querySelector('user-query') !== null;
    }
}
