# 需求-chatgpt平台实现

## 一、需求概述

参考适配器中 Kimi 平台的实现，实现 ChatGPT 平台的所有功能。

### 需要生成的文件

#### 1. 适配器文件（`src/content/adapters/chatgpt/`）
- `chatgpt.ts` - 主适配器类
- `ChatGPTAnswerService.ts` - 答案捕获服务
- `ChatGPTPageService.ts` - 页面状态管理服务
- `ChatGPTClipboardService.ts` - 剪贴板操作服务
- `ChatGPTAnswerController.ts` - 答案控制器

#### 2. 配置文件（`src/content/configs/`）
- `chatgptConfig.ts` - 选择器配置

#### 3. 注册修改
- 在 `SelectorManager.ts` 中注册 `chatgptConfig.ts`

---

## 二、ChatGPT 页面分析（基于实际页面）

### 2.1 页面URL模式
- **Home页**: `https://chatgpt.com/` 或 `https://chatgpt.com/?model=xxx`
- **Chat页**: `https://chatgpt.com/c/{chat_id}`

### 2.2 页面核心选择器分析

通过 chrome-mcp 分析页面 `https://chatgpt.com/c/68e3d520-b8d4-8333-aed9-497044744535`，获得以下关键选择器：

#### 输入框相关
```typescript
inputField: [
  '#prompt-textarea',  // 主输入框
  'textarea[data-virtualkeyboard="true"]',
  '.ProseMirror[contenteditable="true"]',
  '[data-testid="composer-input"]'
]
```

#### 发送按钮
```typescript
sendButton: [
  '[data-testid="send-button"]',
  'button[aria-label*="Send"]',
  'button[aria-label*="发送"]',
  'form button[type="submit"]'
]
```

#### 对话内容区域
```typescript
chatContentList: [
  'main[class*="flex"]',  // 主对话区域
  '[data-testid="conversation"]'
]
```

#### 用户提示词项
```typescript
promptItem: [
  'article[data-turn="user"]',
  '[data-message-author-role="user"]'
]

promptContent: [
  'article[data-turn="user"] .whitespace-pre-wrap',
  '[data-message-author-role="user"] .whitespace-pre-wrap'
]
```

#### AI回答项
```typescript
answerItem: [
  'article[data-turn="assistant"]',
  '[data-message-author-role="assistant"]'
]
```

#### 回答完成标识
```typescript
answerCompletion: [
  'article[data-turn="assistant"] [data-testid="copy-turn-action-button"]',  // 复制按钮出现表示完成
  '.text-message+.flex button',  // 答案底部操作按钮组
]
```

#### 复制按钮
```typescript
copyButton: [
  '[data-testid="copy-turn-action-button"]',
  'button[aria-label*="复制"]',
  'button[aria-label*="Copy"]'
]
```

#### Markdown内容区域
```typescript
markdown: [
  '.markdown.prose',  // ChatGPT使用这个类名包裹markdown内容
  '[data-message-author-role="assistant"] .markdown',
  'article[data-turn="assistant"] .markdown'
]
```

---

## 三、技术设计方案

### 3.1 架构设计

遵循现有的 Kimi 适配器架构，采用**继承+组合**的设计模式：

```
ChatGPTAdapter (extends BaseAIAdapter)
  └── ChatGPTAnswerController (extends BaseAnswerController)
        ├── ChatGPTAnswerService (extends BaseAnswerService)
        ├── ChatGPTPageService (extends BasePageService)
        └── ChatGPTClipboardService (extends BaseClipboardService)
```

### 3.2 各文件功能说明

#### 1. `chatgpt.ts` - 主适配器
```typescript
export class ChatGPTAdapter extends BaseAIAdapter {
  private chatgptAnswerController: ChatGPTAnswerController | null = null;
  
  constructor(platform: PlatformEntity) {
    super(platform);
    this.chatgptAnswerController = new ChatGPTAnswerController();
    this.chatgptAnswerController.init(this);
  }
  
  destroy(): void {
    // 清理资源
  }
}
```

**核心职责**：
- 作为 ChatGPT 平台的入口
- 初始化并管理 AnswerController
- 处理适配器生命周期

#### 2. `ChatGPTAnswerController.ts` - 控制器
```typescript
export class ChatGPTAnswerController extends BaseAnswerController {
  protected createServices(): {
    answerService: BaseAnswerService;
    pageService: BasePageService;
  } {
    return {
      answerService: new ChatGPTAnswerService(),
      pageService: new ChatGPTPageService()
    };
  }
}
```

**核心职责**：
- 创建并返回 ChatGPT 特定的服务实例
- 流程编排由基类完成

#### 3. `ChatGPTPageService.ts` - 页面状态管理
```typescript
export class ChatGPTPageService extends BasePageService {
  protected getPlatformName(): string {
    return 'ChatGPT';
  }

  protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
    return {
      home: /^https:\/\/chatgpt\.com\/?(\?.*)?$/i,
      chat: /^https:\/\/chatgpt\.com\/c\/([a-zA-Z0-9-]+)$/i
    };
  }
}
```

**核心职责**：
- URL 模式匹配
- 页面类型判断（Home/Chat）

#### 4. `ChatGPTAnswerService.ts` - 答案捕获服务

**核心方法**：
```typescript
export class ChatGPTAnswerService extends BaseAnswerService {
  private clipboardService: ChatGPTClipboardService;

  constructor() {
    super();
    this.clipboardService = new ChatGPTClipboardService();
  }

  protected getClipboardService(): BaseClipboardService {
    return this.clipboardService;
  }

  // ChatGPT特定：监听生成中的答案
  protected async processGeneratingAnswer(answerElement: Element): Promise<void> {
    // 使用 MutationObserver 监听答案完成
    // 完成标志：出现复制按钮
  }

  // ChatGPT特定：判断答案是否完成
  protected isAnswerComplete(answerElement: Element): boolean {
    // 检查是否存在 copy-turn-action-button
  }

  // ChatGPT特定：提取已完成的答案
  protected async extractCompletedAnswer(answerElement: Element): Promise<void> {
    // 调用剪贴板服务提取内容
  }
}
```

**核心职责**：
- 监听 DOM 变化，捕获新的答案
- 判断答案是否生成完成
- 提取答案内容

#### 5. `ChatGPTClipboardService.ts` - 剪贴板服务

```typescript
export class ChatGPTClipboardService extends BaseClipboardService {
  // ChatGPT特定：降级方法提取markdown
  protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    // 1. 找到 .markdown.prose 节点
    // 2. 提取 HTML
    // 3. 使用 Turndown 转换为 Markdown
    // 4. 可能需要自定义规则处理 ChatGPT 特殊格式
  }
}
```

**核心职责**：
- 通过复制按钮获取剪贴板内容
- 降级时从 DOM 提取 markdown 内容
- 处理 ChatGPT 特定的内容格式

#### 6. `chatgptConfig.ts` - 选择器配置

```typescript
export const chatgptSelector: SelectorConfig = {
  inputField: [
    '#prompt-textarea',
    'textarea[data-virtualkeyboard="true"]',
    // ...
  ],
  sendButton: [
    '[data-testid="send-button"]',
    // ...
  ],
  chatContentList: [
    'main[class*="flex"]'
  ],
  promptItem: [
    'article[data-turn="user"]'
  ],
  promptContent: [
    'article[data-turn="user"] .whitespace-pre-wrap'
  ],
  answerItem: [
    'article[data-turn="assistant"]'
  ],
  answerCompletion: [
    'article[data-turn="assistant"] [data-testid="copy-turn-action-button"]'
  ],
  copyButton: [
    '[data-testid="copy-turn-action-button"]'
  ],
  markdown: [
    '.markdown.prose'
  ]
};
```

---

## 四、关键差异点与注意事项

### 4.1 与 Kimi 的主要差异

| 维度 | Kimi | ChatGPT | 注意事项 |
|------|------|---------|----------|
| **URL模式** | `kimi.com/chat/{id}` | `chatgpt.com/c/{id}` | URL正则需调整 |
| **答案容器** | `.chat-content-item-assistant` | `article[data-turn="assistant"]` | 选择器完全不同 |
| **完成标识** | `.segment-assistant-actions-content` | `[data-testid="copy-turn-action-button"]` | ChatGPT 通过按钮出现判断 |
| **Markdown类名** | `.markdown` | `.markdown.prose` | ChatGPT 有额外的 prose 类 |
| **复制按钮** | `.simple-button.size-small` | `[data-testid="copy-turn-action-button"]` | data-testid 更可靠 |

### 4.2 需要特别注意的点

1. **答案完成判断**
   - ChatGPT 的答案完成标志是复制按钮出现
   - 需要监听 `[data-testid="copy-turn-action-button"]` 的出现

2. **代码块处理**
   - ChatGPT 可能有特殊的代码块样式
   - Turndown 转换时可能需要自定义规则

3. **暗色主题**
   - 需要测试暗色主题下的选择器是否稳定

4. **流式输出**
   - ChatGPT 使用流式输出，需要确保 MutationObserver 能正确捕获

---

## 五、不明确的地方（需要讨论）

### 🤔 问题 1：ChatGPT 的 Home 页识别
- **问题描述**：ChatGPT 的首页可能带参数 `?model=xxx`
- **建议方案**：
  ```typescript
  home: /^https:\/\/chatgpt\.com\/?(\?.*)?$/i
  ```
- **需要确认**：这个正则是否能覆盖所有 Home 页情况？

### 🤔 问题 2：代码块暗色主题处理
- **问题描述**：Kimi 有专门的暗色主题代码块处理，ChatGPT 是否也需要？
- **建议方案**：先实现基本功能，如遇问题再添加自定义规则
- **需要确认**：是否在第一版就实现暗色主题支持？

### 🤔 问题 3：答案完成的二次确认
- **问题描述**：ChatGPT 可能存在"思考中"状态，按钮可能多次出现/消失
- **建议方案**：添加防抖逻辑，确保答案真正完成
- **需要确认**：是否需要额外的完成确认机制？

### 🤔 问题 4：错误答案处理
- **问题描述**：ChatGPT 可能返回错误提示（如网络错误、限流等）
- **建议方案**：在 `extractCompletedAnswer` 中添加错误检测
- **需要确认**：错误答案是否应该被捕获和存储？

### 🤔 问题 5：Markdown 提取策略
- **问题描述**：是否优先使用复制按钮，还是直接从 DOM 提取？
- **当前方案**：参考 Kimi，优先点击复制按钮
- **需要确认**：这个策略是否适用于 ChatGPT？

---

## 六、实现步骤建议

1. **第一步**：实现 `chatgptConfig.ts` 并在 `SelectorManager` 中注册
2. **第二步**：实现 `ChatGPTPageService.ts`（最简单，可先验证URL匹配）
3. **第三步**：实现 `ChatGPTClipboardService.ts`（核心功能）
4. **第四步**：实现 `ChatGPTAnswerService.ts`（核心功能）
5. **第五步**：实现 `ChatGPTAnswerController.ts`（编排层）
6. **第六步**：实现 `chatgpt.ts`（入口层）
7. **第七步**：集成测试与调试

---

## 七、最终确认结果 ✅

1. ✅ **Home页URL**：`https://chatgpt.com/?model=xxx` 为 Home 页
2. ✅ **Chat页URL**：`https://chatgpt.com/c/{id}` 为 Chat 页
3. ✅ **暗色主题支持**：需要实现，当前就是暗色模式
4. ✅ **答案完成判断**：仅通过复制按钮判断，不需要额外确认机制
5. ✅ **错误答案处理**：错误答案不捕捉和存储
6. ✅ **提取策略**：优先使用复制按钮

**已确认，开始实现！**

