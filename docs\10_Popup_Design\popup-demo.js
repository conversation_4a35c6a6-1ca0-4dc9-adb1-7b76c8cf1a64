// Toggle fullscreen mode
function toggleFullscreen() {
    const container = document.getElementById('popupContainer');
    const btn = document.querySelector('.fullscreen-btn');
    
    container.classList.toggle('fullscreen');
    btn.textContent = container.classList.contains('fullscreen') ? '⛉' : '⛶';
}

// Show specific page
function showPage(pageId) {
    // Hide all pages
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    
    // Remove active class from all nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Show selected page
    document.getElementById(pageId).classList.add('active');
    
    // Add active class to clicked nav item
    event.target.classList.add('active');
}

// Switch platform tabs
function switchPlatform(platformId) {
    // Remove active class from all tabs and contents
    document.querySelectorAll('.platform-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelectorAll('.platform-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Add active class to selected tab and content
    event.target.classList.add('active');
    document.getElementById(platformId + '-content').classList.add('active');
}

// Toggle switch functionality
function toggleSwitch(element) {
    element.classList.toggle('active');
}

// Initialize page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
        });
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Card hover effects
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = 'var(--shadow-lg)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'var(--shadow-md)';
        });
    });

    // Initialize progress animation
    const progressBar = document.querySelector('.progress-fill');
    if (progressBar) {
        // Simulate progress animation
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 10;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            progressBar.style.width = progress + '%';
        }, 200);
    }

    // Add floating animation to floating ball icon
    const floatingElements = document.querySelectorAll('.float');
    floatingElements.forEach(element => {
        element.style.animationDelay = Math.random() * 2 + 's';
    });
});
