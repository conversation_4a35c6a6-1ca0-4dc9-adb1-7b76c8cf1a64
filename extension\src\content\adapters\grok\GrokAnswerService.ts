import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import { GrokClipboardService } from "./GrokClipboardService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";
import { SelectorService } from "@/content/core/SelectorService";
import { AnswerModel } from "@/content/model/AnswerModel";

/**
 * Grok 答案捕获服务
 * 继承 GrokAnswerService，提供 Grok 平台特定的答案处理逻辑
 * 大部分逻辑已在基类实现，此处只需提供剪贴板服务实例
 * 如有特殊的答案处理逻辑，可以覆盖基类方法
 */
export class GrokAnswerService extends BaseAnswerService {
    // Grok 平台的剪贴板服务实例
    private clipboardService: GrokClipboardService;

    constructor() {
        super();
        this.clipboardService = new GrokClipboardService();
    }

    /**
     * 获取剪贴板服务实例
     */
    protected getClipboardService(): BaseClipboardService {
        return this.clipboardService;
    }

    protected getPromptDivHash(promptElement: Element): string|null {
        return promptElement.id;
    }
    protected getAnswerDivHash(answerElement: Element): string|null {
        return answerElement.id;
    }

    protected async processExistingPrompt(promptElement: Element): Promise<void> {
        const selectors = SelectorManager.getSelector();
        const contentNode = DOMUtils.findElementInContainer(promptElement, selectors.promptContent);

        if (contentNode?.textContent) {
            const content = contentNode.textContent.trim();
            await AnswerModel.getInstance().addPrompt(content, this.getPromptDivHash(promptElement));
            // console.info('【GrokAnswerService】 问题提取完成', content.slice(0, 50));
        }
    }


    protected async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            console.info('【GrokAnswerService】Existing模式: 开始提取答案', answerElement);
            const selectors = SelectorManager.getSelector();
            // 1. 查找答案完成组件
            let completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (completionNode == null) {
                completionNode = await DOMUtils.asyncSelectElementInContainer(answerElement, selectors.answerCompletion, this.ANSWER_TIMEOUT);
            }
            if (completionNode == null) {
                console.warn(`【GrokAnswerService】Existing模式: 未找到答案完成组件`);
                return;
            }

            // 2. 查找复制按钮
            const copyButton = SelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn(`【GrokAnswerService】Existing模式: 未找到复制按钮`);
                return;
            }

            // 3. 使用剪贴板服务获取内容
            const clipboardService = this.getClipboardService();
            const answerContent = await clipboardService.simulateClickAndGetContent(answerElement, copyButton);

            if (!answerContent?.trim()) {
                console.warn(`【GrokAnswerService】Existing模式: 提取的答案内容为空`);
                return;
            }

            // 4. 保存到数据模型
            const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
            console.info(`【GrokAnswerService】Existing模式: 已保存，下标: ${answerIndex}`);

            // 5. 触发事件通知
            this.dispatchAnswerExtractedEvent(completionNode, answerIndex);

        } catch (error) {
            console.error(`【GrokAnswerService】Existing模式: 提取失败`, error);
        }
    }

    /**
       * 提取答案内容（通用实现，子类可覆盖）
       * @param answerElement 答案元素
       * @param context 上下文描述（用于日志）
       * @returns 答案内容或 null
       */
    protected async extractAnswerContent(answerElement: Element, context: string): Promise<string | null> {
        try {
            const selectors = SelectorManager.getSelector();

            // 1. 查找答案完成组件
            const completionNode = DOMUtils.findElementInContainer(answerElement, selectors.answerCompletion);
            if (!completionNode) {
                console.warn(`【GrokAnswerService】 ${context}: 未找到答案完成组件`);
                return null;
            }

            // 2. 查找复制按钮
            const copyButton = SelectorService.findCompletionButton(completionNode);
            if (!copyButton) {
                console.warn(`【GrokAnswerService】 ${context}: 未找到复制按钮`);
                return null;
            }

            // 3. 使用剪贴板服务获取内容
            const clipboardService = this.getClipboardService();
            const answerContent = await clipboardService.simulateClickAndGetContent(answerElement, copyButton);

            if (!answerContent?.trim()) {
                console.warn(`【GrokAnswerService】 ${context}: 提取的答案内容为空`);
                return null;
            }

            // 4. 保存到数据模型
            const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
            console.info(`【GrokAnswerService】 ${context} 已保存，下标: ${answerIndex}`);

            // 5. 触发事件通知
            this.dispatchAnswerExtractedEvent(completionNode, answerIndex);

            return answerContent;
        } catch (error) {
            console.error(`【GrokAnswerService】 ${context}: 提取失败`, error);
            return null;
        }
    }

    protected async handleNewNode(node: Element): Promise<void> {
        const selectors = SelectorManager.getSelector();

        // 检测问题节点
        if (selectors.promptItem?.some(sel => node.matches(sel))) {
            console.info('【GrokAnswerService】 检测到新的问题元素');
            // 直接使用当前 node 处理问题
            await this.processExistingPrompt(node);
        } else {
            // 查找子节点中的问题元素
            const childPromptElements = DOMUtils.findElementAllInContainer(node, selectors.promptItem);
            if (childPromptElements?.length > 0) {
                console.info('【GrokAnswerService】 在子节点中检测到问题元素');
                const promptElement = childPromptElements[0]; // 取第一个
                // 立即处理问题节点（问题内容通常是完整的）
                await this.processExistingPrompt(promptElement);
            }
        }

        // 检测答案节点
        if (selectors.answerItem?.some(sel => node.matches(sel))) {
            console.info('【GrokAnswerService】 检测到新的答案元素', node);

            // 检查是否是完成状态的答案（包含 answerCompletion 组件）
            const hasCompletion = DOMUtils.findElementInContainer(node, selectors.answerCompletion);
            if (hasCompletion) {
                console.info('【GrokAnswerService】 检测到完成状态的答案元素（包含answerCompletion），跳过MutationObserver监听，直接处理');
                await this.processExistingAnswer(node);
                return;
            }

            if (this.generatingAnswerElement) {
                console.info('【GrokAnswerService】 已存在生成中的答案节点，跳过处理');
                console.info('【GrokAnswerService】 当前生成中的答案:', this.generatingAnswerElement);
                console.info('【GrokAnswerService】 新检测到的答案:', node);
                // 检查是否是同一个答案元素（可能是 class 变化导致的重复检测）
                if (this.generatingAnswerElement === node) {
                    console.info('【GrokAnswerService】 这是同一个答案元素，可能是属性变化，继续等待...');
                    return;
                }
                // 如果是不同的元素，说明旧的答案可能已经完成，清除旧的引用
                console.warn('【GrokAnswerService】 检测到新的答案元素，清除旧的生成中答案引用');
                this.clearGeneratingElements();
            }

            console.info('【GrokAnswerService】 开始监听新答案的生成过程');
            this.generatingAnswerElement = node;
            await this.processGeneratingAnswer(this.generatingAnswerElement);
        } else {
            // 查找子节点中的答案元素
            const childAnswerElements = DOMUtils.findElementAllInContainer(node, selectors.answerItem);
            if (childAnswerElements?.length > 0) {
                const answerElement = childAnswerElements[0];
                console.info('【GrokAnswerService】 在子节点中检测到答案元素', answerElement);
                if(this.generatingAnswerElement === answerElement){
                    console.info('【GrokAnswerService】 这是同一个答案元素，可能是属性变化，继续等待...');
                    return;
                }
                this.generatingAnswerElement = answerElement;
                await this.processGeneratingAnswer(this.generatingAnswerElement);
            }
        }
    }
}
