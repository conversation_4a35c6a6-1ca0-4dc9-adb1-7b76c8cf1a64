import './MarkdownExportButton.css';

/**
 * Markdown 导出按钮组件
 * 渲染 Markdown 图标，处理点击事件
 */
export class MarkdownExportButton {
    private element: HTMLButtonElement;
    private tooltip: HTMLElement | null = null;
    private answerIndex: number;
    private clickHandler: ((index: number) => void) | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
        this.element = this.createButton();
    }

    /**
     * 创建按钮元素
     */
    private createButton(): HTMLButtonElement {
        const button = document.createElement('button');
        button.className = 'markdown-export-btn';
        button.setAttribute('data-answer-index', String(this.answerIndex));
        
        // 添加 Markdown Logo SVG
        button.innerHTML = this.getMarkdownLogoSVG();
        
        // 创建并添加 Tooltip
        this.tooltip = this.createTooltip();
        button.appendChild(this.tooltip);
        
        // 绑定事件
        button.addEventListener('click', this.handleClick.bind(this));
        button.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        button.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        
        return button;
    }

    /**
     * 创建自定义 Tooltip 元素
     */
    private createTooltip(): HTMLElement {
        const tooltip = document.createElement('div');
        tooltip.className = 'markdown-tooltip';
        tooltip.textContent = '点击导出为标准markdown文件';
        return tooltip;
    }

    /**
     * 获取 Markdown Logo SVG
     */
    private getMarkdownLogoSVG(): string {
        return `
            <svg width="20" height="20" viewBox="0 0 208 128" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g fill="currentColor">
                    <path clip-rule="evenodd" d="m15 10c-2.7614 0-5 2.2386-5 5v98c0 2.761 2.2386 5 5 5h178c2.761 0 5-2.239 5-5v-98c0-2.7614-2.239-5-5-5zm-15 5c0-8.28427 6.71573-15 15-15h178c8.284 0 15 6.71573 15 15v98c0 8.284-6.716 15-15 15h-178c-8.28427 0-15-6.716-15-15z" fill-rule="evenodd"/>
                    <path d="m30 98v-68h20l20 25 20-25h20v68h-20v-39l-20 25-20-25v39zm125 0-30-33h20v-35h20v35h20z"/>
                </g>
            </svg>
        `;
    }

    /**
     * 处理点击事件
     */
    private handleClick(event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();
        
        console.log(`[MarkdownExportButton] 点击导出按钮，索引: ${this.answerIndex}`);
        
        if (this.clickHandler) {
            this.clickHandler(this.answerIndex);
        }
    }

    /**
     * 处理鼠标进入事件
     */
    private handleMouseEnter(): void {
        this.element.style.color = 'var(--markdown-primary, #000000)';
    }

    /**
     * 处理鼠标离开事件
     */
    private handleMouseLeave(): void {
        this.element.style.color = '';
    }

    /**
     * 设置点击回调
     */
    public onClick(handler: (index: number) => void): void {
        this.clickHandler = handler;
    }

    /**
     * 渲染组件，返回 DOM 元素
     */
    public render(): HTMLButtonElement {
        return this.element;
    }

    /**
     * 销毁组件
     */
    public destroy(): void {
        this.element.removeEventListener('click', this.handleClick);
        this.element.removeEventListener('mouseenter', this.handleMouseEnter);
        this.element.removeEventListener('mouseleave', this.handleMouseLeave);
        this.element.remove();
    }
}
