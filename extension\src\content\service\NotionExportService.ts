import { AnswerModel } from '../model/AnswerModel';
import { ExportError, QAPair } from '../types/ExportTypes';
import { localStorageService } from '@/common/service/LocalStorageService';
import { MessageType } from '@/common/types/enums';
import { NotionExportParams, CreatePageRequest } from '@/common/types/NotionTypes';
import { markdownToBlocks } from '@tryfabric/martian';

/**
 * Notion 导出错误类
 */
export class NotionExportError extends Error {
    constructor(
        public code: ExportError,
        message: string
    ) {
        super(message);
        this.name = 'NotionExportError';
    }
}

/**
 * Notion 导出服务
 * 负责生成 Notion Block、调用 API 创建页面
 */
export class NotionExportService {
    private static instance: NotionExportService;

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): NotionExportService {
        if (!NotionExportService.instance) {
            NotionExportService.instance = new NotionExportService();
        }
        return NotionExportService.instance;
    }

    /**
     * 导出到 Notion
     */
    public async exportToNotion(params: NotionExportParams): Promise<void> {
        try {
            console.log('[NotionExportService] 开始导出', params);

            // 1. 检查配置
            const config = await localStorageService.getNotionConfig();
            if (!config) {
                throw new NotionExportError(
                    ExportError.INVALID_INDEX,
                    '未配置 Notion，请先完成配置'
                );
            }

            // 2. 获取问答对
            const qaPair = this.getQAPair(params.answerIndex);
            if (!qaPair) {
                throw new NotionExportError(
                    ExportError.INVALID_INDEX,
                    '无法获取问答内容'
                );
            }

            // 3. 生成 Markdown 内容
            const markdown = this.generateMarkdown(qaPair, params.tags);

            // 4. 转换为 Notion Blocks
            const blocks = await this.convertToNotionBlocks(markdown);

            // 5. 构建创建页面请求
            const request: CreatePageRequest = {
                parent: {
                    type: 'page_id',
                    page_id: config.pageId
                },
                properties: {
                    title: {
                        title: [
                            {
                                text: {
                                    content: params.title
                                }
                            }
                        ]
                    }
                },
                children: blocks
            };

            // 添加图标（如果有）
            if (params.icon) {
                request.icon = {
                    type: 'emoji',
                    emoji: params.icon
                };
            }

            // 6. 调用 Background API 创建页面
            await this.createNotionPage(config, request);

            console.log('[NotionExportService] 导出成功');

            // 7. 显示成功提示
            this.showToast('✓ 已导出到 Notion', 'success');

        } catch (error) {
            if (error instanceof NotionExportError) {
                this.showToast(error.message, 'error');
                throw error;
            }
            
            console.error('[NotionExportService] 导出失败', error);
            this.showToast('导出到 Notion 失败，请重试', 'error');
            throw new NotionExportError(
                ExportError.FILE_SAVE_FAILED,
                error instanceof Error ? error.message : '未知错误'
            );
        }
    }

    /**
     * 获取问答对
     */
    private getQAPair(answerIndex: number): QAPair | null {
        return AnswerModel.getInstance().getQAPair(answerIndex);
    }

    /**
     * 生成 Markdown 内容
     */
    private generateMarkdown(qaPair: QAPair, tags: string[]): string {
        let markdown = '';

        // 添加标签（如果有）
        if (tags.length > 0) {
            markdown += `**标签**: ${tags.join(', ')}\n\n`;
        }

        // 添加问题
        markdown += `## 问题\n\n${qaPair.prompt}\n\n`;

        // 添加答案
        markdown += `## 答案\n\n${qaPair.answer}\n`;

        return markdown;
    }

    /**
     * 转换 Markdown 为 Notion Blocks
     */
    private async convertToNotionBlocks(markdown: string): Promise<any[]> {
        try {
            // 使用 Martian 库转换
            const blocks = markdownToBlocks(markdown);
            
            console.log('[NotionExportService] Markdown 转换成功', {
                blockCount: blocks.length
            });

            return blocks;
        } catch (error) {
            console.error('[NotionExportService] Markdown 转换失败', error);
            
            // 降级方案：创建纯文本块
            return [
                {
                    object: 'block',
                    type: 'paragraph',
                    paragraph: {
                        rich_text: [
                            {
                                type: 'text',
                                text: {
                                    content: markdown
                                }
                            }
                        ]
                    }
                }
            ];
        }
    }

    /**
     * 创建 Notion 页面
     */
    private async createNotionPage(config: any, request: CreatePageRequest): Promise<void> {
        return new Promise((resolve, reject) => {
            chrome.runtime.sendMessage(
                {
                    type: MessageType.NOTION_CREATE_PAGE,
                    payload: { config, request }
                },
                (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (response?.success) {
                        resolve();
                    } else {
                        reject(new Error(response?.error || '创建页面失败'));
                    }
                }
            );
        });
    }

    /**
     * 显示 Toast 提示
     */
    private showToast(message: string, type: 'success' | 'warning' | 'error'): void {
        const toast = document.createElement('div');
        toast.className = `notion-toast notion-toast-${type}`;
        toast.textContent = message;

        // 简单的样式（内联）
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            backgroundColor: type === 'success' ? '#0F7B6C' : type === 'error' ? '#E03E3E' : '#F59E0B',
            color: '#FFFFFF',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        });

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);

        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }
}

/**
 * 导出单例实例
 */
export const notionExportService = NotionExportService.getInstance();
