import './NotionExportButton.css';

/**
 * Notion 导出按钮组件
 * 渲染 Notion 图标，处理点击事件
 */
export class NotionExportButton {
    private element: HTMLButtonElement;
    private tooltip: HTMLElement | null = null;
    private answerIndex: number;
    private clickHandler: ((index: number) => void) | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
        this.element = this.createButton();
    }

    /**
     * 创建按钮元素
     */
    private createButton(): HTMLButtonElement {
        const button = document.createElement('button');
        button.className = 'notion-export-btn';
        button.setAttribute('data-answer-index', String(this.answerIndex));
        
        // 添加 Notion Logo SVG
        button.innerHTML = this.getNotionLogoSVG();
        
        // 创建并添加 Tooltip
        this.tooltip = this.createTooltip();
        button.appendChild(this.tooltip);
        
        // 绑定事件
        button.addEventListener('click', this.handleClick.bind(this));
        button.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        button.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
        
        return button;
    }

    /**
     * 创建自定义 Tooltip 元素
     */
    private createTooltip(): HTMLElement {
        const tooltip = document.createElement('div');
        tooltip.className = 'notion-tooltip';
        tooltip.textContent = '导出到 Notion';
        return tooltip;
    }

    /**
     * 获取 Notion Logo SVG
     */
    private getNotionLogoSVG(): string {
        return `
            <svg width="20" height="20" viewBox="0 0 59.9 62.6" xmlns="http://www.w3.org/2000/svg">
                <path fill="currentColor" d="M3.8,2.7l34.6-2.6c4.2-0.4,5.3-0.1,8,1.8l11.1,7.8c1.8,1.3,2.4,1.7,2.4,3.2v42.7c0,2.7-1,4.3-4.4,4.5
                    l-40.2,2.4c-2.6,0.1-3.8-0.2-5.1-1.9L2.1,50.1c-1.5-2-2.1-3.4-2.1-5.1V7C0,4.8,1,2.9,3.8,2.7L3.8,2.7z M3.8,2.7"/>
                <path fill="#FFF" d="M38.4,0.1L3.8,2.7C1,2.9,0,4.8,0,7v38c0,1.7,0.6,3.2,2.1,5.1l8.1,10.6c1.3,1.7,2.6,2.1,5.1,1.9l40.2-2.4
                    c3.4-0.2,4.4-1.8,4.4-4.5V12.9c0-1.4-0.5-1.8-2.2-3c-0.1-0.1-0.2-0.1-0.3-0.2L46.4,2C43.8,0,42.7-0.2,38.4,0.1L38.4,0.1z
                    M16.2,12.2c-3.3,0.2-4,0.3-5.9-1.3L5.6,7.2C5.1,6.7,5.3,6.1,6.6,6l33.3-2.4c2.8-0.2,4.2,0.7,5.3,1.6l5.7,4.1
                    c0.3,0.1,0.9,0.8,0.1,0.8l-34.4,2.1L16.2,12.2z M12.4,55.3V19c0-1.6,0.5-2.3,1.9-2.4l39.5-2.3c1.3-0.1,1.9,0.7,1.9,2.3v36
                    c0,1.6-0.3,2.9-2.4,3l-37.8,2.2C13.4,58,12.4,57.2,12.4,55.3L12.4,55.3z M49.7,21c0.2,1.1,0,2.2-1.1,2.3l-1.8,0.4v26.8
                    c-1.6,0.9-3,1.3-4.2,1.3c-1.9,0-2.4-0.6-3.9-2.4L26.7,30.6v18.1l3.8,0.9c0,0,0,2.2-3,2.2l-8.4,0.5c-0.2-0.5,0-1.7,0.8-1.9l2.2-0.6
                    v-24l-3-0.3c-0.2-1.1,0.4-2.7,2.1-2.8l9-0.6l12.4,19V24.3l-3.2-0.4c-0.2-1.3,0.7-2.3,1.9-2.4L49.7,21z M49.7,21"/>
            </svg>
        `;
    }

    /**
     * 处理点击事件
     */
    private handleClick(event: MouseEvent): void {
        event.preventDefault();
        event.stopPropagation();
        
        console.log(`[NotionExportButton] 点击导出按钮，索引: ${this.answerIndex}`);
        
        if (this.clickHandler) {
            this.clickHandler(this.answerIndex);
        }
    }

    /**
     * 处理鼠标进入事件
     */
    private handleMouseEnter(): void {
        this.element.style.color = 'var(--notion-primary, #000000)';
    }

    /**
     * 处理鼠标离开事件
     */
    private handleMouseLeave(): void {
        this.element.style.color = '';
    }

    /**
     * 设置点击回调
     */
    public onClick(handler: (index: number) => void): void {
        this.clickHandler = handler;
    }

    /**
     * 渲染组件，返回 DOM 元素
     */
    public render(): HTMLButtonElement {
        return this.element;
    }

    /**
     * 销毁组件
     */
    public destroy(): void {
        this.element.removeEventListener('click', this.handleClick);
        this.element.removeEventListener('mouseenter', this.handleMouseEnter);
        this.element.removeEventListener('mouseleave', this.handleMouseLeave);
        this.element.remove();
    }
}
