import React from 'react'
import { cn } from '@/common/utils'

interface NavItem {
  id: string
  icon: string
  label: string
  path: string
}

interface SidebarProps {
  activeItem: string
  onItemClick: (itemId: string) => void
  isFullscreen?: boolean
  className?: string
}

const navItems: NavItem[] = [
  { id: 'home', icon: '🏠', label: '首页', path: '/' },
  { id: 'membership', icon: '👑', label: '会员', path: '/membership' },
  { id: 'settings', icon: '⚙️', label: '设置', path: '/settings' },
  { id: 'history', icon: '📋', label: '历史', path: '/history' },
  { id: 'billing', icon: '💳', label: '账单', path: '/billing' },
  { id: 'support', icon: '🆘', label: '支持', path: '/support' },
  { id: 'manual', icon: '📖', label: '手册', path: '/manual' }
]

export function Sidebar({ 
  activeItem, 
  onItemClick, 
  isFullscreen = false,
  className 
}: SidebarProps) {
  return (
    <div className={cn('sidebar', className)}>
      {navItems.map((item) => (
        <div
          key={item.id}
          className={cn(
            'nav-item',
            activeItem === item.id && 'active'
          )}
          onClick={() => onItemClick(item.id)}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault()
              onItemClick(item.id)
            }
          }}
        >
          {item.icon} {item.label}
        </div>
      ))}
    </div>
  )
}
