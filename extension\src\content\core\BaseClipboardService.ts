import TurndownService from 'turndown';
import SelectorManager from "../configs/SelectorManager";
import { DOMUtils } from "../utils/DOMUtils";

/**
 * 剪贴板服务基类
 * 提供剪贴板操作的通用实现
 */
export abstract class BaseClipboardService {
  /**
   * 延迟函数（通用工具）
   * @param ms 延迟毫秒数
   */
  protected static delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 模拟点击并获取内容（通用流程，子类可覆盖）
   * @param answerElement 答案元素
   * @param copyButton 复制按钮
   * @returns 内容字符串
   */
  public async simulateClickAndGetContent(
    answerElement: Element, 
    copyButton: Element
  ): Promise<string> {
    try {
      console.info('[ClipboardService] 开始内容提取流程（模拟点击复制按钮）');
      
      // 1. 模拟点击复制按钮
      if (copyButton && copyButton instanceof HTMLElement) {
        console.info('[ClipboardService] 点击复制按钮');
        
        // 确保页面获得焦点 - 多重保险策略
        await this.ensureDocumentFocus();
        
        // 点击复制按钮
        copyButton.click();
        
        // 等待剪贴板更新（确保复制完成）
        await BaseClipboardService.delay(300);
        
        // 2. 尝试读取剪贴板（现代 API）
        let clipboardContent = await this.getClipboardContent(answerElement);
        
        // 如果成功读取到内容，直接返回
        if (clipboardContent && clipboardContent.trim()) {
          console.info('[ClipboardService] 通过剪贴板成功获取内容');
          return clipboardContent;
        }
      } else {
        console.warn('[ClipboardService] 复制按钮无效');
      }
      
      // 3. 剪贴板读取失败，使用降级方案（从 DOM 提取）
      console.info('[ClipboardService] 📋 剪贴板方式未成功，使用 DOM 提取降级方案');
      const fallbackContent = await this.getFallbackClipboardContent(answerElement);
      
      if (fallbackContent && fallbackContent.trim()) {
        console.info('[ClipboardService] ✅ DOM 提取成功，内容长度:', fallbackContent.length);
      } else {
        console.warn('[ClipboardService] ⚠️ DOM 提取未获取到内容');
      }
      
      return fallbackContent;
      
    } catch (error) {
      console.error('[ClipboardService] 内容提取失败', error);
      // 最后尝试降级方案
      try {
        console.warn('[ClipboardService] 异常后尝试降级方案');
        return await this.getFallbackClipboardContent(answerElement);
      } catch (fallbackError) {
        console.error('[ClipboardService] 降级方案也失败', fallbackError);
        return '';
      }
    }
  }

  /**
   * 确保文档获得焦点 - 多重策略
   * 解决自动初始化时文档未获得焦点导致剪贴板读取失败的问题
   */
  private async ensureDocumentFocus(): Promise<void> {
    try {
      // 策略 1: 先尝试聚焦窗口
      window.focus();
      
      // 策略 2: 如果文档已经有焦点，直接返回
      if (document.hasFocus()) {
        console.info('[ClipboardService] 文档已有焦点');
        return;
      }
      
      // 策略 3: 尝试聚焦 document.body
      if (document.body) {
        document.body.focus();
      }
      
      // 策略 4: 创建并触发用户交互事件（模拟鼠标移动）
      // 这可以帮助触发浏览器的焦点获取机制
      const mouseEvent = new MouseEvent('mousemove', {
        view: window,
        bubbles: true,
        cancelable: true
      });
      document.dispatchEvent(mouseEvent);
      
      // 等待一小段时间让焦点生效
      await BaseClipboardService.delay(100);
      
      // 最终检查
      if (document.hasFocus()) {
        console.info('[ClipboardService] 文档焦点已激活');
      } else {
        console.warn('[ClipboardService] 文档仍未获得焦点，将依赖降级方案');
      }
    } catch (error) {
      console.warn('[ClipboardService] 设置焦点时出错', error);
    }
  }

  /**
   * 降级方法：直接从 DOM 提取内容（通用实现，子类可覆盖）
   * @param answerElement 答案元素
   * @returns Markdown 格式的内容
   */
  protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    const selectors = SelectorManager.getSelector();
    const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
    
    if (!markdownNode) {
      throw new Error('未找到 markdown 内容节点');
    }

    // 使用 Turndown 转换 HTML 到 Markdown
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      emDelimiter: '*'
    });
    
    return turndownService.turndown(markdownNode.innerHTML);
  }

  /**
   * 使用现代 API 获取剪贴板内容（子类可选实现）
   * @param answerElement 答案元素
   * @returns 剪贴板内容
   */
  protected async getClipboardContent(answerElement: Element): Promise<string> {
    try {
      // 检查浏览器支持
      if (!navigator.clipboard || !navigator.clipboard.readText) {
        console.warn('[ClipboardService] 浏览器不支持 Clipboard API');
        return '';
      }
      
      // 检查文档焦点状态（静默失败，不抛出错误）
      if (!document.hasFocus()) {
        console.warn('[ClipboardService] 文档未获得焦点，无法读取剪贴板（将使用降级方案）');
        return '';
      }
      
      // 尝试读取剪贴板
      const content = await navigator.clipboard.readText();
      
      if (!content || !content.trim()) {
        console.warn('[ClipboardService] 剪贴板内容为空');
        return '';
      }
      
      console.info('[ClipboardService] ✅ 成功读取剪贴板内容', content.slice(0, 50));
      return content;
      
    } catch (error) {
      // 常见错误：NotAllowedError (文档未聚焦/权限被拒), NotFoundError (剪贴板为空)
      const errorName = error instanceof Error ? error.name : 'Unknown';
      const errorMsg = error instanceof Error ? error.message : String(error);
      
      // 权限错误和焦点错误是预期的，使用 warn 级别
      if (errorName === 'NotAllowedError') {
        console.warn(`[ClipboardService] 剪贴板访问被拒绝 (${errorName}): ${errorMsg}，将使用降级方案`);
      } else {
        console.warn(`[ClipboardService] 读取剪贴板失败 (${errorName}): ${errorMsg}`);
      }
      
      return '';
    }
  }
}
