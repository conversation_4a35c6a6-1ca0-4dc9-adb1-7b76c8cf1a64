import { BasePageService } from "@/content/core/BasePageService";

/**
 * ChatGPT 页面状态管理服务
 * 继承 BasePageService，提供 ChatGPT 平台特定的 URL 匹配规则
 */
export class ChatGPTPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'ChatGPT';
    }

    /**
     * 获取 ChatGPT 的 URL 匹配模式
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: https://chatgpt.com 或 https://chatgpt.com/?model=xxx
            home: /^https:\/\/chatgpt\.com\/?(\?.*)?$/i,
            // Chat页模式: https://chatgpt.com/c/{chat_id}
            chat: /^https:\/\/chatgpt\.com\/c\/([a-zA-Z0-9-]+)$/i
        };
    }

    /**
     * 检测欢迎页面（ChatGPT 特定实现）
     */
    public isWelcomePage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isHome) {
            return true;
        }
        
        // 降级使用 DOM 检测（ChatGPT 特有的欢迎页面元素）
        return document.querySelector('[data-testid="composer-plus-btn"]') !== null;
    }
    
    /**
     * 检测聊天页面（ChatGPT 特定实现）
     */
    public isChatPage(): boolean {
        const urlResult = this.getPageTypeFromUrl();
        if (urlResult.isChat) {
            return true;
        }
        
        // 降级使用 DOM 检测（存在对话历史记录）
        return document.querySelector('article[data-turn="assistant"]') !== null ||
               document.querySelector('article[data-turn="user"]') !== null;
    }
}
