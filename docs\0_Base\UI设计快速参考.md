# UI 设计快速参考卡

> 开发 UI 组件时的速查表 - 基于 Linear 现代极简风

---

## 🎨 CSS 变量速查

### 颜色
```css
/* 主色 */
var(--primary)          /* #3B82F6 蓝色 */
var(--purple)           /* #8B5CF6 紫色 */
var(--pink)             /* #EC4899 粉色 */

/* 文字 */
var(--text-primary)     /* #111827 */
var(--text-secondary)   /* #374151 */
var(--text-tertiary)    /* #6B7280 */

/* 背景 */
var(--bg-elevated)      /* #FFFFFF 白色 */
var(--bg-subtle)        /* #F9FAFB 浅灰 */

/* 边框 */
var(--border-light)     /* #E5E7EB */
```

### 间距（8pt Grid）
```css
var(--space-1)   /* 4px */
var(--space-2)   /* 8px */
var(--space-3)   /* 12px */
var(--space-4)   /* 16px */
var(--space-8)   /* 32px */
```

### 字体
```css
var(--font-xs)    /* 12px - Badge */
var(--font-base)  /* 14px - 正文 */
var(--font-lg)    /* 20px - 标题 */
var(--font-3xl)   /* 48px - 价格 */
```

### 圆角
```css
var(--radius-sm)  /* 6px - Badge */
var(--radius-md)  /* 8px - 按钮 */
var(--radius-lg)  /* 12px - 卡片 */
```

### 阴影
```css
var(--shadow-hover-blue)    /* 蓝色悬停阴影 */
var(--shadow-hover-purple)  /* 紫色悬停阴影 */
var(--shadow-hover-pink)    /* 粉色悬停阴影 */
```

### 动效
```css
var(--duration-fast)    /* 150ms - 按钮 */
var(--duration-normal)  /* 200ms - 卡片 */
var(--easing)           /* cubic-bezier(0.4, 0, 0.2, 1) */
```

---

## 🧩 组件模板

### 卡片
```css
.card {
  background: var(--bg-elevated);
  border: 1.5px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-8);
  transition: all var(--duration-normal) var(--easing);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-hover-blue);
}
```

### 按钮
```css
.btn {
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: var(--font-base);
  font-weight: 600;
  transition: all var(--duration-fast) var(--easing);
}

.btn:hover {
  transform: translateY(-1px);
}
```

### Badge
```css
.badge {
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  border: 1px solid var(--primary);
}
```

---

## ✅ 快速检查清单

- [ ] 使用 CSS 变量（不硬编码）
- [ ] 间距是 4 的倍数
- [ ] 卡片边框 1.5px
- [ ] 卡片圆角 12px
- [ ] 按钮圆角 8px
- [ ] 动画 ≤ 300ms
- [ ] 悬停位移：卡片 -4px，按钮 -1px
- [ ] 无玻璃拟态（backdrop-filter）
- [ ] 无缩放动画（scale）

---

## 🚫 禁止规则

```css
/* ❌ 绝对禁止 */
backdrop-filter: blur(20px);
transform: scale(1.1);
border-radius: 24px;
color: #000000;
padding: 15px;  /* 奇数值 */
transition: all 500ms;  /* 超过 300ms */
```

---

## 📏 标准尺寸

| 元素 | 尺寸 |
|------|------|
| 卡片内边距 | 32px |
| 按钮内边距 | 12px 20px |
| Badge 内边距 | 6px 12px |
| 功能图标 | 18x18px |
| 卡片间距 | 16px |
| 顶部边框高度 | 3px |

---

## 🎯 等级颜色

| 等级 | 颜色 | 用途 |
|------|------|------|
| Free | 灰色 | 基础版 |
| Pro | 蓝色 | 推荐版 |
| Plus | 紫色 | 进阶版 |
| Max | 粉色 | 旗舰版 |

---

**完整规范**：`.github/UIAgentRules.md`
