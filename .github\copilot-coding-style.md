# GitHub Copilot 项目指令 · 代码风格与平台特性

## 代码风格核心规则

| 规则类型 | 要求 |
|---------|------|
| 注释语言 | 中文注释 + JSDoc |
| 日志格式 | `console.log('【类名】 操作描述', data)` |
| 错误处理 | 统一 try-catch + CustomError |
| 单例导出 | Manager 类使用 `getInstance()`，Service 类由 Controller 实例化 |
| 事件监听器 | 所有 `document.addEventListener` 必须使用箭头函数，确保正确解绑 |
| DOM 选择器 | 选择器必须包含上级和下级信息，提高定位准确性（如 `div.parent > span.child`、`:has()` 伪类） |

## Chrome 扩展核心特性

| 特性 | 实现方式 |
|-----|---------|
| 消息传递 | 使用 `MessagingService`，禁止直接 `chrome.runtime.sendMessage` |
| 存储策略 | sync(配置) / local(设置) / IndexedDB(大数据) |
| Manifest V3 | Service Worker + Declarative Net Request |
