import { BaseAIAdapter } from "./BaseAIAdapter";
import { BaseAnswerService } from "./BaseAnswerService";
import { BasePageService } from "./BasePageService";
import { AnswerModel } from "../model/AnswerModel";
import { DOMUtils } from "../utils/DOMUtils";
import SelectorManager from "../configs/SelectorManager";
import { ObsidianExportInject } from "../inject/ObsidianExportInject";
import { MarkdownExportInject } from "../inject/MarkdownExportInject";
import { NotionExportInject } from "../inject/NotionExportInject";

/**
 * 答案控制器基类
 * 提供流程编排的通用实现
 */
export abstract class BaseAnswerController {
  protected adapter: BaseAIAdapter | null = null;
  protected answerService: BaseAnswerService | null = null;
  protected pageService: BasePageService | null = null;
  protected currentPageType: 'home' | 'chat' | 'unknown' = 'unknown';
  
  // 导出注入器（通用）
  protected obsidianExportInject: ObsidianExportInject | null = null;
  protected markdownExportInject: MarkdownExportInject | null = null;
  protected notionExportInject: NotionExportInject | null = null;

  /**
   * 创建服务实例（子类需要提供）
   */
  protected abstract createServices(): {
    answerService: BaseAnswerService;
    pageService: BasePageService;
  };

  constructor() {
    // 子类创建服务实例
    const services = this.createServices();
    this.answerService = services.answerService;
    this.pageService = services.pageService;
    
    // 创建导出注入器
    this.obsidianExportInject = new ObsidianExportInject();
    this.markdownExportInject = new MarkdownExportInject();
    this.notionExportInject = new NotionExportInject();
  }

  /**
   * 初始化（通用实现）
   * @param adapter BaseAIAdapter 实例
   */
  public init(adapter: BaseAIAdapter): void {
    this.adapter = adapter;
    console.info('【BaseAnswerController】 初始化开始');
    
    // 初始化答案模型
    AnswerModel.getInstance().initPlatform(adapter.getCurrentPlatform());
    
    // 检查页面类型并启动服务
    this.checkPageTypeAndInitServices();
    
    console.info('【BaseAnswerController】 初始化完成');
  }

  /**
   * 检查页面类型并初始化服务（通用实现，子类可覆盖）
   */
  protected checkPageTypeAndInitServices(): void {
    try {
      const urlResult = this.pageService!.getPageTypeFromUrl();
      
      let newPageType: 'home' | 'chat' | 'unknown';
      if (urlResult.isHome) {
        newPageType = 'home';
      } else if (urlResult.isChat) {
        newPageType = 'chat';
      } else {
        newPageType = 'unknown';
      }

      console.info('【BaseAnswerController】 页面类型检测', {
        url: window.location.href,
        pageType: newPageType,
        chatId: urlResult.chatId
      });

      this.currentPageType = newPageType;
      
      switch (newPageType) {
        case 'home':
          console.info('【BaseAnswerController】 进入Home页，停止所有监听器');
          this.stopAllListeners();
          break;

        case 'chat':
          AnswerModel.getInstance().setChatId(urlResult.chatId);
          console.info(`【BaseAnswerController】 进入Chat页，启动聊天列表监听器 (chatId: ${urlResult.chatId})`);
          
          // ✅ 先启动导出注入器，再启动监听器
          // 这样确保在捕获现有答案时，注入器已经处于 active 状态
          // this.startExportInjects();
          
          // 使用 Promise 处理异步启动
          this.startChatListeners().catch(error => {
            console.error('【BaseAnswerController】 启动监听器失败', error);
          });
          break;

        default:
          console.info('【BaseAnswerController】 未知页面类型');
      }
    } catch (error) {
      console.error('【BaseAnswerController】 页面类型检测失败', error);
    }
  }

  /**
   * 启动聊天监听器（通用实现，子类可覆盖）
   */
  protected async startChatListeners(): Promise<void> {
    try {
      const selectors = SelectorManager.getSelector();

      //sleep 5s 
      await new Promise(resolve => setTimeout(resolve, 2000));
      // 使用异步方法等待聊天列表元素加载
      console.info('【BaseAnswerController】 等待聊天列表元素加载...');
      const chatListElement = await DOMUtils.asyncSelectElement(selectors.chatContentList, 20000);
      
      if (!chatListElement) {
        console.warn('【BaseAnswerController】 未找到聊天列表元素');
        return;
      }
      
      console.info('【BaseAnswerController】 聊天列表元素已找到',chatListElement);
      
      if (this.answerService) {
        await this.answerService.startListeningOnChatList(chatListElement);
        console.info('【BaseAnswerController】 聊天列表监听器运行中...');
      }
    } catch (error) {
      console.error('【BaseAnswerController】 启动聊天监听器失败', error);
    }
  }

  /**
   * 启动导出注入器（通用实现）
   */
  // protected startExportInjects(): void {
  //   try {
  //     this.obsidianExportInject?.start();
  //     this.markdownExportInject?.start();
  //     this.notionExportInject?.start();
  //     console.info('【BaseAnswerController】 导出注入器已启动');
  //   } catch (error) {
  //     console.error('【BaseAnswerController】 启动导出注入器失败', error);
  //   }
  // }

  /**
   * 停止所有监听器（通用实现）
   */
  protected stopAllListeners(): void {
    try {
      this.answerService?.stopAnswerListener();
      this.obsidianExportInject?.destroy();
      this.markdownExportInject?.destroy();
      this.notionExportInject?.destroy();
      console.info('【BaseAnswerController】 所有监听器已停止');
    } catch (error) {
      console.error('【BaseAnswerController】 停止监听器失败', error);
    }
  }

  /**
   * 销毁控制器（通用实现）
   */
  public destroy(): void {
    console.info('【BaseAnswerController】 开始销毁');
    this.stopAllListeners();
    this.answerService?.destroy();
    this.adapter = null;
    AnswerModel.getInstance().clear();
    console.info('【BaseAnswerController】 销毁完成');
  }

  /**
   * 刷新页面状态（通用实现）
   */
  public refreshPageState(): void {
    console.info('【BaseAnswerController】 刷新页面状态');
    this.checkPageTypeAndInitServices();
  }
}
