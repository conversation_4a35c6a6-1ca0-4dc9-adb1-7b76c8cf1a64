// 设置存储服务 - 重构版本
// 分离同步存储和本地运行时信息，避免多设备冲突
// 同步存储：跨设备共享的配置信息
// 本地运行时：当前设备特定信息

// 设备配置（移除status字段，通过存在性判断）
export interface DeviceConfig {
  id: string        // 设备指纹ID
  name: string      // 设备显示名称 
  icon: string      // 设备图标
  path: string      // 导出路径（用于显示和验证 DirectoryHandle）
  lastActive: number // 最后活跃时间戳
}

// 笔记平台设置（改为Record结构）
export interface NotePlatformSettings {
  devices: Record<string, DeviceConfig> // key: 设备指纹ID, value: 设备配置
}

// 同步存储设置（跨设备共享）
export interface SyncSettings {
  platforms: {
    obsidian: NotePlatformSettings
    notion: NotePlatformSettings  
    markdown: NotePlatformSettings
  } //笔记平台
  export_title_len?: number // 导出标题截断长度（默认40）
}

// 运行时设备信息（不存储，每次生成）
export interface RuntimeDeviceInfo {
  currentDeviceId: string  // 当前设备指纹ID
  os: string              // 操作系统
  deviceName: string      // 设备类型名称
  fullName: string        // 完整显示名称
}

class SettingsStorageService {
  private readonly SYNC_STORAGE_KEY = 'echosync_settings_v2'  // 新版本存储键
  
  // 生成设备指纹（基础方案）
  private generateDeviceFingerprint(): string {
    const components = [
      navigator.userAgent,
      screen.width.toString(),
      screen.height.toString(),
      navigator.language,
      navigator.hardwareConcurrency?.toString() || '0'
    ]
    
    const fingerprint = btoa(components.join('|')).replace(/[^a-zA-Z0-9]/g, '').slice(0, 12)
    return fingerprint.toUpperCase()
  }

  // 根据操作系统获取设备图标
  private getDeviceIcon(os: string): string {
    switch (os) {
      case 'Windows': return '💻'
      case 'macOS': return '💻'
      case 'Linux': return '🖥️'
      case 'iOS': return '📱'
      case 'Android': return '📱'
      default: return '💻'
    }
  }

  // 根据操作系统和应用获取默认路径
  private getDefaultPath(os: string, app: string): string {
    switch (os) {
      case 'Windows':
        return `C:\\Users\\<USER>\\Documents\\${app}`
      case 'macOS':
        return `/Users/<USER>/Documents/${app}`
      case 'Linux':
        return `/home/<USER>/Documents/${app}`
      default:
        return `~/Documents/${app}`
    }
  }

  // 生成运行时设备信息
  private generateRuntimeDeviceInfo(): RuntimeDeviceInfo {
    const userAgent = navigator.userAgent
    
    // 检测操作系统
    let os = 'Unknown'
    if (userAgent.includes('Windows')) os = 'Windows'
    else if (userAgent.includes('Mac')) os = 'macOS'
    else if (userAgent.includes('Linux')) os = 'Linux'
    else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) os = 'iOS'
    else if (userAgent.includes('Android')) os = 'Android'
    
    // 检测设备名称
    let deviceName = 'Device'
    if (os === 'Windows') deviceName = 'PC'
    else if (os === 'macOS') {
      if (userAgent.includes('MacBook')) deviceName = 'MacBook'
      else if (userAgent.includes('iMac')) deviceName = 'iMac'
      else deviceName = 'Mac'
    }
    else if (os === 'iOS') {
      if (userAgent.includes('iPhone')) deviceName = 'iPhone'
      else if (userAgent.includes('iPad')) deviceName = 'iPad'
    }
    else if (os === 'Android') deviceName = 'Android'
    else if (os === 'Linux') deviceName = 'Linux'
    
    // 生成稳定的设备指纹ID
    const currentDeviceId = this.generateDeviceFingerprint()
    const fullName = `${os}-${deviceName}-${currentDeviceId}`
    
    return {
      currentDeviceId,
      os,
      deviceName,
      fullName
    }
  }

  // 获取默认同步设置
  private getDefaultSyncSettings(): SyncSettings {
    return {
      platforms: {
        obsidian: { devices: {} },
        notion: { devices: {} },
        markdown: { devices: {} }
      },
      export_title_len: 40 // 默认导出标题截断长度
    }
  }

  // 确保当前设备已注册到同步存储
  private async ensureCurrentDeviceRegistered(): Promise<void> {
    const syncSettings = await this.getSyncSettings()
    const deviceInfo = this.generateRuntimeDeviceInfo()
    
    // 为每个平台注册当前设备
    const platforms: (keyof SyncSettings['platforms'])[] = ['obsidian', 'notion', 'markdown']
    let needsSave = false
    
    for (const platform of platforms) {
      if (!syncSettings.platforms[platform].devices[deviceInfo.currentDeviceId]) {
        syncSettings.platforms[platform].devices[deviceInfo.currentDeviceId] = {
          id: deviceInfo.currentDeviceId,
          name: deviceInfo.fullName,
          icon: this.getDeviceIcon(deviceInfo.os),
          path: this.getDefaultPath(deviceInfo.os, platform.charAt(0).toUpperCase() + platform.slice(1)),
          lastActive: Date.now()
        }
        needsSave = true
      } else {
        // 更新最后活跃时间
        syncSettings.platforms[platform].devices[deviceInfo.currentDeviceId].lastActive = Date.now()
        needsSave = true
      }
    }
    
    if (needsSave) {
      await this.saveSyncSettings(syncSettings)
    }
  }

  // 获取同步设置
  private async getSyncSettings(): Promise<SyncSettings> {
    try {
      const result = await chrome.storage.sync.get(this.SYNC_STORAGE_KEY)
      return result[this.SYNC_STORAGE_KEY] || this.getDefaultSyncSettings()
    } catch (error) {
      console.error('Failed to get sync settings:', error)
      return this.getDefaultSyncSettings()
    }
  }

  // 保存同步设置
  private async saveSyncSettings(settings: SyncSettings): Promise<void> {
    try {
      await chrome.storage.sync.set({
        [this.SYNC_STORAGE_KEY]: settings
      })
    } catch (error) {
      console.error('Failed to save sync settings:', error)
      throw error
    }
  }

  // 获取当前设备信息（公开方法）
  getCurrentDeviceInfo(): RuntimeDeviceInfo {
    return this.generateRuntimeDeviceInfo()
  }

  // 获取设置（仅返回同步设置）
  async getSettings(): Promise<SyncSettings> {
    await this.ensureCurrentDeviceRegistered()
    return await this.getSyncSettings()
  }

  // 保存所有设置（已废弃，直接操作同步设置）
  async saveSettings(settings: SyncSettings): Promise<void> {
    console.warn('saveSettings is deprecated, use specific update methods instead')
    await this.saveSyncSettings(settings)
  }

  // 更新活跃平台（已废弃，固定为obsidian顺序）
  async updateActivePlatform(_platform: string): Promise<void> {
    console.warn('updateActivePlatform is deprecated, platform order is now fixed')
    // 不再需要更新活跃平台，顺序固定为 obsidian -> notion -> markdown
  }

  // 更新设备路径
  async updateDevicePath(platform: keyof SyncSettings['platforms'], deviceId: string, path: string): Promise<void> {
    try {
      const syncSettings = await this.getSyncSettings()
      const device = syncSettings.platforms[platform].devices[deviceId]
      
      if (device) {
        device.path = path
        device.lastActive = Date.now()
        await this.saveSyncSettings(syncSettings)
      }
    } catch (error) {
      console.error('Failed to update device path:', error)
      throw error
    }
  }

  // 更新设备状态（已废弃，状态现在通过路径自动判断）
  async updateDeviceStatus(platform: keyof SyncSettings['platforms'], deviceId: string, _status: string): Promise<void> {
    console.warn('updateDeviceStatus is deprecated, status is now auto-determined by path')
    // 状态现在基于是否有路径自动判断，不需要手动设置
  }

  // 添加新设备
  async addDevice(platform: keyof SyncSettings['platforms'], device: DeviceConfig): Promise<void> {
    try {
      const syncSettings = await this.getSyncSettings()
      syncSettings.platforms[platform].devices[device.id] = device
      await this.saveSyncSettings(syncSettings)
    } catch (error) {
      console.error('Failed to add device:', error)
      throw error
    }
  }

  // 删除设备
  async removeDevice(platform: keyof SyncSettings['platforms'], deviceId: string): Promise<void> {
    try {
      const syncSettings = await this.getSyncSettings()
      delete syncSettings.platforms[platform].devices[deviceId]
      await this.saveSyncSettings(syncSettings)
    } catch (error) {
      console.error('Failed to remove device:', error)
      throw error
    }
  }

  // 监听设置变化
  onSettingsChanged(callback: (settings: SyncSettings) => void): void {
    chrome.storage.onChanged.addListener(async (changes, areaName) => {
      if (areaName === 'sync' && changes[this.SYNC_STORAGE_KEY]) {
        // 同步设置发生变化时，回调新设置
        const newSettings = await this.getSettings()
        callback(newSettings)
      }
    })
  }

  // 获取特定平台的当前设备配置（content模块常用）
  async getCurrentDeviceConfig(platform: keyof SyncSettings['platforms']): Promise<DeviceConfig | null> {
    try {
      const syncSettings = await this.getSyncSettings()
      const deviceInfo = this.generateRuntimeDeviceInfo()
      
      return syncSettings.platforms[platform].devices[deviceInfo.currentDeviceId] || null
    } catch (error) {
      console.error('Failed to get current device config:', error)
      return null
    }
  }

  // 获取活跃平台（content模块常用）
  async getActivePlatform(): Promise<string> {
    // 始终返回固定的第一个平台：obsidian
    return 'obsidian'
  }

  // 检查平台是否已配置（content模块常用）
  async isPlatformConfigured(platform: keyof SyncSettings['platforms']): Promise<boolean> {
    try {
      const currentDevice = await this.getCurrentDeviceConfig(platform)
      return currentDevice !== null && currentDevice.path !== ''
    } catch (error) {
      console.error('Failed to check platform configuration:', error)
      return false
    }
  }

  // 获取导出标题截断长度
  async getExportTitleLength(): Promise<number> {
    try {
      const syncSettings = await this.getSyncSettings()
      return syncSettings.export_title_len ?? 40 // 如果未设置，返回默认值40
    } catch (error) {
      console.error('Failed to get export title length:', error)
      return 40 // 出错时返回默认值
    }
  }

  // 设置导出标题截断长度
  async setExportTitleLength(length: number): Promise<void> {
    try {
      const syncSettings = await this.getSyncSettings()
      syncSettings.export_title_len = length
      await this.saveSyncSettings(syncSettings)
      console.log(`[SettingsStorageService] 导出标题截断长度已更新为: ${length}`)
    } catch (error) {
      console.error('Failed to set export title length:', error)
      throw error
    }
  }
}

export const settingsStorage = new SettingsStorageService()
