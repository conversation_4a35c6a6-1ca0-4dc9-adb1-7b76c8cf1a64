<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>会员解锁 - Linear 现代极简风</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* ========================================
       CSS Design Tokens - 遵守 UIAgentRules.md
       ======================================== */
    :root {
      /* 主色系 */
      --primary: #3B82F6;
      --primary-dark: #2563EB;
      --primary-light: #60A5FA;
      --primary-50: #EFF6FF;
      --primary-100: #DBEAFE;
      
      /* 辅助色 */
      --purple: #8B5CF6;
      --purple-dark: #7C3AED;
      --purple-50: #F5F3FF;
      --purple-100: #EDE9FE;
      
      /* 点缀色 */
      --pink: #EC4899;
      --pink-dark: #DB2777;
      --pink-50: #FDF4FF;
      --pink-100: #FCE7F3;
      
      /* 中性色 */
      --text-primary: #111827;
      --text-secondary: #374151;
      --text-tertiary: #6B7280;
      
      --bg-base: #FAFBFC;
      --bg-elevated: #FFFFFF;
      --bg-subtle: #F9FAFB;
      
      --border-light: #E5E7EB;
      --border-medium: #D1D5DB;
      
      /* 间距（8pt Grid） */
      --space-1: 4px;
      --space-2: 8px;
      --space-3: 12px;
      --space-4: 16px;
      --space-6: 24px;
      --space-8: 32px;
      --space-12: 48px;
      --space-16: 64px;
      
      /* 字体 */
      --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      --font-xs: 12px;
      --font-sm: 13px;
      --font-base: 14px;
      --font-md: 16px;
      --font-lg: 20px;
      --font-xl: 24px;
      --font-2xl: 32px;
      --font-3xl: 48px;
      
      --font-regular: 400;
      --font-medium: 500;
      --font-semibold: 600;
      --font-bold: 700;
      
      --tracking-tight: -0.03em;
      --tracking-normal: -0.02em;
      --tracking-wide: 0.5px;
      
      --leading-tight: 1.25;
      --leading-normal: 1.5;
      
      /* 圆角 */
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --radius-xl: 16px;
      
      /* 阴影 */
      --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
      
      --shadow-hover-blue: 
        0 0 0 1px var(--primary),
        0 12px 24px -8px rgba(59, 130, 246, 0.15),
        0 24px 48px -8px rgba(0, 0, 0, 0.08);
      
      --shadow-hover-purple:
        0 0 0 1px var(--purple),
        0 12px 24px -8px rgba(139, 92, 246, 0.15),
        0 24px 48px -8px rgba(0, 0, 0, 0.08);
      
      --shadow-hover-pink:
        0 0 0 1px var(--pink),
        0 12px 24px -8px rgba(236, 72, 153, 0.15),
        0 24px 48px -8px rgba(0, 0, 0, 0.08);
      
      --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
      
      /* 动效 */
      --duration-fast: 150ms;
      --duration-normal: 200ms;
      --duration-slow: 300ms;
      
      --easing: cubic-bezier(0.4, 0, 0.2, 1);
      --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
    }
    
    /* ========================================
       全局样式
       ======================================== */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: var(--font-family);
      font-size: var(--font-base);
      line-height: var(--leading-normal);
      color: var(--text-primary);
      background: var(--bg-base);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* 支持减少动画偏好 */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
    
    /* ========================================
       容器布局
       ======================================== */
    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: var(--space-16) var(--space-4);
    }
    
    .header {
      text-align: center;
      margin-bottom: var(--space-16);
    }
    
    .header-title {
      font-size: var(--font-2xl);
      font-weight: var(--font-bold);
      letter-spacing: var(--tracking-normal);
      color: var(--text-primary);
      margin-bottom: var(--space-3);
    }
    
    .header-subtitle {
      font-size: var(--font-md);
      color: var(--text-secondary);
      font-weight: var(--font-regular);
    }
    
    /* ========================================
       卡片网格
       ======================================== */
    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: var(--space-4);
    }
    
    @media (max-width: 1399px) {
      .pricing-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
      }
    }
    
    @media (max-width: 767px) {
      .pricing-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
      }
    }
    
    /* ========================================
       卡片组件 - Linear 现代极简风
       ======================================== */
    .pricing-card {
      background: var(--bg-elevated);
      border: 1.5px solid var(--border-light);
      border-radius: var(--radius-lg);
      padding: var(--space-8);
      position: relative;
      transition: all var(--duration-normal) var(--easing);
      cursor: pointer;
    }
    
    /* Linear 特色：3px 顶部彩色边框 */
    .pricing-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      border-radius: var(--radius-lg) var(--radius-lg) 0 0;
      opacity: 0;
      transition: opacity var(--duration-normal) var(--easing);
    }
    
    /* 等级颜色 */
    .pricing-card.free::before {
      background: linear-gradient(90deg, var(--primary-light), var(--primary));
    }
    
    .pricing-card.pro::before {
      background: linear-gradient(90deg, var(--primary), var(--primary-dark));
    }
    
    .pricing-card.plus::before {
      background: linear-gradient(90deg, var(--primary), var(--purple));
    }
    
    .pricing-card.max::before {
      background: linear-gradient(90deg, var(--purple), var(--pink));
    }
    
    /* Apple 风格微交互：悬停上浮 -4px */
    .pricing-card:hover::before {
      opacity: 1;
    }
    
    .pricing-card:hover {
      transform: translateY(-4px);
    }
    
    .pricing-card.free:hover {
      border-color: var(--primary-light);
      box-shadow: 0 0 0 1px var(--primary-light),
                  0 12px 24px -8px rgba(96, 165, 250, 0.15),
                  0 24px 48px -8px rgba(0, 0, 0, 0.08);
    }
    
    .pricing-card.pro:hover {
      border-color: var(--primary);
      box-shadow: var(--shadow-hover-blue);
    }
    
    .pricing-card.plus:hover {
      border-color: var(--purple);
      box-shadow: var(--shadow-hover-purple);
    }
    
    .pricing-card.max:hover {
      border-color: var(--pink);
      box-shadow: var(--shadow-hover-pink);
    }
    
    /* Apple 风格微交互：点击微缩放 0.98 */
    .pricing-card:active {
      transform: translateY(-2px) scale(0.99);
    }
    
    /* Popular 标签 */
    .pricing-card.popular::after {
      content: 'POPULAR';
      position: absolute;
      top: var(--space-3);
      right: var(--space-3);
      padding: 4px 10px;
      border-radius: var(--radius-sm);
      font-size: 10px;
      font-weight: var(--font-bold);
      background: linear-gradient(135deg, var(--primary), var(--purple));
      color: white;
      letter-spacing: var(--tracking-wide);
    }
    
    /* ========================================
       Badge 标签
       ======================================== */
    .card-badge {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: var(--radius-sm);
      font-size: var(--font-xs);
      font-weight: var(--font-semibold);
      text-transform: uppercase;
      letter-spacing: var(--tracking-wide);
      border: 1px solid;
      margin-bottom: var(--space-6);
    }
    
    .card-badge.free {
      background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
      color: var(--primary);
      border-color: var(--primary-light);
    }
    
    .card-badge.pro {
      background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
      color: var(--primary-dark);
      border-color: var(--primary);
    }
    
    .card-badge.plus {
      background: linear-gradient(135deg, var(--purple-50), var(--purple-100));
      color: var(--purple-dark);
      border-color: var(--purple);
    }
    
    .card-badge.max {
      background: linear-gradient(135deg, var(--pink-50), var(--pink-100));
      color: var(--pink-dark);
      border-color: var(--pink);
    }
    
    /* Badge 图标 */
    .badge-icon {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: white;
      flex-shrink: 0;
    }
    
    .badge-icon.free {
      background: linear-gradient(135deg, var(--primary-light), var(--primary));
    }
    
    .badge-icon.pro {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    }
    
    .badge-icon.plus {
      background: linear-gradient(135deg, var(--purple), var(--purple-dark));
    }
    
    .badge-icon.max {
      background: linear-gradient(135deg, var(--pink), var(--pink-dark));
    }
    
    /* ========================================
       卡片内容
       ======================================== */
    .card-header {
      margin-bottom: var(--space-8);
    }
    
    .card-title {
      font-size: var(--font-lg);
      font-weight: var(--font-semibold);
      letter-spacing: var(--tracking-normal);
      color: var(--text-primary);
      margin-bottom: var(--space-2);
    }
    
    .card-description {
      font-size: var(--font-sm);
      color: var(--text-secondary);
      line-height: var(--leading-normal);
    }
    
    /* 价格 */
    .card-price {
      margin-bottom: var(--space-8);
    }
    
    .price-amount {
      display: flex;
      align-items: baseline;
      gap: var(--space-2);
      margin-bottom: var(--space-1);
    }
    
    .price-currency {
      font-size: var(--font-lg);
      font-weight: var(--font-semibold);
      color: var(--text-secondary);
    }
    
    .price-value {
      font-size: var(--font-3xl);
      font-weight: var(--font-bold);
      letter-spacing: var(--tracking-tight);
      line-height: 1;
    }
    
    .pricing-card.free .price-value {
      color: var(--primary-light);
    }
    
    .pricing-card.pro .price-value {
      color: var(--primary);
    }
    
    .pricing-card.plus .price-value {
      color: var(--purple);
    }
    
    .pricing-card.max .price-value {
      color: var(--pink);
    }
    
    .price-period {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
    }
    
    /* 功能列表 */
    .card-features {
      list-style: none;
      margin-bottom: var(--space-8);
    }
    
    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: var(--space-3);
      padding: var(--space-3) 0;
      font-size: var(--font-sm);
      color: var(--text-secondary);
      border-bottom: 1px solid var(--border-light);
    }
    
    .feature-item:last-child {
      border-bottom: none;
    }
    
    .feature-icon {
      width: 18px;
      height: 18px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 11px;
      color: white;
      flex-shrink: 0;
      margin-top: 2px;
      transition: transform var(--duration-fast) var(--easing-spring);
    }
    
    /* 图标微交互：悬停放大到 1.1 */
    .pricing-card:hover .feature-icon {
      transform: scale(1.05);
    }
    
    .pricing-card.free .feature-icon {
      background: linear-gradient(135deg, var(--primary-light), var(--primary));
    }
    
    .pricing-card.pro .feature-icon {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    }
    
    .pricing-card.plus .feature-icon {
      background: linear-gradient(135deg, var(--purple), var(--purple-dark));
    }
    
    .pricing-card.max .feature-icon {
      background: linear-gradient(135deg, var(--pink), var(--pink-dark));
    }
    
    /* ========================================
       按钮组件
       ======================================== */
    .card-cta {
      width: 100%;
      padding: 12px 20px;
      border-radius: var(--radius-md);
      font-size: var(--font-base);
      font-weight: var(--font-semibold);
      border: none;
      cursor: pointer;
      transition: all var(--duration-fast) var(--easing-spring);
    }
    
    /* Primary 按钮 */
    .btn-primary {
      color: white;
    }
    
    .pricing-card.free .btn-primary {
      background: linear-gradient(135deg, var(--primary-light), var(--primary));
    }
    
    .pricing-card.pro .btn-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    }
    
    .pricing-card.plus .btn-primary {
      background: linear-gradient(135deg, var(--purple), var(--purple-dark));
    }
    
    .pricing-card.max .btn-primary {
      background: linear-gradient(135deg, var(--pink), var(--pink-dark));
    }
    
    /* 按钮微交互：悬停上浮 -1px */
    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-focus);
    }
    
    /* 按钮微交互：点击微缩放 0.98 */
    .btn-primary:active {
      transform: translateY(0) scale(0.98);
    }
    
    /* Secondary 按钮 */
    .btn-secondary {
      background: var(--bg-elevated);
      color: var(--text-secondary);
      border: 1.5px solid var(--border-light);
    }
    
    .btn-secondary:hover {
      background: var(--bg-subtle);
      border-color: var(--border-medium);
      transform: translateY(-1px);
    }
    
    .btn-secondary:active {
      transform: translateY(0) scale(0.98);
    }
    
    /* ========================================
       页脚
       ======================================== */
    .footer {
      text-align: center;
      margin-top: var(--space-16);
      padding-top: var(--space-12);
      border-top: 1px solid var(--border-light);
    }
    
    .footer-text {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
      margin-bottom: var(--space-3);
    }
    
    .footer-link {
      color: var(--primary);
      text-decoration: none;
      font-weight: var(--font-medium);
      transition: color var(--duration-fast);
    }
    
    .footer-link:hover {
      color: var(--primary-dark);
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 页头 -->
    <header class="header">
      <h1 class="header-title">选择适合您的计划</h1>
      <p class="header-subtitle">解锁全部功能，提升工作效率</p>
    </header>
    
    <!-- 定价卡片网格 -->
    <div class="pricing-grid">
      <!-- Free 版本 -->
      <div class="pricing-card free">
        <div class="card-badge free">
          <span class="badge-icon free">F</span>
          <span>Free</span>
        </div>
        
        <div class="card-header">
          <h2 class="card-title">免费版</h2>
          <p class="card-description">入门体验，感受 AI 助手的魅力</p>
        </div>
        
        <div class="card-price">
          <div class="price-amount">
            <span class="price-currency">¥</span>
            <span class="price-value">0</span>
          </div>
          <p class="price-period">永久免费</p>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>每日 10 次对话</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>基础导出功能</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>标准响应速度</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>社区支持</span>
          </li>
        </ul>
        
        <button class="card-cta btn-secondary">当前计划</button>
      </div>
      
      <!-- Pro 版本 -->
      <div class="pricing-card pro popular">
        <div class="card-badge pro">
          <span class="badge-icon pro">P</span>
          <span>Pro</span>
        </div>
        
        <div class="card-header">
          <h2 class="card-title">专业版</h2>
          <p class="card-description">适合高频使用的专业用户</p>
        </div>
        
        <div class="card-price">
          <div class="price-amount">
            <span class="price-currency">¥</span>
            <span class="price-value">29</span>
          </div>
          <p class="price-period">每月 / 按年付费享 8 折</p>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>无限对话次数</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>高级导出格式（Markdown）</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>优先响应速度</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>邮件技术支持</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>历史记录云同步</span>
          </li>
        </ul>
        
        <button class="card-cta btn-primary">立即升级</button>
      </div>
      
      <!-- Plus 版本 -->
      <div class="pricing-card plus">
        <div class="card-badge plus">
          <span class="badge-icon plus">+</span>
          <span>Plus</span>
        </div>
        
        <div class="card-header">
          <h2 class="card-title">增强版</h2>
          <p class="card-description">适合团队协作的进阶用户</p>
        </div>
        
        <div class="card-price">
          <div class="price-amount">
            <span class="price-currency">¥</span>
            <span class="price-value">59</span>
          </div>
          <p class="price-period">每月 / 按年付费享 8 折</p>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Pro 全部功能</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Notion / Obsidian 集成</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>自定义快捷键</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>API 访问权限</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>团队协作工具</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>7x24 优先支持</span>
          </li>
        </ul>
        
        <button class="card-cta btn-primary">立即升级</button>
      </div>
      
      <!-- Max 版本 -->
      <div class="pricing-card max">
        <div class="card-badge max">
          <span class="badge-icon max">M</span>
          <span>Max</span>
        </div>
        
        <div class="card-header">
          <h2 class="card-title">旗舰版</h2>
          <p class="card-description">适合企业级用户的顶级方案</p>
        </div>
        
        <div class="card-price">
          <div class="price-amount">
            <span class="price-currency">¥</span>
            <span class="price-value">99</span>
          </div>
          <p class="price-period">每月 / 按年付费享 8 折</p>
        </div>
        
        <ul class="card-features">
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>Plus 全部功能</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>无限 AI 模型切换</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>专属客户经理</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>私有化部署支持</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>定制化开发服务</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>SLA 服务保障</span>
          </li>
          <li class="feature-item">
            <span class="feature-icon">✓</span>
            <span>企业级安全审计</span>
          </li>
        </ul>
        
        <button class="card-cta btn-primary">联系销售</button>
      </div>
    </div>
    
    <!-- 页脚 -->
    <footer class="footer">
      <p class="footer-text">所有计划均支持 30 天无理由退款</p>
      <p class="footer-text">
        <a href="#" class="footer-link">查看完整功能对比</a> · 
        <a href="#" class="footer-link">常见问题</a> · 
        <a href="#" class="footer-link">联系客服</a>
      </p>
    </footer>
  </div>
  
  <script>
    // 为卡片添加点击事件（可选）
    document.querySelectorAll('.pricing-card').forEach(card => {
      card.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发卡片点击
        if (e.target.tagName === 'BUTTON') return;
        
        const button = this.querySelector('.card-cta');
        if (button) {
          button.click();
        }
      });
    });
    
    // 按钮点击事件
    document.querySelectorAll('.card-cta').forEach(button => {
      button.addEventListener('click', function(e) {
        e.stopPropagation();
        const tier = this.closest('.pricing-card').classList[1];
        console.log(`用户选择了 ${tier} 版本`);
        // 这里可以添加实际的跳转逻辑
      });
    });
  </script>
</body>
</html>
