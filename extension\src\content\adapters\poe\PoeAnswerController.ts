import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { PoeAnswerService } from "./PoeAnswerService";
import { PoePageService } from "./PoePageService";

/**
 * Poe 答案控制器
 * 继承 BaseAnswerController，提供 Poe 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 PoeAnswerService 和 PoePageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 * - 协调Poe特有的问答对结构处理
 */
export class PoeAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: PoeAnswerService;
        pageService: PoePageService;
    } {
        console.info('[PoeAnswerController] 创建 Poe 服务实例');
        
        return {
            answerService: new PoeAnswerService(),
            pageService: new PoePageService()
        };
    }
}
