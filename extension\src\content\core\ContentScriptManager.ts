import { detectCurrentPlatform } from "@/content/capture/PlatformDetector";
import { ChatGPTAdapter } from "@/content/adapters/chatgpt/chatgpt";
import { DeepSeekAdapter } from "@/content/adapters/deepseek/deepseek";
import { ClaudeAdapter } from "@/content/adapters/claude";
import { GeminiAdapter } from "@/content/adapters/gemini/gemini";
import { KimiAdapter } from "@/content/adapters/kimi/kimi";
import { GrokAdapter } from "@/content/adapters/grok/grok";
import { PoeAdapter } from "@/content/adapters/poe/poe";
import { DolaAdapter } from "@/content/adapters/dola/Dola";
import { DoubaoAdapter } from "@/content/adapters/doubao/Doubao";
import { MonicaAdapter } from "@/content/adapters/monica/monica";
import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { platformDatabaseProxy } from "@/common/service/PlatformDatabaseProxy";
import { checkAndUpdateFavicon } from "@/content/capture/FaviconCapture";
import type {
  PlatformEntity,
} from "@/common/types/database_entity";
import { PlatformConfig } from "../types/PlatformConfigType";

/**
 * Content Script 管理器
 * 重构后以 BaseAIAdapter 为核心应用上下文，统一管理组件间交互
 */
export class ContentScriptManager {
  private adapter: BaseAIAdapter | null = null;
  private isInitialized: boolean = false;

  constructor() {
    // 不在构造函数中立即初始化，而是等待外部调用
    console.log('【ContentScriptManager】ContentScriptManager created, waiting for initialization...');
  }

  /**
   * 公共初始化方法，确保页面元素加载完成后再初始化
   */
  async initialize(): Promise<void> {
    console.log('【ContentScriptManager】Starting ContentScriptManager initialization...');
    await this.init();
  }

  /**
   * 初始化管理器
   * 重构后以 BaseAIAdapter 为核心应用上下文
   */
  private async init() {
    if (this.isInitialized) return;

    try {
      // 1. 检测平台
      const platformConfig: PlatformConfig = detectCurrentPlatform();
      // 2. 加载平台信息
      const platformEntity: PlatformEntity = await this.loadPlatformEntity(platformConfig);

      if (platformConfig) {
        // 2. 创建适配器
        this.adapter = this.createAdapter(platformEntity);

        if (this.adapter) {
          // 3. 初始化适配器（等待DOM元素可用）
          await this.adapter.initialize();

          
          this.isInitialized = true;
          console.log("【ContentScriptManager】Initialized for:", platformConfig.name);
        }
      }
    } catch (error) {
      console.error("【ContentScriptManager】Init error:", error);
    }
  }

  /**
   * 创建适配器实例作为核心应用上下文
   */
  private createAdapter(platform: PlatformEntity): BaseAIAdapter | null {
    // 添加 null 检查
    if (!platform || !platform.name) {
      console.error('【ContentScriptManager】无法创建适配器：platform 或 platform.name 为空', platform);
      return null;
    }

    const adapters = {
      ChatGPT: () => new ChatGPTAdapter(platform),
      DeepSeek: () => new DeepSeekAdapter(platform),
      Claude: () => new ClaudeAdapter(platform),
      Gemini: () => new GeminiAdapter(platform),
      Kimi: () => new KimiAdapter(platform),
      Grok: () => new GrokAdapter(platform),
      Poe: () => new PoeAdapter(platform),
      Dola: () => new DolaAdapter(platform),
      Doubao: () => new DoubaoAdapter(platform),
      Monica: () => new MonicaAdapter(platform),
    };

    return adapters[platform.name]?.() || null;
  }

  /**
   * 加载平台信息 比如favicon
   */
  private async loadPlatformEntity(platformConfig: PlatformConfig): Promise<PlatformEntity | null> {
   
    try {
      const platformsResult = await platformDatabaseProxy.getAll();

      if (platformsResult.success) {
        
        // 找到当前平台
        const currentPlatform: PlatformEntity | null =
          platformsResult.data.find((p: any) => {
            const nameMatch = p.name === platformConfig.name;
            const urlMatch = window.location.href.includes(
              p.url.replace("https://", "").replace("http://", "")
            );
            return nameMatch || urlMatch;
          }) || null;

        if (currentPlatform?.id) {
          // 更新平台favicon
          checkAndUpdateFavicon(currentPlatform).catch(console.error);
        }

        
        console.log(
          "【ContentScriptManager】Platform info loaded via MessagingService:",
          currentPlatform
        );

        return currentPlatform;
      } else {
        console.warn(
          "【ContentScriptManager】Failed to load platform info:",
          platformsResult.error
        );
        return null;
      }
    } catch (error) {
      console.error("【ContentScriptManager】Error loading platform info:", error);
      return null;
    }
  }

  /**
   * 销毁管理器
   * 清理核心应用上下文和所有依赖组件
   */
  destroy(): void {
    if (this.adapter) {
      this.adapter.destroy();
      this.adapter = null;
      console.log(
        "【ContentScriptManager】Core application context (BaseAIAdapter) destroyed"
      );
    }

    // 清理其他组件
    this.isInitialized = false;
    console.log("【ContentScriptManager】ContentScriptManager destroyed completely");
  }
}
