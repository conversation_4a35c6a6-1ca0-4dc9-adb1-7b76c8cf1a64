import React from 'react'
import { StatusIndicator } from '../components/button'

export function BillingPage() {
  // Mock data
  const currentSubscription = {
    plan: 'Pro 套餐',
    price: '$9.9/月',
    nextBilling: '2024-02-15',
    paymentMethod: '**** **** **** 1234'
  }

  const paymentHistory = [
    {
      id: 1,
      description: 'Pro 套餐 - 1月',
      date: '2024-01-15',
      amount: '$9.9',
      status: 'paid'
    },
    {
      id: 2,
      description: 'Pro 套餐 - 首月优惠',
      date: '2023-12-15',
      amount: '$4.9',
      status: 'paid'
    }
  ]

  const paymentMethods = [
    {
      id: 1,
      type: 'Visa',
      number: '**** **** **** 1234',
      expiry: '12/26',
      isDefault: true
    }
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 当前订阅 */}
      <div className="card fade-in">
        <div className="card-title">💳 当前订阅</div>
        <div className="card-content">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
            <div>
              <div style={{ fontWeight: 600, fontSize: '16px' }}>{currentSubscription.plan}</div>
              <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{currentSubscription.price}</div>
            </div>
            <StatusIndicator type="success">活跃</StatusIndicator>
          </div>
          <div style={{ marginBottom: '12px' }}>
            <div style={{ fontSize: '14px', marginBottom: '4px' }}>下次扣费: {currentSubscription.nextBilling}</div>
            <div style={{ fontSize: '14px' }}>支付方式: {currentSubscription.paymentMethod}</div>
          </div>
        </div>
      </div>

      {/* 订阅管理 */}
      <div className="card fade-in">
        <div className="card-title">📋 订阅管理</div>
        <div className="card-content">
          <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
            <button 
              className="btn btn-secondary" 
              style={{ background: 'var(--purple-500)', color: 'white', border: 'none' }}
            >
              升级到Plus
            </button>
            <button className="btn btn-secondary">升级到Max</button>
          </div>
          <button 
            className="btn btn-secondary" 
            style={{ color: 'var(--error)', borderColor: 'var(--error)' }}
          >
            取消订阅
          </button>
        </div>
      </div>

      {/* 支付历史 */}
      <div className="card fade-in">
        <div className="card-title">🧾 支付历史</div>
        <div className="card-content">
          {paymentHistory.map((payment, index) => (
            <div
              key={payment.id}
              style={{
                borderBottom: index < paymentHistory.length - 1 ? '1px solid rgba(15, 23, 42, 0.06)' : 'none',
                padding: '12px 0',
                marginBottom: index < paymentHistory.length - 1 ? '12px' : '0'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <div style={{ fontWeight: 600 }}>{payment.description}</div>
                  <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{payment.date}</div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <span style={{ fontWeight: 600 }}>{payment.amount}</span>
                  <StatusIndicator type="success">已支付</StatusIndicator>
                  <button className="btn btn-secondary btn-small">下载发票</button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 支付方式管理 */}
      <div className="card fade-in">
        <div className="card-title">💳 支付方式管理</div>
        <div className="card-content">
          {paymentMethods.map((method) => (
            <div
              key={method.id}
              style={{
                border: '1px solid rgba(15, 23, 42, 0.12)',
                borderRadius: '8px',
                padding: '12px',
                marginBottom: '12px'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <div style={{ fontWeight: 600 }}>{method.number}</div>
                  <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
                    过期时间: {method.expiry}
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  {method.isDefault && <StatusIndicator type="success">默认</StatusIndicator>}
                  <button className="btn btn-secondary btn-small">编辑</button>
                  <button className="btn btn-secondary btn-small">删除</button>
                </div>
              </div>
            </div>
          ))}
          <button className="btn btn-secondary">+ 添加新支付方式</button>
        </div>
      </div>
    </div>
  )
}
