import { PlatformEntity } from "@/common/types/database_entity";
import { Singleton } from "../../common/base/Singleton";
import ArchiveService from "../service/ArchiveService";
import { CreateChatHistoryReq } from "@/common/types/content_vo";

interface PromptItem{
    prompt: string;
    promptUid: string;
}

/**
 * 答案数据模型
 * 继承Singleton，作为单例类存储聊天数据
 * 为KimiAnswerController和KimiAnswerService提供数据访问接口
 */
export class AnswerModel extends Singleton<AnswerModel> {

    private currentTitle: string = '';
    private currentChatId: string = ''; // 当前聊天组的id
    private readonly MAX_CONVERSATIONS = 10; // 最大对话数量

    private promptList: PromptItem[] = [];
    private answerList: string[] = [];
    private platform: PlatformEntity = null;
    
    // 去重机制：记录已处理的 hash（不持久化，页面刷新后清空）
    // key=页面的id value=数组下标
    private processedPromptHashes = new Map<string, number>();
    private processedAnswerHashes = new Map<string, number>();
    
    initPlatform(arg0: PlatformEntity) {
        this.platform = arg0;
    }
    /**
     * 更新对话标题
     */
    public setCurrentTitle(title: string): void {
        this.currentTitle = title;
    }
    
    /**
     * 获取当前标题
     */
    public getCurrentTitle(): string {
        return this.currentTitle;
    }

    public setChatId(chatId: string): void {
        this.currentChatId = chatId;
    }

    public getCurrentChatId(): string {
        return this.currentChatId;
    }

    public async addPrompt(prompt: string, hash?: string): Promise<void> {
        // 去重检查
        if (hash && this.processedPromptHashes.has(hash)) {
            console.info('【AnswerModel】 问题已存在，跳过添加 hash:', hash);
            return; // 返回-1表示未添加
        }
        
        const displayPrompt = prompt.length > 10 ? prompt.slice(0, 10) + '...' : prompt;
        console.log('【AnswerModel】 添加问题 index：%d, content: %s', this.promptList.length, displayPrompt);
        
        const promptUid = await ArchiveService.getInstance().archivePrompt(prompt, this.platform?.id);
        this.promptList.push({
            prompt,
            promptUid
        });
        
        // 记录已处理的 hash
        if (hash) {
            this.processedPromptHashes.set(hash, this.promptList.length - 1);
        }
    }

    /**
     * 
     * @param answer 答案markdown
     * @param hash 可选的去重标识
     * @returns id
     */
    public async addAnswer(answer: string, hash?: string): Promise<number> {
        // 去重检查
        if (hash && this.processedAnswerHashes.has(hash)) {
            console.info('【AnswerModel】 答案已存在，跳过添加 hash:', hash);
            return this.processedAnswerHashes.get(hash); // 返回-1表示未添加
        }
        
        const displayAnswer = answer.length > 10 ? answer.slice(0, 10) + '...' : answer;
        console.log('【AnswerModel】 Answer index：%d, %s', this.answerList.length, displayAnswer);

        const answerData: CreateChatHistoryReq = {
            prompt_uid: this.promptList[this.promptList.length - 1].promptUid,
            platform_id: this.platform?.id,
            chat_answer: answer,
            chat_group_name: this.currentTitle,
            chat_group_id: this.currentChatId,
            chat_sort: this.answerList.length,
            create_time: Date.now()
        }
        await ArchiveService.getInstance().archiveAnswer(answerData);
        this.answerList.push(answer);
        
        // 记录已处理的 hash
        if (hash) {
            this.processedAnswerHashes.set(hash, this.answerList.length - 1);
        }
        
        return this.answerList.length - 1;
    }
    
    /**
     * 获取问答对（用于导出功能）
     * @param index 答案索引
     * @returns 问答对或null
     */
    public getQAPair(index: number): { prompt: string; answer: string } | null {
        if (index < 0 || index >= this.answerList.length) {
            console.warn('【AnswerModel】 getQAPair: 索引越界', index);
            return null;
        }
        
        return {
            prompt: this.promptList[index]?.prompt || '',
            answer: this.answerList[index] || ''
        };
    }

    /**
     * 获取答案数量
     * @returns 答案总数
     */
    public getAnswerCount(): number {
        return this.answerList.length;
    }

    /**
     * 清理所有对话数据
     */
    public clearAllConversations(): void {

        this.currentTitle = '';
        this.currentChatId = '';
        
        // 清理数组数据，而不是方法引用
        this.promptList.length = 0;  // 清空数组
        this.answerList.length = 0;  // 清空数组
        this.platform = null;
        
        // 清理去重 hash 集合
        this.processedPromptHashes.clear();
        this.processedAnswerHashes.clear();
        
        console.info('【AnswerModel】 清理了所有对话数据和去重记录');
    }

    /**
     * 清理业务资源
     */
    public clear(): void {
        this.clearAllConversations();
        console.info('【AnswerModel】 cleared');
    }

    // /**
    //  * 完全重置单例实例（静态方法）
    //  * 注意：这会清理所有数据并重置单例实例
    //  */
    // public static resetSingleton(): void {
    //     AnswerModel.resetInstance();
    // }
}