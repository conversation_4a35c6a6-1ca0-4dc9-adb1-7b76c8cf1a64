# Notion 导出功能 - 阶段 1&2 完成报告

## 📋 项目概述

实现 Notion 导出功能，通过 Notion 官方 API 将 Kimi AI 回答导出到用户的 Notion 工作区。

## ✅ 已完成工作（阶段 1 & 2）

### 阶段 1: 基础设施 (100%)

#### 1. 安装依赖
```bash
npm install @tryfabric/martian
```
- Martian 库用于 Markdown 到 Notion Block 的转换
- 成功安装 78 个相关包

#### 2. LocalStorageService (src/common/service/)
**功能**:
- 管理 Notion 配置（Token、PageID、PageName）
- 存储在 chrome.storage.local（不同步，保证安全）
- 提供 CRUD 操作

**接口**:
```typescript
interface NotionConfig {
  token: string;          // Integration Token
  pageId: string;         // Parent Page ID
  pageName: string;       // Page 名称
  lastVerified?: number;  // 最后验证时间
}
```

**方法**:
- `saveNotionConfig(config)` - 保存配置
- `getNotionConfig()` - 获取配置
- `hasNotionConfig()` - 检查是否已配置
- `clearNotionConfig()` - 清除配置
- `updateLastVerified()` - 更新验证时间

#### 3. NotionTypes (src/common/types/)
**类型定义**:
- `NotionBlock` - Notion Block 基础类型
- `CreatePageRequest` - 创建页面请求
- `NotionApiResponse` - API 响应
- `NotionExportParams` - 导出参数
- `NotionConfigValidation` - 配置验证结果

#### 4. NotionApiService (src/background/)
**功能**: 在 Background Script 中代理 Notion API 调用（避免 CORS）

**方法**:
- `validateConfig(config)` - 验证 Token 和 Page ID
  - 调用 Notion API 获取 Page 信息
  - 返回验证结果和 Page 标题
  - 处理各种错误（401、404 等）

- `createPage(config, request)` - 创建 Notion 页面
  - POST 到 `/v1/pages`
  - 返回创建的页面数据

**API 配置**:
- Base URL: `https://api.notion.com/v1`
- API Version: `2022-06-28`
- Headers: Authorization、Notion-Version、Content-Type

#### 5. MessageType 扩展 (src/common/types/enums.ts)
```typescript
NOTION_VALIDATE_CONFIG = 'NOTION_VALIDATE_CONFIG'
NOTION_CREATE_PAGE = 'NOTION_CREATE_PAGE'
```

#### 6. messageHandler 更新 (src/background/messageHandler.ts)
添加两个消息处理器：
- `NOTION_VALIDATE_CONFIG` - 验证配置
- `NOTION_CREATE_PAGE` - 创建页面

#### 7. manifest.json 权限
添加 Notion API 权限：
```json
"host_permissions": [
  "https://api.notion.com/*"
]
```

---

### 阶段 2: 配置流程 (100%)

#### 1. NotionSetupModal (src/content/inject/components/)
**功能**: 首次使用引导用户配置 Notion

**UI 流程**:

**步骤 1: 引导说明**
- Logo + 标题
- 步骤指示器（1 → 2）
- 3 个配置步骤说明：
  1. 创建 Integration
  2. 配置权限
  3. 连接到页面
- 按钮：取消、下一步

**步骤 2: 输入配置**
- Token 输入框（password 类型）
- Page URL 输入框
- 验证状态显示
- 按钮：上一步、完成配置

**核心功能**:
- `parsePageUrl(url)` - 解析 Notion Page URL
  - 支持多种格式：
    - `https://www.notion.so/Page-Title-xxxxx`
    - `https://www.notion.so/username/Page-Title-xxxxx`
    - `https://www.notion.so/xxxxx`
  - 提取 32 位 pageId
  - 提取 pageName

- `validateConfig(config)` - 调用 Background 验证配置
  - 显示验证状态（loading、success、error）
  - 验证成功后保存配置
  - 触发 `NOTION_CONFIG_COMPLETED` 事件

**样式特点**:
- Notion 品牌色（黑色为主）
- 两步骤指示器
- 响应式设计
- 平滑过渡动画

#### 2. NotionSetupModal.css
**设计特点**:
- Notion 风格设计
- 模态框 + 遮罩层
- 步骤指示器动画
- 表单样式
- 验证状态样式（loading、success、error）
- 响应式适配

---

### 阶段 3: 导出按钮 (已完成部分)

#### 1. NotionExportButton (src/content/inject/components/)
**功能**: Notion 导出按钮组件

**特点**:
- Notion 官方 Logo SVG
- Tooltip: "导出到 Notion"
- 闪烁动画吸引注意
- 点击事件处理

#### 2. NotionExportButton.css
**样式特点**:
- 与 Obsidian/Markdown 按钮风格一致
- 32x32 像素尺寸
- 闪烁动画（悬浮时停止）
- 暗色模式适配
- Tooltip 样式

---

## 🎯 技术要点

### 1. CORS 解决方案
通过 Background Script 代理 API 调用，避免浏览器 CORS 限制：
```
Content Script → chrome.runtime.sendMessage
    ↓
Background Script → fetch(Notion API)
    ↓
Response → sendResponse
```

### 2. 安全性
- Token 存储在 `chrome.storage.local`（不同步）
- Password 输入框隐藏 Token
- 验证配置后再保存

### 3. URL 解析算法
```typescript
// 支持多种 Notion URL 格式
const idMatch = lastSegment.match(/([a-f0-9]{32}|[a-f0-9-]{36})$/i);
const pageId = idMatch[1].replace(/-/g, ''); // 移除横线统一格式
```

### 4. 错误处理
- 401: Token 无效或已过期
- 404: Page 不存在或无访问权限
- Network: 网络错误
- 详细错误信息提示用户

---

## 📂 文件结构

```
extension/
├── src/
│   ├── common/
│   │   ├── service/
│   │   │   └── LocalStorageService.ts ✅
│   │   └── types/
│   │       ├── NotionTypes.ts ✅
│   │       └── enums.ts (修改) ✅
│   ├── background/
│   │   ├── NotionApiService.ts ✅
│   │   └── messageHandler.ts (修改) ✅
│   └── content/
│       └── inject/
│           └── components/
│               ├── NotionSetupModal.ts ✅
│               ├── NotionSetupModal.css ✅
│               ├── NotionExportButton.ts ✅
│               └── NotionExportButton.css ✅
└── public/
    └── manifest.json (修改) ✅
```

---

## 🧪 测试要点

### 配置流程测试
1. **首次配置**
   - [ ] 打开 NotionSetupModal
   - [ ] 步骤 1 显示正确
   - [ ] 点击"下一步"进入步骤 2
   - [ ] 输入 Token 和 Page URL
   - [ ] 点击"完成配置"
   - [ ] 验证 loading 状态显示
   - [ ] 验证成功/失败提示

2. **URL 解析测试**
   - [ ] 测试标准格式: `https://www.notion.so/Page-xxxxx`
   - [ ] 测试带用户名: `https://www.notion.so/user/Page-xxxxx`
   - [ ] 测试纯 ID: `https://www.notion.so/xxxxx`
   - [ ] 测试带横线 ID: `https://www.notion.so/xxx-xxx-xxx`

3. **配置验证测试**
   - [ ] 无效 Token 提示 "Token 无效或已过期"
   - [ ] 无权限 Page 提示 "Page 不存在或无访问权限"
   - [ ] 成功验证显示 "✓ 配置成功！"

4. **配置存储测试**
   - [ ] 配置保存到 chrome.storage.local
   - [ ] 刷新页面后配置仍然存在
   - [ ] Token 不会同步到其他设备

### 按钮显示测试
1. **NotionExportButton**
   - [ ] 按钮正常显示（黑色 Notion logo）
   - [ ] 鼠标悬浮显示 Tooltip
   - [ ] 闪烁动画正常
   - [ ] 点击触发事件

---

## ⏳ 剩余工作

### 阶段 3: 导出功能（50% 待完成）
1. ⏳ NotionExportModal.ts - 导出设置 Modal
   - 标题输入
   - 标签选择（复用 TagSelector）
   - Emoji 图标选择（可选）
   - 预览功能

2. ⏳ NotionExportModal.css - Modal 样式

3. ⏳ NotionExportService.ts - 导出服务
   - 获取问答内容
   - 使用 Martian 转换 Markdown
   - 调用 Background API 创建页面
   - Toast 提示

4. ⏳ NotionExportInject.ts - 注入器
   - 监听答案提取事件
   - 注入 NotionExportButton
   - 检查配置状态
   - 显示 SetupModal 或 ExportModal

5. ⏳ KimiAnswerController.ts - 集成
   - 初始化 NotionExportInject
   - 启动/停止注入器

### 阶段 4: Popup 集成（待完成）
1. ⏳ 修改 Popup 设置页面
   - 移除 Notion 的 Sync 配置
   - 添加 Notion 配置区域
   - 显示当前配置状态
   - "重新配置"按钮
   - "测试连接"按钮

---

## 💡 下一步建议

### 方案 A: 继续完成导出功能（推荐）
继续实现剩余的 Modal、Service、Inject 组件，完成完整的导出流程。

### 方案 B: 先测试基础功能
1. 创建一个简单的测试页面
2. 测试 NotionSetupModal 显示和配置
3. 测试 API 调用是否成功
4. 验证配置存储

---

## 📅 完成日期

2025年10月7日 - 阶段 1&2 完成

## 👨‍💻 实施者

GitHub Copilot
