import { SelectorConfig } from "./SelectorManager";

/**
 * Grok 适配器的选择器配置
 * 基于 Grok 平台的页面结构定制
 */
export const grokSelector: SelectorConfig = {
  inputField: [
    'textarea[aria-label*="向 Grok 提任何问题"]',
    'textarea[aria-label*="How can Grok help"]',
    '.query-bar textarea',
    'form textarea',
  ],
  
  sendButton: [
    'button[aria-label*="提交"]',
    'button[aria-label*="Submit"]',
    'form button[type="submit"]',
    'button:has(svg[class*="arrow"])',
  ],
  
  headerContent: [],

  // Grok 特有的选择器
  chatContentList: [
    '.relative.flex.w-full.flex-col.items-center',
    'main .flex.flex-col',
    '[id="last-reply-container"]',
    'main > div',
  ],

  promptItem: [
    // 用户消息：气泡在右侧，带有特定的圆角样式
    // '.message-bubble.rounded-br-lg',
    'div[id^="response-"]:has(.message-bubble.rounded-br-lg)',
  ],

  promptContent: [
    '.message-bubble.rounded-br-lg .whitespace-pre-wrap',
    '.message-bubble .whitespace-pre-wrap',
  ],

  promptAction: [],

  answerItem: [
    // AI答案：包含 response-content-markdown 的消息
    'div[id^="response-"]:has(.response-content-markdown)',
    '.message-bubble:has(.response-content-markdown)',
  ],

  answerCompletion: [
    // 答案完成标志：action-buttons 容器出现
    // 移除 :has() 伪类以提高兼容性
    '.action-buttons',
    'div.action-buttons',
  ],

  copyButton: [
    '.action-buttons button[aria-label*="复制"]',
    '.action-buttons button[aria-label*="Copy"]',
    'button[aria-label*="复制"]',
    'button[aria-label*="Copy"]',
  ],

  markdown: [
    '.response-content-markdown',
    '.message-bubble .relative',
    '.prose',
  ]
};
