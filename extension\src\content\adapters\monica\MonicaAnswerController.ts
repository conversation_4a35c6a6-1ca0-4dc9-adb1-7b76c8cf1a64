import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { MonicaAnswerService } from "./MonicaAnswerService";
import { MonicaPageService } from "./MonicaPageService";

/**
 * Monica 答案控制器
 * 继承 BaseAnswerController，提供 Monica 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 MonicaAnswerService 和 MonicaPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 * - 协调答案监听、提取和存储的完整流程
 * 
 * 使用示例：
 * ```typescript
 * const controller = new MonicaAnswerController();
 * controller.init(adapter);
 * ```
 */
export class MonicaAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: MonicaAnswerService;
        pageService: MonicaPageService;
    } {
        console.info('[MonicaAnswerController] 创建 Monica 服务实例');
        
        return {
            answerService: new MonicaAnswerService(),
            pageService: new MonicaPageService()
        };
    }
}
