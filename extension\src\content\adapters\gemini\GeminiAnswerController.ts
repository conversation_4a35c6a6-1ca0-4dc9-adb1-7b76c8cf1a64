import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { GeminiAnswerService } from "./GeminiAnswerService";
import { GeminiPageService } from "./GeminiPageService";

/**
 * Gemini 答案控制器
 * 继承 BaseAnswerController，提供 Gemini 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 GeminiAnswerService 和 GeminiPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 * - 协调 Gemini 平台的所有答案相关操作
 */
export class GeminiAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: GeminiAnswerService;
        pageService: GeminiPageService;
    } {
        console.info('[GeminiAnswerController] 创建 Gemini 服务实例');
        
        return {
            answerService: new GeminiAnswerService(),
            pageService: new GeminiPageService()
        };
    }
}
