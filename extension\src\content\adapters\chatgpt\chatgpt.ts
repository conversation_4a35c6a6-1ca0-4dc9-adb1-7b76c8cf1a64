import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { ChatGPTAnswerController } from "./ChatGPTAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * ChatGPT 平台适配器
 * 继承 BaseAIAdapter，提供 ChatGPT 平台的完整集成
 * 
 * 主要特性：
 * - 自动检测 ChatGPT 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 支持暗色主题的代码块提取
 * - 优先使用复制按钮，自动降级到 DOM 提取
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new ChatGPTAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class ChatGPTAdapter extends BaseAIAdapter {
    private chatgptAnswerController: ChatGPTAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【ChatGPTAdapter】ChatGPTAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.chatgptAnswerController = new ChatGPTAnswerController();
        this.chatgptAnswerController.init(this);
        
        console.log('【ChatGPTAdapter】ChatGPTAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【ChatGPTAdapter】ChatGPTAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 ChatGPTAnswerController
            if (this.chatgptAnswerController) {
                this.chatgptAnswerController.destroy();
                this.chatgptAnswerController = null;
                console.log('【ChatGPTAdapter】ChatGPTAnswerController 清理完成');
            }
            
            console.log('【ChatGPTAdapter】ChatGPTAdapter destroy 完成');
            
        } catch (error) {
            console.error('【ChatGPTAdapter】ChatGPTAdapter destroy 失败:', error);
        }
    }
}
