/**
 * 文本处理工具类
 * 提供文本截断、格式化等通用功能
 */
export class TextUtils {
    /**
     * 智能截断标题，超过指定长度则添加省略号
     * 按词边界截断，避免截断单词或词组中间
     * 
     * @param text 原始文本
     * @param maxLength 最大长度（默认40个字符）
     * @returns 截断后的文本
     * 
     * @example
     * TextUtils.truncateTitle('这是一个很长的标题需要被截断', 10)
     * // 返回: '这是一个很长的标题...'
     * 
     * TextUtils.truncateTitle('How to use TypeScript with React', 20)
     * // 返回: 'How to use...'
     */
    static truncateTitle(text: string, maxLength: number = 60): string {
        if (!text) return '';
        
        // 移除首尾空白
        const trimmed = text.trim();
        
        // 如果长度不超过最大值，直接返回
        if (trimmed.length <= maxLength) {
            return trimmed;
        }
        
        // 截断到最大长度
        let truncated = trimmed.substring(0, maxLength);
        
        // 智能截断：按词边界
        truncated = this.truncateAtWordBoundary(truncated, trimmed);
        
        // 移除末尾的标点符号和空格
        truncated = this.removeTrailingPunctuation(truncated);
        
        // 添加省略号
        return truncated + '...';
    }

    /**
     * 按词边界截断文本
     * 避免在单词或词组中间截断
     * 
     * @param truncated 已截断的文本
     * @param original 原始完整文本
     * @returns 调整后的截断文本
     */
    private static truncateAtWordBoundary(truncated: string, original: string): string {
        // 如果截断位置正好是空格、标点或中文字符边界，直接返回
        const nextChar = original.charAt(truncated.length);
        if (!nextChar || this.isWordBoundary(nextChar) || this.isCJK(nextChar)) {
            return truncated;
        }
        
        // 如果截断位置在英文单词中间，回退到最近的词边界
        if (this.isAlphanumeric(nextChar)) {
            // 向前查找最近的空格或标点
            const lastSpaceIndex = truncated.search(/[\s\p{P}](?!.*[\s\p{P}])/u);
            
            if (lastSpaceIndex > 0) {
                // 找到词边界，截断到该位置
                truncated = truncated.substring(0, lastSpaceIndex);
            } else {
                // 没找到词边界，保持原截断（罕见情况）
                // 这种情况通常是整个文本都是一个长单词
            }
        }
        
        return truncated;
    }

    /**
     * 移除末尾的标点符号和空格
     * 
     * @param text 文本
     * @returns 清理后的文本
     */
    private static removeTrailingPunctuation(text: string): string {
        // 移除末尾的空格、逗号、句号、冒号、分号、问号、感叹号等
        return text.replace(/[\s,;:!?。，；：！？、]+$/, '');
    }

    /**
     * 判断字符是否为词边界（空格、标点符号等）
     * 
     * @param char 字符
     * @returns 是否为词边界
     */
    private static isWordBoundary(char: string): boolean {
        // 空格或标点符号
        return /[\s\p{P}]/u.test(char);
    }

    /**
     * 判断字符是否为中日韩（CJK）字符
     * 
     * @param char 字符
     * @returns 是否为CJK字符
     */
    private static isCJK(char: string): boolean {
        // 中文、日文、韩文字符范围
        const code = char.charCodeAt(0);
        return (
            (code >= 0x4E00 && code <= 0x9FFF) ||   // CJK统一表意文字
            (code >= 0x3400 && code <= 0x4DBF) ||   // CJK扩展A
            (code >= 0x20000 && code <= 0x2A6DF) || // CJK扩展B
            (code >= 0x2A700 && code <= 0x2B73F) || // CJK扩展C
            (code >= 0x2B740 && code <= 0x2B81F) || // CJK扩展D
            (code >= 0x2B820 && code <= 0x2CEAF) || // CJK扩展E
            (code >= 0xF900 && code <= 0xFAFF) ||   // CJK兼容表意文字
            (code >= 0x3040 && code <= 0x309F) ||   // 平假名
            (code >= 0x30A0 && code <= 0x30FF) ||   // 片假名
            (code >= 0xAC00 && code <= 0xD7AF)      // 韩文音节
        );
    }

    /**
     * 判断字符是否为字母或数字
     * 
     * @param char 字符
     * @returns 是否为字母或数字
     */
    private static isAlphanumeric(char: string): boolean {
        return /[a-zA-Z0-9]/.test(char);
    }

    /**
     * 清理文件名中的非法字符
     * 移除或替换文件系统不允许的字符
     * 
     * @param filename 文件名
     * @returns 清理后的文件名
     * 
     * @example
     * TextUtils.sanitizeFilename('文件名:包含/非法\\字符?.txt')
     * // 返回: '文件名-包含-非法-字符.txt'
     */
    static sanitizeFilename(filename: string): string {
        if (!filename) return '';
        
        // 替换文件系统非法字符为 '-'
        // Windows: < > : " / \ | ? *
        // Unix: /
        return filename
            .replace(/[<>:"/\\|?*]/g, '-')
            .replace(/\s+/g, ' ')  // 多个空格替换为单个空格
            .trim();
    }

    /**
     * 生成安全的文件名（结合截断和清理）
     * 
     * @param text 原始文本
     * @param maxLength 最大长度（默认40）
     * @returns 安全的文件名
     * 
     * @example
     * TextUtils.generateSafeFilename('如何使用 TypeScript/React 开发 Chrome 扩展？')
     * // 返回: '如何使用 TypeScript-React 开发 Chrome...'
     */
    static generateSafeFilename(text: string, maxLength: number = 60): string {
        // 先截断
        const truncated = this.truncateTitle(text, maxLength);
        // 再清理非法字符
        return this.sanitizeFilename(truncated);
    }
}
