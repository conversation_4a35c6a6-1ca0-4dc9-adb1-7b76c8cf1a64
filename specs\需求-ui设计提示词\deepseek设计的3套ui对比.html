<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI助手会员方案</title>
    <style>
        /* 基础重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        /* 配色方案选择器 */
        .theme-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .theme-btn {
            padding: 0.75rem 1.5rem;
            border: 2px solid rgba(255,255,255,0.3);
            background: rgba(255,255,255,0.1);
            color: white;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .theme-btn.active {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        /* 会员卡片容器 */
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        /* 会员卡片样式 */
        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0,0,0,0.15);
        }

        .pricing-card.popular::before {
            content: "最受欢迎";
            position: absolute;
            top: 1rem;
            right: -2rem;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 3rem;
            transform: rotate(45deg);
            font-size: 0.875rem;
            font-weight: 600;
        }

        .card-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }

        .plan-period {
            color: #666;
            font-size: 0.875rem;
        }

        .features {
            list-style: none;
            margin-bottom: 2rem;
        }

        .features li {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }

        .features li:last-child {
            border-bottom: none;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
        }

        .upgrade-btn {
            width: 100%;
            padding: 1rem 2rem;
            border: none;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--gradient);
            color: white;
        }

        .upgrade-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        /* 方案一：科技冷色系 */
        .theme-1 {
            --primary-color: #0ea5e9;
            --secondary-color: #8b5cf6;
            --accent-color: #a855f7;
            --gradient: linear-gradient(135deg, #0ea5e9 0%, #8b5cf6 100%);
        }

        .theme-1 .pricing-card {
            border-top: 4px solid #0ea5e9;
        }

        .theme-1 .feature-icon {
            background: #0ea5e9;
        }

        /* 方案二：自然平衡系 */
        .theme-2 {
            --primary-color: #0ea5e9;
            --secondary-color: #06b6d4;
            --accent-color: #22d3ee;
            --gradient: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 100%);
        }

        .theme-2 .pricing-card {
            border-top: 4px solid #06b6d4;
        }

        .theme-2 .feature-icon {
            background: #06b6d4;
        }

        /* 方案三：高级对比系 */
        .theme-3 {
            --primary-color: #0ea5e9;
            --secondary-color: #f59e0b;
            --accent-color: #fbbf24;
            --gradient: linear-gradient(135deg, #0ea5e9 0%, #f59e0b 100%);
        }

        .theme-3 .pricing-card {
            border-top: 4px solid #f59e0b;
        }

        .theme-3 .feature-icon {
            background: #f59e0b;
        }

        /* 功能对比表格 */
        .feature-comparison {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
        }

        .comparison-table th {
            font-weight: 600;
            color: #333;
        }

        .comparison-table .feature-name {
            text-align: left;
            font-weight: 500;
        }

        .check-icon {
            color: var(--primary-color);
            font-weight: bold;
        }

        .cross-icon {
            color: #ccc;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .pricing-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>升级您的AI助手体验</h1>
            <p>选择适合您需求的会员方案，解锁更多强大功能</p>
        </div>

        <!-- 配色方案选择器 -->
        <div class="theme-selector">
            <button class="theme-btn active" onclick="switchTheme('theme-1')">科技冷色系</button>
            <button class="theme-btn" onclick="switchTheme('theme-2')">自然平衡系</button>
            <button class="theme-btn" onclick="switchTheme('theme-3')">高级对比系</button>
        </div>

        <!-- 会员方案卡片 -->
        <div class="pricing-grid theme-1" id="pricingSection">
            <!-- Free 方案 -->
            <div class="pricing-card">
                <div class="card-header">
                    <div class="plan-name">Free</div>
                    <div class="plan-price">¥0</div>
                    <div class="plan-period">永久免费</div>
                </div>
                <ul class="features">
                    <li><span class="feature-icon">✓</span>基础AI对话功能</li>
                    <li><span class="feature-icon">✓</span>每日10次提问限制</li>
                    <li><span class="feature-icon">✓</span>基础回复质量</li>
                    <li><span class="feature-icon">✓</span>社区支持</li>
                </ul>
                <button class="upgrade-btn">当前方案</button>
            </div>

            <!-- Pro 方案 -->
            <div class="pricing-card popular">
                <div class="card-header">
                    <div class="plan-name">Pro</div>
                    <div class="plan-price">¥29</div>
                    <div class="plan-period">每月</div>
                </div>
                <ul class="features">
                    <li><span class="feature-icon">✓</span>无限制AI对话</li>
                    <li><span class="feature-icon">✓</span>高级回复质量</li>
                    <li><span class="feature-icon">✓</span>文件上传支持</li>
                    <li><span class="feature-icon">✓</span>优先技术支持</li>
                    <li><span class="feature-icon">✓</span>自定义指令</li>
                </ul>
                <button class="upgrade-btn">升级到 Pro</button>
            </div>

            <!-- Plus 方案 -->
            <div class="pricing-card">
                <div class="card-header">
                    <div class="plan-name">Plus</div>
                    <div class="plan-price">¥59</div>
                    <div class="plan-period">每月</div>
                </div>
                <ul class="features">
                    <li><span class="feature-icon">✓</span>Pro所有功能</li>
                    <li><span class="feature-icon">✓</span>GPT-4模型访问</li>
                    <li><span class="feature-icon">✓</span>高级数据分析</li>
                    <li><span class="feature-icon">✓</span>API访问权限</li>
                    <li><span class="feature-icon">✓</span>团队协作功能</li>
                </ul>
                <button class="upgrade-btn">升级到 Plus</button>
            </div>

            <!-- Max 方案 -->
            <div class="pricing-card">
                <div class="card-header">
                    <div class="plan-name">Max</div>
                    <div class="plan-price">¥99</div>
                    <div class="plan-period">每月</div>
                </div>
                <ul class="features">
                    <li><span class="feature-icon">✓</span>Plus所有功能</li>
                    <li><span class="feature-icon">✓</span>无限次GPT-4访问</li>
                    <li><span class="feature-icon">✓</span>专属模型训练</li>
                    <li><span class="feature-icon">✓</span>白标解决方案</li>
                    <li><span class="feature-icon">✓</span>专属客户经理</li>
                </ul>
                <button class="upgrade-btn">升级到 Max</button>
            </div>
        </div>

        <!-- 功能对比表格 -->
        <div class="feature-comparison">
            <h2 style="text-align: center; margin-bottom: 2rem; color: #333;">功能详细对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th class="feature-name">功能特性</th>
                        <th>Free</th>
                        <th>Pro</th>
                        <th>Plus</th>
                        <th>Max</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="feature-name">每日提问次数</td>
                        <td>10次</td>
                        <td>无限制</td>
                        <td>无限制</td>
                        <td>无限制</td>
                    </tr>
                    <tr>
                        <td class="feature-name">AI模型访问</td>
                        <td>基础模型</td>
                        <td>高级模型</td>
                        <td>GPT-4</td>
                        <td>GPT-4 Turbo</td>
                    </tr>
                    <tr>
                        <td class="feature-name">文件上传支持</td>
                        <td><span class="cross-icon">✗</span></td>
                        <td><span class="check-icon">✓</span></td>
                        <td><span class="check-icon">✓</span></td>
                        <td><span class="check-icon">✓</span></td>
                    </tr>
                    <tr>
                        <td class="feature-name">API访问</td>
                        <td><span class="cross-icon">✗</span></td>
                        <td><span class="cross-icon">✗</span></td>
                        <td><span class="check-icon">✓</span></td>
                        <td><span class="check-icon">✓</span></td>
                    </tr>
                    <tr>
                        <td class="feature-name">团队协作</td>
                        <td><span class="cross-icon">✗</span></td>
                        <td><span class="cross-icon">✗</span></td>
                        <td><span class="check-icon">✓</span></td>
                        <td><span class="check-icon">✓</span></td>
                    </tr>
                    <tr>
                        <td class="feature-name">专属支持</td>
                        <td><span class="cross-icon">✗</span></td>
                        <td>优先支持</td>
                        <td>专属支持</td>
                        <td>客户经理</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function switchTheme(theme) {
            // 更新主题按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新定价区域主题
            const pricingSection = document.getElementById('pricingSection');
            pricingSection.className = 'pricing-grid ' + theme;
            
            // 更新对比表格中的图标颜色
            updateComparisonIcons(theme);
        }

        function updateComparisonIcons(theme) {
            const checkIcons = document.querySelectorAll('.check-icon');
            let primaryColor;
            
            switch(theme) {
                case 'theme-1':
                    primaryColor = '#0ea5e9';
                    break;
                case 'theme-2':
                    primaryColor = '#06b6d4';
                    break;
                case 'theme-3':
                    primaryColor = '#f59e0b';
                    break;
            }
            
            checkIcons.forEach(icon => {
                icon.style.color = primaryColor;
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateComparisonIcons('theme-1');
            
            // 添加升级按钮点击事件
            document.querySelectorAll('.upgrade-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const planName = this.closest('.pricing-card').querySelector('.plan-name').textContent;
                    if (planName !== 'Free') {
                        alert(`您选择了升级到 ${planName} 方案！`);
                    }
                });
            });
        });
    </script>
</body>
</html>