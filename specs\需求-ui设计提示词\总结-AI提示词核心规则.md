# UI设计核心原则 - AI提示词规则

> 本文档提炼自7本经典UI设计书籍，整理为可直接用于AI页面设计的规则集。

---

## 📋 快速使用指南

### 提示词模板
```
请按照以下UI设计规则设计页面：

【视觉布局规则】
- 应用CRAP四原则：对比、重复、对齐、亲密性
- [具体需求...]

【交互体验规则】
- 遵循不要让我思考原则
- [具体需求...]

【动效规则】
- 参考动效三原则
- [具体需求...]
```

---

## 1️⃣ 《写给大家看的设计书》- CRAP四原则

### 核心思想
非专业设计师通过四个基本原则就能做出专业的设计。

### 设计原则

#### 📌 **C - Contrast (对比)**
```yaml
规则:
  - 元素之间必须有明显差异，避免"看起来差不多"
  - 字号对比: 标题至少比正文大2倍 (例如: 32px vs 16px)
  - 颜色对比: 主按钮与次按钮必须有明显区分
  - 粗细对比: 重要信息用粗体，次要信息用细体
  - 大小对比: 核心功能卡片应比次要卡片大1.5-2倍
  
禁止:
  - 标题18px + 正文16px (对比不足)
  - 两个同样大小的按钮颜色相近
  - 所有文字都是同一个字重
```

#### 📌 **R - Repetition (重复)**
```yaml
规则:
  - 在整个界面中重复使用相同的设计元素
  - 按钮样式: 圆角、阴影、高度保持一致
  - 间距系统: 统一使用 8px 基准 (8, 16, 24, 32, 48...)
  - 颜色系统: 主色、辅色、中性色保持一致
  - 图标风格: 统一使用线性/面性，不混用
  
示例:
  - 所有卡片圆角都是 12px
  - 所有标题都用 Semibold + 主色
  - 所有输入框高度都是 40px
```

#### 📌 **A - Alignment (对齐)**
```yaml
规则:
  - 页面上的每个元素都应该与其他元素有视觉联系
  - 优先使用左对齐 (符合阅读习惯)
  - 数字/金额使用右对齐
  - 标题和内容左边缘对齐
  - 按钮组居中对齐
  
禁止:
  - 随意居中对齐文本块
  - 元素位置看起来"差不多对齐"
  - 左对齐和居中对齐混用在同一区域
```

#### 📌 **P - Proximity (亲密性)**
```yaml
规则:
  - 相关元素靠近，不相关元素分离
  - 标题与内容间距: 8-12px
  - 内容块之间间距: 24-32px
  - 卡片内边距: 16-24px
  - 卡片之间间距: 16-24px
  
示例:
  - 表单标签和输入框间距 4px
  - 同一组按钮间距 8px
  - 不同功能区块间距 48px
```

---

## 2️⃣ 《Don't Make Me Think》- 交互自明性原则

### 核心思想
优秀的UI应该让用户无需思考就能理解如何使用。

### 设计原则

#### 📌 **自解释性**
```yaml
规则:
  - 按钮文字必须是动词 ("保存"、"提交"、"下载")
  - 避免技术术语，用用户语言 ("删除" 不是 "移除实体")
  - 图标必须配文字说明 (除非是超级通用图标如搜索、关闭)
  - 表单错误提示要说明如何修正
  
示例:
  ✅ "导出为 Markdown"
  ❌ "Export"
  
  ✅ "密码至少8位，包含数字和字母"
  ❌ "密码格式错误"
```

#### 📌 **减少认知负担**
```yaml
规则:
  - 每页只有1个主要操作
  - 选项不超过7个 (米勒定律)
  - 使用熟悉的模式 (搜索框在右上角)
  - 用进度条显示多步骤流程
  
禁止:
  - 同一页面出现3个"确定"按钮
  - 下拉菜单超过10个选项不分组
  - 重要操作隐藏在三级菜单
```

#### 📌 **即时反馈**
```yaml
规则:
  - 点击按钮必须有视觉反馈 (100ms内)
  - 加载超过1秒显示进度指示器
  - 操作成功/失败必须有明确提示
  - 表单验证实时显示 (失焦时验证)
  
示例:
  - 按钮 hover 改变颜色
  - 提交表单显示 loading 状态
  - 保存成功显示 toast 提示
```

#### 📌 **清晰的视觉层级**
```yaml
规则:
  - 页面标题 > 区块标题 > 内容标题 > 正文
  - 主按钮 > 次按钮 > 文字按钮
  - 用户应该能在5秒内找到主要操作
  
字号示例:
  - H1: 32px (页面标题)
  - H2: 24px (区块标题)
  - H3: 18px (卡片标题)
  - Body: 14-16px (正文)
```

---

## 3️⃣ 《Designing Interface Animation》- 动效设计原则

### 核心思想
动效不是装饰，是引导用户理解界面变化的工具。

### 设计原则

#### 📌 **有目的的动效**
```yaml
规则:
  - 每个动效必须服务于功能，不做纯装饰动画
  - 转场动画帮助用户理解页面关系
  - 微交互增强操作反馈
  - 引导注意力到重要信息
  
合理使用场景:
  - 按钮点击: 轻微缩放 (scale 0.95)
  - 模态框出现: 淡入 + 轻微上移
  - 列表加载: 骨架屏渐现
  - 通知消息: 从右侧滑入
```

#### 📌 **速度与缓动**
```yaml
规则:
  - 快速动效: 100-200ms (按钮反馈、hover)
  - 中速动效: 200-400ms (模态框、抽屉)
  - 慢速动效: 400-600ms (页面转场)
  - 使用自然缓动: ease-out (加速开始，减速结束)
  
禁止:
  - 动画超过 800ms (用户会感到卡顿)
  - 使用 linear (看起来机械)
  - 同时播放超过3个动画 (视觉混乱)
  
CSS 示例:
  transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
```

#### 📌 **连贯性**
```yaml
规则:
  - 元素移动应该有轨迹 (不要突然消失/出现)
  - 状态变化使用过渡 (loading -> success)
  - 页面切换保持空间连续性
  
示例:
  - 卡片点击展开: 从原位置放大到全屏
  - 删除项目: 淡出 + 其他项目平滑上移
  - Tab 切换: 下划线滑动到新位置
```

#### 📌 **性能优先**
```yaml
规则:
  - 只动画 transform 和 opacity (GPU 加速)
  - 避免动画 width, height, margin (触发重排)
  - 长列表用 will-change 提前优化
  - 移动端动画减速 20%
  
优化示例:
  ✅ transform: translateY(-10px)
  ❌ margin-top: -10px
  
  ✅ opacity: 0
  ❌ visibility: hidden (不能动画)
```

---

## 4️⃣ 《Laws of UX》- 用户体验定律

### 核心思想
将心理学原理转化为可操作的设计规则。

### 设计原则

#### 📌 **Hick's Law (希克定律)**
```yaml
原理: 选项越多，决策时间越长

规则:
  - 导航菜单不超过7个主项
  - 表单每页最多5个输入项
  - 用分组/分类简化选择
  - 用推荐选项减少决策负担
  
示例:
  ❌ 20个平铺的筛选条件
  ✅ 分为"价格"、"品牌"、"评分"三组
```

#### 📌 **Fitts's Law (菲茨定律)**
```yaml
原理: 目标越大越近，点击越容易

规则:
  - 按钮最小尺寸: 44x44px (移动端)
  - 主按钮应该最大、最显眼
  - 常用操作放在拇指热区 (移动端底部)
  - 相关操作靠近放置
  
示例:
  - 提交按钮 120x44px，取消按钮 80x44px
  - 模态框的"确定"按钮在右下角 (鼠标常停留位置)
```

#### 📌 **Miller's Law (米勒定律)**
```yaml
原理: 人只能同时记住 7±2 个信息块

规则:
  - 导航栏最多7个项目
  - 表单分步骤，每步不超过7个字段
  - 用分块记忆 (手机号: 138-0013-8000)
  - 长列表必须分页或虚拟滚动
  
示例:
  - 注册表单: 第1步基本信息(3项) → 第2步详细信息(4项)
  - 仪表盘: 显示5个核心指标卡片
```

#### 📌 **Jakob's Law (雅各布定律)**
```yaml
原理: 用户期望你的网站和其他网站类似

规则:
  - 搜索框放在右上角
  - logo 点击返回首页
  - 链接用蓝色下划线
  - "x" 关闭模态框
  - 汉堡菜单 ≡ 代表导航
  
禁止:
  - 重新发明轮子 (创造新的交互模式)
  - 绿色表示错误、红色表示成功
```

#### 📌 **Peak-End Rule (峰终定律)**
```yaml
原理: 用户只记住体验的高峰和结尾

规则:
  - 关键流程结束要有愉悦的确认页
  - 注册成功显示欢迎动画
  - 表单提交后显示感谢信息
  - 第一次使用时提供引导
  
示例:
  - 支付成功: ✅ 动画 + "订单已确认！"
  - 注册完成: 🎉 "欢迎加入！你已获得新人礼包"
```

---

## 5️⃣ 《Refactoring UI》- 前端设计实战

### 核心思想
开发者可以通过系统化的方法提升界面美观度。

### 设计原则

#### 📌 **从功能出发，不是布局**
```yaml
规则:
  - 先确定内容，再选择布局
  - 不要先画框架，而是先设计核心元素
  - 让内容决定容器大小
  
流程:
  1. 列出需要显示的信息
  2. 确定优先级
  3. 选择合适的展示方式 (卡片/列表/网格)
  4. 最后调整间距和对齐
```

#### 📌 **建立设计系统**
```yaml
规则:
  - 字号系统: 12, 14, 16, 18, 20, 24, 32, 48, 64
  - 间距系统: 4, 8, 12, 16, 24, 32, 48, 64, 96
  - 颜色系统: 每个颜色准备 9 个色阶
  - 圆角系统: 4, 8, 12, 16, 24
  - 阴影系统: sm, md, lg, xl
  
Tailwind 示例:
  text-sm (14px), text-base (16px), text-lg (18px)
  p-4 (16px), p-6 (24px), p-8 (32px)
  rounded-md (8px), rounded-lg (12px)
```

#### 📌 **少用边框，多用阴影和间距**
```yaml
规则:
  - 用阴影分隔元素比边框更优雅
  - 用背景色区分区域
  - 用间距制造呼吸感
  
示例:
  ❌ border: 1px solid #ddd
  ✅ box-shadow: 0 1px 3px rgba(0,0,0,0.1)
  
  ❌ 两个区块都加边框
  ✅ 一个浅灰背景，一个白色背景
```

#### 📌 **用留白创造高级感**
```yaml
规则:
  - 内容宽度不要超过 80ch (约 600-700px)
  - 段落间距至少 1.5 倍行高
  - 按钮内边距至少 12px 上下、24px 左右
  - 卡片内边距至少 24px
  
对比:
  普通设计: padding: 8px
  精致设计: padding: 24px 32px
```

#### 📌 **有层次的灰色**
```yaml
规则:
  - 准备至少 5 种灰色 (不是简单的 #ccc)
  - 主文本: #1a1a1a (深灰，不是纯黑)
  - 次要文本: #6b7280
  - 占位符: #9ca3af
  - 边框: #e5e7eb
  - 背景: #f9fafb
  
禁止:
  - 全部用 #000 和 #999
  - 直接用 opacity 降低透明度 (会受背景影响)
```

#### 📌 **让关键信息"跳出来"**
```yaml
规则:
  - 重要按钮用饱和度高的颜色
  - 次要按钮用灰色
  - 危险操作用红色
  - 成功状态用绿色
  - 数字/金额用更大字号 + 粗体
  
示例:
  - 主按钮: bg-blue-600 (饱和)
  - 次按钮: bg-gray-200 (低调)
  - 价格显示: 32px Bold
```

---

## 6️⃣ 《简约至上》- 简化原则

### 核心思想
简单的设计 = 删除、组织、隐藏、转移。

### 设计原则

#### 📌 **删除 (Remove)**
```yaml
规则:
  - 每个元素都要问: 不要它会怎样?
  - 删除不必要的装饰
  - 删除重复的信息
  - 默认隐藏高级选项
  
示例:
  ❌ 输入框加边框 + 背景色 + 阴影
  ✅ 只用底部边框
  
  ❌ 显示"创建时间"和"更新时间"
  ✅ 只显示"更新时间"
```

#### 📌 **组织 (Organize)**
```yaml
规则:
  - 用分组减少视觉元素数量
  - 相关功能放在一起
  - 用标签页隔离不同场景
  - 用手风琴折叠次要内容
  
示例:
  ❌ 30个平铺的设置项
  ✅ 分为"账户"、"隐私"、"通知"三个标签
```

#### 📌 **隐藏 (Hide)**
```yaml
规则:
  - 不常用的功能放到"更多"菜单
  - 高级选项默认折叠
  - 用渐进式展示 (先显示核心信息)
  - 上下文相关的操作才显示
  
示例:
  - 列表项 hover 才显示"编辑"、"删除"按钮
  - 表单默认显示必填项，"显示更多"展开可选项
```

#### 📌 **转移 (Displace)**
```yaml
规则:
  - 复杂计算让设备处理
  - 自动填充代替手动输入
  - 智能默认值
  - 记住用户选择
  
示例:
  - 地址输入: 输入邮编自动填充城市
  - 时间选择: 默认选择当前时间
  - 上次使用的筛选条件自动保存
```

---

## 7️⃣ 《设计中的设计》- 日式美学原则

### 核心思想
克制、留白、自然、本质。

### 设计原则

#### 📌 **克制**
```yaml
规则:
  - 颜色不超过3种
  - 字体不超过2种 (一个衬线、一个非衬线)
  - 避免过度装饰
  - 优先使用中性色
  
示例:
  - 主色: 一个品牌色
  - 辅色: 灰色系
  - 点缀色: 一个强调色 (用于CTA)
```

#### 📌 **留白即设计**
```yaml
规则:
  - 不要填满所有空间
  - 重要元素周围留更多空白
  - 行高至少 1.5
  - 段落间距至少 2em
  
对比:
  拥挤设计: padding: 8px, line-height: 1.2
  舒适设计: padding: 32px, line-height: 1.6
```

#### 📌 **自然与本质**
```yaml
规则:
  - 材质贴近真实 (避免过度拟物)
  - 动画符合物理规律
  - 颜色柔和 (避免过度饱和)
  - 保持功能的纯粹性
  
示例:
  ✅ 卡片阴影: 0 2px 8px rgba(0,0,0,0.08)
  ❌ 卡片阴影: 0 10px 40px rgba(255,0,255,0.8)
```

---

## 🎯 综合应用示例

### 示例1: 设计登录页面
```yaml
应用规则:
  1. CRAP原则:
     - 对比: 登录按钮用主色，背景用浅色
     - 重复: 输入框高度统一 44px，圆角统一 8px
     - 对齐: 表单元素左对齐
     - 亲密性: 标签和输入框间距 4px，输入框之间间距 16px
  
  2. 不要让我思考:
     - 按钮文字: "登录" 而非 "Submit"
     - 错误提示: "邮箱格式不正确" 而非 "Invalid input"
  
  3. 动效:
     - 按钮点击: scale(0.98) 100ms
     - 错误提示: shake 动画 300ms
  
  4. UX定律:
     - Fitts定律: 登录按钮宽度 100%
     - 峰终定律: 登录成功显示欢迎动画
  
  5. Refactoring UI:
     - 输入框不用边框，只用底部线
     - 使用 shadow-sm 而非 border
  
  6. 简约至上:
     - 删除"记住我"选项(默认记住)
     - 隐藏"忘记密码"到输入框下方小字
```

### 示例2: 设计数据表格
```yaml
应用规则:
  1. CRAP原则:
     - 对比: 表头 Bold + 深灰，内容 Regular + 中灰
     - 重复: 行高统一 48px
     - 对齐: 文字左对齐，数字右对齐
     - 亲密性: 单元格内边距 12px 16px
  
  2. 不要让我思考:
     - 可排序列显示 ↑↓ 图标
     - hover 行高亮背景色
  
  3. UX定律:
     - Miller定律: 默认显示7列，其他列可选显示
     - Jakob定律: 复选框在第一列
  
  4. 简约至上:
     - 删除竖向分隔线 (只用横线)
     - 操作按钮 hover 才显示
```

---

## 📖 快速检查清单

### 在发送给AI之前，确认：
- [ ] 是否指定了颜色对比规则？
- [ ] 是否定义了间距系统？
- [ ] 是否明确了主次按钮层级？
- [ ] 是否考虑了移动端触摸尺寸？
- [ ] 是否定义了加载和错误状态？
- [ ] 是否符合用户习惯 (Jakob定律)？
- [ ] 是否每个动效都有明确目的？

---

## 🚀 AI提示词完整模板

```markdown
# 页面设计要求

## 视觉设计规则

### CRAP原则
- **对比**: [具体要求，例如: 标题32px，正文16px]
- **重复**: [具体要求，例如: 所有卡片圆角12px]
- **对齐**: [具体要求，例如: 内容左对齐，按钮居中]
- **亲密性**: [具体要求，例如: 卡片内边距24px]

### 设计系统
- **字号**: 12, 14, 16, 18, 24, 32, 48px
- **间距**: 4, 8, 12, 16, 24, 32, 48px (基准8px)
- **颜色**: 主色 #[color], 辅色 #[color], 灰色系 5阶
- **圆角**: 8px (小组件), 12px (卡片), 16px (模态框)
- **阴影**: sm: 0 1px 3px rgba(0,0,0,0.1), md: 0 4px 6px

### 视觉层级
- H1: 32px Semibold (页面标题)
- H2: 24px Semibold (区块标题)
- Body: 16px Regular (正文)
- Caption: 14px Regular (辅助文字)

## 交互设计规则

### 自明性
- 按钮文字必须是动词
- 图标配文字说明
- 错误提示说明如何修正

### 反馈
- 按钮 hover: 改变背景色
- 点击反馈: scale(0.98) 100ms
- 加载状态: 显示 spinner
- 操作结果: toast 提示

### 可访问性
- 按钮最小尺寸: 44x44px
- 颜色对比度: 至少 4.5:1
- 链接用下划线或颜色区分

## 动效规则

- 快速反馈: 100-200ms (按钮、hover)
- 转场动画: 300ms (模态框、抽屉)
- 缓动函数: cubic-bezier(0.4, 0.0, 0.2, 1)
- 只动画 transform 和 opacity

## 简化原则

- 删除不必要的装饰
- 默认隐藏高级选项
- 相关功能分组
- 常用操作显眼位置

## 具体需求
[描述你要设计的页面]
```

---

## 📚 延伸阅读

如需深入学习，建议阅读原书：
1. 《写给大家看的设计书》→ 快速建立设计思维
2. 《Refactoring UI》→ 前端开发者必读
3. 《Don't Make Me Think》→ 交互设计基础
4. 《Laws of UX》→ 理解用户心理学

---

**更新日期**: 2025-10-30  
**适用场景**: Web UI设计、浏览器扩展、移动端H5页面
