import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { DolaAnswerService } from "./DolaAnswerService";
import { DolaPageService } from "./DolaPageService";

/**
 * Dola 答案控制器
 * 继承 BaseAnswerController，提供 Dola 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 DolaAnswerService 和 DolaPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 * - 支持 dola.com 和 cici.com 两个域名
 */
export class DolaAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: DolaAnswerService;
        pageService: DolaPageService;
    } {
        console.info('[DolaAnswerController] 创建 Dola 服务实例');
        
        return {
            answerService: new DolaAnswerService(),
            pageService: new DolaPageService()
        };
    }
}
