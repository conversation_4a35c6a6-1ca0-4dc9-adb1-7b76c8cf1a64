# Deepseek 降级 HTML 转 Markdown 优化总结

## 优化背景

针对 Deepseek 平台复制按钮失效后的降级处理场景,优化 `DeepseekClipboardService.ts` 中的 HTML 到 Markdown 转换逻辑,主要解决代码块和表格的转换问题。

参考页面: https://chat.deepseek.com/a/chat/s/783e31ea-063d-4c78-b555-29878f08feeb

## 核心问题

### 1. 代码块问题
- Deepseek 的代码块包含大量 UI 元素（语言标签、复制按钮、下载按钮、运行按钮等）
- 代码使用 token 化结构（`.token` 类），需要正确提取纯文本
- 代码块横幅元素（`.md-code-block-banner`）干扰内容提取

### 2. 表格问题
- 需要正确处理表格的 `<thead>` 和 `<tbody>` 结构
- 单元格内容可能包含换行和多余空格
- 需要正确转义管道符 `|`

## 优化方案

### 1. TurndownService 规则优化

#### 规则 1: 代码块容器处理
```typescript
turndownService.addRule('deepseekCodeBlockContainer', {
    filter: (node: HTMLElement) => {
        return node.classList && node.classList.contains('md-code-block');
    },
    replacement: (content: string, node: HTMLElement) => {
        // 提取语言标签
        const langElement = node.querySelector('.d813de27');
        const language = langElement?.textContent?.trim() || '';
        
        // 提取 pre 元素中的代码
        const preElement = node.querySelector('pre');
        if (!preElement) {
            return '\n\n```\n' + content + '\n```\n\n';
        }
        
        // 提取纯文本代码内容
        const codeText = this.extractCodeText(preElement);
        
        return '\n\n```' + language + '\n' + codeText + '\n```\n\n';
    }
});
```

**优化点**:
- 直接处理整个 `.md-code-block` 容器而不是单独的 `<pre>` 元素
- 从专门的语言标签元素 `.d813de27` 提取语言信息
- 使用优化的 `extractCodeText` 方法提取纯文本代码

#### 规则 2: 内联代码处理
```typescript
turndownService.addRule('deepseekInlineCode', {
    filter: (node: HTMLElement) => {
        return node.nodeName === 'CODE' && 
               !node.closest('.md-code-block') &&
               node.classList.contains('inline-code');
    },
    replacement: (content: string, node: HTMLElement) => {
        const text = node.textContent || '';
        return '`' + text + '`';
    }
});
```

**优化点**:
- 使用 `closest` 方法更准确地判断是否在代码块内
- 确保只处理真正的内联代码

#### 规则 3: 表格处理
```typescript
turndownService.addRule('deepseekTable', {
    filter: 'table',
    replacement: (content: string, node: HTMLElement) => {
        const table = node as HTMLTableElement;
        const rows: string[] = [];
        
        // 处理表头
        const thead = table.querySelector('thead');
        if (thead) {
            const headerRow = thead.querySelector('tr');
            if (headerRow) {
                const headers: string[] = [];
                const cells = headerRow.querySelectorAll('th');
                cells.forEach(cell => {
                    const text = this.cleanCellText(cell.textContent || '');
                    headers.push(text);
                });
                
                if (headers.length > 0) {
                    rows.push('| ' + headers.join(' | ') + ' |');
                    rows.push('| ' + headers.map(() => '---').join(' | ') + ' |');
                }
            }
        }
        
        // 处理表体
        const tbody = table.querySelector('tbody');
        if (tbody) {
            const bodyRows = tbody.querySelectorAll('tr');
            bodyRows.forEach(row => {
                const cells: string[] = [];
                const tds = row.querySelectorAll('td');
                tds.forEach(cell => {
                    const text = this.cleanCellText(cell.textContent || '');
                    cells.push(text);
                });
                
                if (cells.length > 0) {
                    rows.push('| ' + cells.join(' | ') + ' |');
                }
            });
        }
        
        return rows.length > 0 ? '\n\n' + rows.join('\n') + '\n\n' : '';
    }
});
```

**优化点**:
- 分别处理 `<thead>` 和 `<tbody>`
- 自动生成 Markdown 表格分隔行
- 使用 `cleanCellText` 方法清理单元格内容

#### 规则 4: 段落处理
```typescript
turndownService.addRule('deepseekParagraph', {
    filter: (node: HTMLElement) => {
        return node.classList && node.classList.contains('ds-markdown-paragraph');
    },
    replacement: (content: string) => {
        return content ? '\n\n' + content.trim() + '\n\n' : '';
    }
});
```

**优化点**:
- 识别 Deepseek 特有的段落类 `.ds-markdown-paragraph`
- 确保段落前后有适当的空行

### 2. 代码提取优化

#### 主方法: extractCodeText
```typescript
private extractCodeText(preElement: HTMLElement): string {
    // 首先尝试简单的 textContent 方法
    let code = preElement.textContent || '';
    
    // 清理代码内容
    code = code
        .replace(/^[\s\n]+/, '')  // 移除开头的空白
        .replace(/[\s\n]+$/, '')  // 移除结尾的空白
        .trim();
    
    // 如果通过 textContent 获取失败，使用递归方法
    if (!code || code.length === 0) {
        code = this.extractCodeTextRecursive(preElement);
    }
    
    return code;
}
```

**优化点**:
- 优先使用简单的 `textContent` 方法（因为 Deepseek 的代码块已经保留了正确的文本）
- 提供递归降级方法作为备选
- 简化代码提取逻辑,提高性能

#### 递归降级方法
```typescript
private extractCodeTextRecursive(element: HTMLElement): string {
    const lines: string[] = [];
    
    const extractText = (node: Node): void => {
        if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent || '';
            if (text) {
                lines.push(text);
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const el = node as HTMLElement;
            
            // 跳过 UI 元素
            if (el.classList.contains('ds-atom-button') || 
                el.classList.contains('md-code-block-banner') ||
                el.classList.contains('md-code-block-banner-wrap')) {
                return;
            }
            
            // 处理换行
            if (el.nodeName === 'BR') {
                lines.push('\n');
                return;
            }
            
            // 递归处理子节点
            node.childNodes.forEach(child => extractText(child));
        }
    };
    
    extractText(element);
    
    return lines.join('').trim();
}
```

### 3. HTML 清理优化

```typescript
private cleanHtmlContent(htmlContent: string): string {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // 要移除的选择器列表
    const selectorsToRemove = [
        // 代码块相关的 UI 元素
        '.md-code-block-banner',
        '.md-code-block-banner-wrap',
        '.md-code-block-banner-lite',
        '.efa13877',  // 复制/下载按钮容器
        '.d813de27',  // 语言标签
        
        // 所有按钮
        'button',
        '.ds-atom-button',
        '.ds-icon-button',
        '.ds-text-button',
        
        // SVG 图标占位符
        '[data-placeholder="svg-icon"]',
        
        // 其他 UI 元素
        '.code-info-button-text'
    ];
    
    // 批量移除元素
    selectorsToRemove.forEach(selector => {
        const elements = tempDiv.querySelectorAll(selector);
        elements.forEach(element => element.remove());
    });
    
    // 移除代码块内所有 SVG 相关元素
    const codeBlocks = tempDiv.querySelectorAll('.md-code-block');
    codeBlocks.forEach(block => {
        const icons = block.querySelectorAll('.ds-icon, .ds-icon-button__icon');
        icons.forEach(icon => icon.remove());
    });
    
    return tempDiv.innerHTML;
}
```

**优化点**:
- 集中定义要移除的选择器列表
- 批量处理移除操作
- 特别处理代码块内的图标元素

### 4. Markdown 后处理优化

```typescript
private postProcessMarkdown(markdown: string): string {
    let result = markdown;
    
    // 1. 清理代码块前后的多余文本
    result = result.replace(/([复制下载运行]+)(\n)?```/g, '```');
    
    // 2. 确保代码块格式正确
    result = result.replace(/([^\n])\n```/g, '$1\n\n```');
    result = result.replace(/```\n([^\n])/g, '```\n\n$1');
    
    // 3. 清理表格
    result = result.replace(/([^\n])\n\|/g, '$1\n\n|');
    result = result.replace(/\|\n([^\n|])/g, '|\n\n$1');
    result = result.replace(/\|\s+([^|]*?)\s+\|/g, '| $1 |');
    
    // 4. 标题处理
    result = result.replace(/([^\n])\n(#{1,6} )/g, '$1\n\n$2');
    result = result.replace(/(#{1,6} [^\n]+)\n([^\n#])/g, '$1\n\n$2');
    
    // 5. 列表处理
    result = result.replace(/([^\n])\n([*\-+] )/g, '$1\n\n$2');
    result = result.replace(/([*\-+] [^\n]+)\n([^\n*\-+])/g, '$1\n\n$2');
    
    // 6. 移除过多的空行
    result = result.replace(/\n{3,}/g, '\n\n');
    
    // 7. 清理段落间距
    result = result.replace(/([。！？.!?])\n([^\n])/g, '$1\n\n$2');
    
    // 8. 清理首尾空白
    result = result.trim();
    
    // 9. 最终检查：确保代码块闭合正确
    const codeBlockMatches = result.match(/```/g);
    if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
        console.warn('[DeepseekClipboardService] 检测到代码块未正确闭合');
    }
    
    return result;
}
```

**优化点**:
- 移除代码块前的 UI 按钮文本（"复制"、"下载"、"运行"）
- 确保代码块、表格、标题、列表前后都有适当的空行
- 优化表格格式
- 添加代码块闭合检查
- 清理段落间距

### 5. 表格单元格清理

```typescript
private cleanCellText(text: string): string {
    return text
        .trim()
        .replace(/\n+/g, ' ')  // 移除换行符
        .replace(/\s+/g, ' ')  // 合并多个空格
        .replace(/\|/g, '\\|'); // 转义管道符
}
```

**功能**:
- 清理单元格内的换行符
- 合并多个空格
- 转义管道符避免表格格式错误

## 测试结果

### 代码块测试
- ✅ 正确提取多种语言的代码块（Java, XML, Dart 等）
- ✅ 正确移除 UI 元素（复制、下载、运行按钮）
- ✅ 保留代码的原始格式和缩进
- ✅ 正确识别代码语言

### 表格测试
- ✅ 正确转换 Markdown 表格格式
- ✅ 表头和表体分离清晰
- ✅ 单元格内容正确处理
- ✅ 管道符正确转义

### 整体格式测试
- ✅ 标题、段落、列表间距正确
- ✅ 代码块前后空行正确
- ✅ 表格前后空行正确
- ✅ 无多余的空行

## 关键改进点

1. **针对性优化**: 根据 Deepseek 实际 HTML 结构优化选择器和规则
2. **性能优化**: 使用 `textContent` 优先策略,减少递归操作
3. **健壮性提升**: 提供降级方法,确保各种情况下都能提取内容
4. **格式规范**: 通过后处理确保生成标准的 Markdown 格式
5. **错误检测**: 添加代码块闭合检查,便于调试

## 后续优化建议

1. **监控 Deepseek 结构变化**: 定期检查页面结构是否有更新
2. **添加更多测试用例**: 覆盖更多边缘情况（嵌套列表、复杂表格等）
3. **性能监控**: 添加性能日志,监控转换耗时
4. **用户反馈**: 收集用户反馈,持续优化转换质量

## 相关文件

- `extension/src/content/adapters/deepseek/DeepseekClipboardService.ts` - 主要实现文件
- `extension/src/content/core/BaseClipboardService.ts` - 基类
- `extension/src/content/configs/SelectorManager.ts` - 选择器配置

## 参考资源

- [Turndown 官方文档](https://github.com/mixmark-io/turndown)
- [Deepseek 测试页面](https://chat.deepseek.com/a/chat/s/783e31ea-063d-4c78-b555-29878f08feeb)
- [Markdown 表格规范](https://www.markdownguide.org/extended-syntax/#tables)
