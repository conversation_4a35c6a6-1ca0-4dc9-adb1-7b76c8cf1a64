// 文件选择服务
export interface FileSelectOptions {
  mode: 'directory' | 'file'
  accept?: string[]
  multiple?: boolean
}

export interface FileSelectResult {
  success: boolean
  path?: string
  paths?: string[]
  error?: string
}

class FileSelectorService {
  // 检查是否支持文件系统访问API
  private supportsFileSystemAccess(): boolean {
    return 'showDirectoryPicker' in window && 'showOpenFilePicker' in window
  }

  // 根据目录名生成默认完整路径
  private getDefaultFullPath(directoryName: string): string {
    const userAgent = navigator.userAgent

    if (userAgent.includes('Windows')) {
      return `C:\\Users\\<USER>\\Documents\\${directoryName}`
    } else if (userAgent.includes('Mac')) {
      return `/Users/<USER>/Documents/${directoryName}`
    } else {
      return `/home/<USER>/Documents/${directoryName}`
    }
  }

  // 使用现代文件系统访问API选择目录
  private async selectDirectoryModern(): Promise<FileSelectResult> {
    try {
      // @ts-ignore - showDirectoryPicker 可能不在类型定义中
      const dirHandle = await window.showDirectoryPicker({
        mode: 'readwrite'
      })

      // 由于安全限制，现代API无法获取完整路径
      // 我们需要提示用户手动输入完整路径
      const directoryName = dirHandle.name
      const fullPath = prompt(
        `已选择目录: ${directoryName}\n\n由于浏览器安全限制，请手动输入该目录的完整路径:\n\n例如:\nWindows: C:\\Users\\<USER>\\Documents\\${directoryName}\nmacOS: /Users/<USER>/Documents/${directoryName}\nLinux: /home/<USER>/Documents/${directoryName}`,
        this.getDefaultFullPath(directoryName)
      )

      if (fullPath === null) {
        return {
          success: false,
          error: '用户取消了路径输入'
        }
      }

      if (!fullPath.trim()) {
        return {
          success: false,
          error: '路径不能为空'
        }
      }

      return {
        success: true,
        path: fullPath.trim()
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: '用户取消了选择'
        }
      }
      return {
        success: false,
        error: `选择目录失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  // 降级方案：使用传统的input元素
  private async selectDirectoryFallback(): Promise<FileSelectResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.webkitdirectory = true
      input.style.display = 'none'

      input.onchange = (event) => {
        const target = event.target as HTMLInputElement
        if (target.files && target.files.length > 0) {
          // 从第一个文件的路径中提取目录路径
          const firstFile = target.files[0]
          const pathParts = firstFile.webkitRelativePath.split('/')
          const directoryName = pathParts[0]

          // 同样需要用户输入完整路径
          const fullPath = prompt(
            `已选择目录: ${directoryName}\n\n由于浏览器安全限制，请手动输入该目录的完整路径:\n\n例如:\nWindows: C:\\Users\\<USER>\\Documents\\${directoryName}\nmacOS: /Users/<USER>/Documents/${directoryName}\nLinux: /home/<USER>/Documents/${directoryName}`,
            this.getDefaultFullPath(directoryName)
          )

          if (fullPath === null) {
            resolve({
              success: false,
              error: '用户取消了路径输入'
            })
          } else if (!fullPath.trim()) {
            resolve({
              success: false,
              error: '路径不能为空'
            })
          } else {
            resolve({
              success: true,
              path: fullPath.trim()
            })
          }
        } else {
          resolve({
            success: false,
            error: '未选择任何目录'
          })
        }
        document.body.removeChild(input)
      }
      
      input.oncancel = () => {
        resolve({
          success: false,
          error: '用户取消了选择'
        })
        document.body.removeChild(input)
      }
      
      document.body.appendChild(input)
      input.click()
    })
  }

  // 简单的路径输入对话框（最终降级方案）
  private async selectDirectoryPrompt(currentPath?: string): Promise<FileSelectResult> {
    const defaultPaths = {
      'Windows': 'C:\\Users\\<USER>\\Documents',
      'macOS': '/Users/<USER>/Documents',
      'Linux': '/home/<USER>/Documents'
    }
    
    const os = navigator.userAgent.includes('Windows') ? 'Windows' : 
              navigator.userAgent.includes('Mac') ? 'macOS' : 'Linux'
    
    const defaultPath = defaultPaths[os] || '/path/to/directory'
    const promptMessage = `请输入导出路径:\n\n建议路径格式:\n${defaultPath}`
    
    const result = prompt(promptMessage, currentPath || defaultPath)
    
    if (result === null) {
      return {
        success: false,
        error: '用户取消了选择'
      }
    }
    
    if (!result.trim()) {
      return {
        success: false,
        error: '路径不能为空'
      }
    }
    
    return {
      success: true,
      path: result.trim()
    }
  }

  // 主要的目录选择方法
  async selectDirectory(currentPath?: string): Promise<FileSelectResult> {
    try {
      // 由于浏览器安全限制，直接使用prompt方法更可靠
      console.log('Using prompt for directory selection')
      return await this.selectDirectoryPrompt(currentPath)

    } catch (error) {
      console.error('Directory selection failed:', error)
      return {
        success: false,
        error: `选择目录失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }

  // 验证路径格式
  validatePath(path: string): { valid: boolean; error?: string } {
    if (!path || !path.trim()) {
      return { valid: false, error: '路径不能为空' }
    }
    
    const trimmedPath = path.trim()
    
    // 基本的路径格式验证
    const windowsPathRegex = /^[a-zA-Z]:\\(?:[^<>:"|?*\r\n]+\\)*[^<>:"|?*\r\n]*$/
    const unixPathRegex = /^\/(?:[^\/\0]+\/)*[^\/\0]*$/
    const relativePathRegex = /^(?:[^\/\0<>:"|?*\r\n]+\/)*[^\/\0<>:"|?*\r\n]*$/
    
    if (windowsPathRegex.test(trimmedPath) || 
        unixPathRegex.test(trimmedPath) || 
        relativePathRegex.test(trimmedPath)) {
      return { valid: true }
    }
    
    return { valid: false, error: '路径格式不正确' }
  }
}

export const fileSelectorService = new FileSelectorService()
