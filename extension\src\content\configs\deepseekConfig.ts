import { SelectorConfig } from "./SelectorManager";

/**
 * DeepSeek 平台选择器配置
 * 用于 DeepSeek Chat 页面的元素定位
 * 页面地址: https://chat.deepseek.com
 */
export const deepseekSelector: SelectorConfig = {
  /**
   * 聊天输入框选择器
   * DeepSeek 使用 textarea 作为输入框
   */
  inputField: [
    'textarea._27c9245',
    'textarea.ds-scroll-area',
    'textarea[placeholder*="DeepSeek"]',
    'textarea[placeholder*="发送消息"]',
    'textarea[rows="2"]',
    '.aaff8b8f textarea',
    '._77cefa5 textarea',
    '._3d616d3 textarea'
  ],

  /**
   * 发送按钮选择器
   * DeepSeek 的发送按钮可能在不同状态下有不同的类名
   */
  sendButton: [
    'button._7436101:not(.ds-icon-button--disabled)',
    '.bcc55ca1:not(.ds-icon-button--disabled)',
    'button[aria-disabled="false"]._7436101',
    '._3d616d3 button:not([disabled])',
    '.ec4f5d61 button:not(.ds-icon-button--disabled)',
    'div._3d616d3 button[type="button"]:not([disabled])'
  ],

  /**
   * 页面头部内容选择器
   * 用于识别当前对话标题
   */
  headerContent: [
    '.f8d1e4c0',
    '.afa34042',
    '._2be88ba .afa34042',
    'div[tabindex="0"].afa34042'
  ],

  /**
   * 聊天内容列表容器选择器
   * 包含所有对话消息的主容器
   */
  chatContentList: [
    '.dad65929',
    '._0f72b0b .dad65929',
    '.ds-scroll-area .dad65929',
    '._8f60047 .dad65929'
  ],

  /**
   * 用户提示词消息项选择器
   * 用户发送的消息容器
   */
  promptItem: [
    '._9663006 > .ds-message',
  ],

  /**
   * 用户提示词内容选择器
   * 用户消息的具体内容区域
   */
  promptContent: [
    '.ds-message > .fbb737a4',
  ],

  /**
   * AI回答消息项选择器
   * AI回复的消息容器
   */
  answerItem: [
    '.dad65929 > ._4f9bf79:has(.ds-message)',
  ],

  /**
   * AI回答完成区域选择器
   * 回答完成后显示操作按钮的区域
   */
  answerCompletion: [
    '._0a3d93b > .ds-flex._965abe9._54866f7:has(> .ds-icon-button.db183363)',
    '.ds-flex._0a3d93b',
    '._4f9bf79 ._0a3d93b',
    'div._0a3d93b'
  ],

  /**
   * 复制按钮选择器
   * 用于复制 AI 回答内容的按钮
   */
  copyButton: [
    '.ds-icon-button.db183363',
    '._0a3d93b .db183363',
    '._965abe9 .db183363',
    'div.ds-icon-button.db183363[tabindex="-1"]',
    '._4f9bf79 .ds-icon-button.db183363',
    'div[role="button"].ds-icon-button.db183363',
    '.ds-flex._0a3d93b > .ds-icon-button.db183363:nth-child(1)',
    'button.ds-icon-button.db183363'
  ],

  /**
   * Markdown 内容区域选择器
   * AI 回答的 Markdown 渲染区域
   */
  markdown: [
    '.ds-markdown:has(.ds-markdown-paragraph)',
    '.ds-message._63c77b1 .ds-markdown',
    '._4f9bf79 .ds-markdown',
    'div.ds-markdown'
  ]
};

