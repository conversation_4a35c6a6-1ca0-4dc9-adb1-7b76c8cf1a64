# 平台类型优化设计方案

## 📋 问题分析

### 当前问题
1. **类型重复**：`AIPlatform` 和 `PlatformName` 功能重合
2. **命名不一致**：平台值的大小写不统一（`chatgpt` vs `ChatGPT`）
3. **接口冗余**：`Platform` 接口中的 `id` 字段与 `name` 字段语义重叠

### 现有定义

#### 在 `database_entity.ts` 中
```typescript
export type PlatformName = 'DeepSeek' | 'Kimi' | 'ChatGPT' | 'Claude' | 'Gemini' | 'Grok'

export interface PlatformEntity extends DatabaseRow {
  id?: number
  name: PlatformName  // 唯一索引
  url: string
  icon?: string
  icon_base64?: string
  is_delete?: number
}
```

#### 在 `index.ts` 中
```typescript
export type AIPlatform =
  | 'chatgpt'
  | 'deepseek'
  | 'claude'
  | 'gemini'
  | 'kimi'
  | 'poe'
  | 'perplexity'
  | 'you'
  | 'grok';

export interface Platform {
  id: AIPlatform;
  name: string;
  url: string;
  enabled: boolean;
  selectors: {
    inputField: string;
    sendButton: string;
    messageContainer: string;
  };
}
```

#### 在 `PlatformConfigType.ts` 中
```typescript
export interface PlatformConfig {
  id: string
  name: string
  platform: AIPlatform
  url: string
  patterns: {
    hostname: RegExp
    validPath?: RegExp
  }
}
```

---

## 🎯 优化目标

1. **统一平台类型定义**：只保留 `PlatformName` 作为平台标识
2. **统一命名规范**：使用 PascalCase（首字母大写）作为平台名称
3. **简化接口结构**：移除冗余字段，优化接口设计
4. **保持向后兼容**：确保数据库数据和现有配置可以平滑迁移

---

## 📐 优化方案

### 方案 A：扩展 PlatformName（推荐）

#### 1. 扩展 `PlatformName` 类型
在 `database_entity.ts` 中：
```typescript
export type PlatformName = 
  | 'DeepSeek' 
  | 'Kimi' 
  | 'ChatGPT' 
  | 'Claude' 
  | 'Gemini' 
  | 'Grok'
  | 'Poe'
  | 'Perplexity'
  | 'You'
```

#### 2. 移除 `AIPlatform`
从 `index.ts` 中删除 `AIPlatform` 类型定义

#### 3. 修改 `Platform` 接口
```typescript
export interface Platform {
  name: PlatformName;  // 移除 id，使用 name 作为唯一标识
  url: string;
  enabled: boolean;
  selectors: {
    inputField: string;
    sendButton: string;
    messageContainer: string;
  };
}
```

#### 4. 修改 `PlatformConfig` 接口
```typescript
export interface PlatformConfig {
  name: PlatformName;  // 移除 id，使用 name
  platform: PlatformName;  // 保持一致性
  url: string;
  patterns: {
    hostname: RegExp;
    validPath?: RegExp;
  };
}
```

**优点**：
- 统一使用 PascalCase，符合品牌名称规范
- 语义清晰，`PlatformName` 即平台名称
- 数据库设计更规范（name 作为唯一索引）

**缺点**：
- 需要大量修改现有代码（小写转大写）

---

### 方案 B：使用小写 + 映射（折中方案）

保持 `PlatformName` 为 PascalCase，但提供小写映射：

```typescript
export type PlatformName = 
  | 'DeepSeek' 
  | 'Kimi' 
  | 'ChatGPT' 
  | 'Claude' 
  | 'Gemini' 
  | 'Grok'
  | 'Poe'
  | 'Perplexity'
  | 'You'

// 平台名称映射（用于 URL 匹配等场景）
export const PlatformNameMap: Record<string, PlatformName> = {
  'chatgpt': 'ChatGPT',
  'deepseek': 'DeepSeek',
  'claude': 'Claude',
  'gemini': 'Gemini',
  'kimi': 'Kimi',
  'grok': 'Grok',
  'poe': 'Poe',
  'perplexity': 'Perplexity',
  'you': 'You'
} as const
```

**优点**：
- 兼容现有小写使用场景
- 提供灵活的映射转换

**缺点**：
- 增加了一层映射复杂度

---

## ❓ 需要讨论的问题

### 1. **PlatformConfig 中的字段冗余**
```typescript
export interface PlatformConfig {
  id: string          // ❓ 这个 id 是什么用途？
  name: string        // ❓ 这个 name 是显示名称还是标识？
  platform: AIPlatform // ❓ 这个 platform 和 id/name 有什么区别？
  url: string
}
```

**疑问**：
- `id`、`name`、`platform` 三个字段的语义区别是什么？
- 是否 `id` 用于内部标识，`name` 用于显示，`platform` 用于类型？
- 能否简化为只使用 `platform: PlatformName`？

**建议方案**：
```typescript
export interface PlatformConfig {
  platform: PlatformName;  // 作为唯一标识
  displayName?: string;    // 可选的显示名称（如果需要本地化）
  url: string;
  patterns: {
    hostname: RegExp;
    validPath?: RegExp;
  };
}
```

### 2. **Platform 接口的使用场景**
```typescript
export interface Platform {
  id: AIPlatform;
  name: string;
  url: string;
  enabled: boolean;
  selectors: { ... };
}
```

**疑问**：
- 这个 `Platform` 接口主要用于什么场景？
- `name` 是显示名称还是标识？
- `selectors` 是否应该移到 `PlatformConfig` 中？

**建议方案**：
```typescript
// 如果 Platform 用于前端设置页面
export interface PlatformSettings {
  platform: PlatformName;
  enabled: boolean;
  customUrl?: string;  // 用户自定义 URL
}

// 选择器配置单独管理
export interface PlatformSelectors {
  platform: PlatformName;
  inputField: string;
  sendButton: string;
  messageContainer: string;
}
```

### 3. **平台名称的大小写规范**

**当前情况**：
- 数据库：PascalCase (`ChatGPT`, `DeepSeek`)
- 前端代码：lowercase (`chatgpt`, `deepseek`)

**建议**：
- ✅ **统一使用 PascalCase**，理由：
  - 符合品牌名称规范
  - 与数据库设计一致
  - 更易读和识别
  
- 🔄 **提供迁移脚本**：将现有小写值转换为 PascalCase

### 4. **数据库迁移策略**

**问题**：
- 现有数据库中可能存在小写的平台名称
- 现有配置文件中可能使用小写

**建议**：
1. 创建数据库迁移脚本
2. 在代码中添加兼容层（临时）
3. 逐步废弃小写用法

---

## 🚀 实施步骤（待确认后执行）

### 第一阶段：类型定义优化
1. ✅ 扩展 `PlatformName` 类型
2. ✅ 移除 `AIPlatform` 类型
3. ✅ 修改 `Platform` 接口
4. ✅ 修改 `PlatformConfig` 接口

### 第二阶段：代码迁移
1. 替换所有 `AIPlatform` 引用为 `PlatformName`
2. 更新所有平台字符串值为 PascalCase
3. 移除接口中的冗余 `id` 字段
4. 更新相关的类型导入

### 第三阶段：数据迁移
1. 创建数据库迁移脚本
2. 更新配置文件
3. 测试兼容性

### 第四阶段：清理
1. 移除临时兼容代码
2. 更新文档
3. 代码审查和测试

---

## 📝 请您确认

请针对以下问题给出您的意见：

1. **采用哪个方案？**
   - [ ] 方案 A：完全使用 PascalCase
   - [ ] 方案 B：PascalCase + 小写映射
   - [ ] 其他方案

2. **PlatformConfig 的字段设计**
   - 是否采用简化的设计方案？
   - `id`、`name`、`platform` 三个字段的具体用途说明

3. **Platform 接口的定位**
   - 这个接口主要用于哪些场景？
   - 是否需要拆分为多个更具体的接口？

4. **迁移策略**
   - 是否需要保持向后兼容？
   - 是否立即执行还是分阶段迁移？

5. **命名规范**
   - 是否统一使用 PascalCase？
   - 是否需要显示名称和标识符分离？

---

## 📌 补充说明

- 此方案旨在提高代码的可维护性和一致性
- 所有修改都会保证类型安全
- 会提供完整的迁移脚本和文档
- 可根据您的反馈调整方案细节
