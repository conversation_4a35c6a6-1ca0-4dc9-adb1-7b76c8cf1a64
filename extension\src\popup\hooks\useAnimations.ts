import { useEffect } from 'react'

export const useAnimations = () => {
  useEffect(() => {
    // Add hover effects to buttons
    const buttons = document.querySelectorAll('.btn')
    buttons.forEach(btn => {
      const handleMouseEnter = () => {
        if (btn instanceof HTMLElement) {
          btn.style.transform = 'translateY(-1px)'
        }
      }
      
      const handleMouseLeave = () => {
        if (btn instanceof HTMLElement) {
          btn.style.transform = 'translateY(0)'
        }
      }
      
      btn.addEventListener('mouseenter', handleMouseEnter)
      btn.addEventListener('mouseleave', handleMouseLeave)
      
      // Cleanup
      return () => {
        btn.removeEventListener('mouseenter', handleMouseEnter)
        btn.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

    // Add hover effects to cards
    const cards = document.querySelectorAll('.card')
    cards.forEach(card => {
      const handleMouseEnter = () => {
        if (card instanceof HTMLElement) {
          card.style.transform = 'translateY(-2px)'
          card.style.boxShadow = 'var(--shadow-lg)'
        }
      }
      
      const handleMouseLeave = () => {
        if (card instanceof HTMLElement) {
          card.style.transform = 'translateY(0)'
          card.style.boxShadow = 'var(--shadow-md)'
        }
      }
      
      card.addEventListener('mouseenter', handleMouseEnter)
      card.addEventListener('mouseleave', handleMouseLeave)
      
      // Cleanup
      return () => {
        card.removeEventListener('mouseenter', handleMouseEnter)
        card.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

    // Add floating animation to elements with float class
    const floatingElements = document.querySelectorAll('.float')
    floatingElements.forEach(element => {
      if (element instanceof HTMLElement) {
        element.style.animationDelay = Math.random() * 2 + 's'
      }
    })

  }, [])
}

export const usePageTransition = (activePage: string) => {
  useEffect(() => {
    // Add fade-in animation to page content
    const pageContent = document.querySelector('.page.active')
    if (pageContent instanceof HTMLElement) {
      pageContent.style.animation = 'none'
      // Force reflow
      pageContent.offsetHeight
      pageContent.style.animation = 'fadeIn 0.3s ease'
    }
  }, [activePage])
}
