import { MarkdownExportButton } from './components/MarkdownExportButton';
import { markdownExportService } from '../service/MarkdownExportService';
import SelectorManager from '../configs/SelectorManager';
import { DOMUtils } from '../utils/DOMUtils';
import { EchoSyncEventEnum } from '../types/DOMEnum';

/**
 * Markdown 导出注入器
 * 监听答案完成事件，在每个答案中注入导出按钮
 */
export class MarkdownExportInject {
    // private buttons: Map<Element, MarkdownExportButton> = new Map();
    // private isActive: boolean = false;
    // private answerUidSet: Set<string> = new Set();

    constructor() {
        this.setupEventListeners();
    }

    /**
     * 设置事件监听
     */
    private setupEventListeners(): void {
        // 使用箭头函数自动绑定 this，无需手动 bind
        document.addEventListener(EchoSyncEventEnum.ANSWER_EXTRACTED, this.handleAnswerExtracted);
        console.info('[MarkdownExportInject] 事件监听器已设置');
    }

    /**
     * 处理答案提取完成事件
     * 使用箭头函数自动绑定 this 上下文
     */
    private handleAnswerExtracted = (event: Event): void => {


        const customEvent = event as CustomEvent;
        const { completionElement, answerIndex, chatId } = customEvent.detail;
        const answerUid = chatId + '_' + answerIndex;
        // if (this.answerUidSet.has(answerUid)) {
        //     console.warn('[MarkdownExportInject] 已存在相同UID的答案，跳过注入');
        //     return;
        // }
        // this.answerUidSet.add(answerUid);

        console.info('[MarkdownExportInject] 收到答案提取事件', customEvent.detail);

        // 注入导出按钮
        this.injectButton(completionElement, answerIndex);
    }

    /**
     * 注入导出按钮
     */
    private injectButton(completionElement: Element, answerIndex: number): void {
        try {
            const selectors = SelectorManager.getSelector();
            if (!selectors) {
                console.warn('[MarkdownExportInject] 选择器配置未找到');
                return;
            }

            // 查找 answerCompletion 组件
            // const completionElement = DOMUtils.findElementInContainer(
            //     answerElement,
            //     selectors.answerCompletion
            // );

            // if (!completionElement) {
            //     console.warn('[MarkdownExportInject] 未找到 answerCompletion 组件');
            //     return;
            // }

            // 检查是否已注入
            // if (this.buttons.has(completionElement)) {
            //     console.info('[MarkdownExportInject] 按钮已存在，跳过注入');
            //     return;
            // }

            // 创建按钮组件
            const button = new MarkdownExportButton(answerIndex);
            
            // 设置点击回调 - 直接导出，不显示 Modal
            button.onClick((index) => {
                this.handleButtonClick(index);
            });

            // 渲染按钮元素
            const buttonElement = button.render();

            // 策略1：尝试查找复制按钮并插入到旁边（Kimi、ChatGPT等平台）
            const copyButton = DOMUtils.findElementInContainer(
                completionElement,
                selectors.copyButton
            );

            if (copyButton && copyButton.parentElement) {
                // 将 Markdown 按钮插入到复制按钮旁边
                copyButton.parentElement.insertBefore(buttonElement, copyButton.nextSibling);
                console.info('[MarkdownExportInject] 导出按钮已插入到复制按钮旁边');
            } else {
                // 策略2：没有复制按钮，直接添加到 completionElement 底部（Poe等平台）
                console.info('[MarkdownExportInject] 未找到复制按钮，将按钮添加到答案区域底部');
                
                // 创建一个容器来放置按钮，保持样式一致
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = 'display: flex; justify-content: flex-end; margin-top: 8px; padding: 4px 0;';
                buttonContainer.appendChild(buttonElement);
                
                completionElement.appendChild(buttonContainer);
            }

            // 存储按钮实例
            // this.buttons.set(completionElement, button);

            console.info('[MarkdownExportInject] Markdown 导出按钮注入成功', { answerIndex });

        } catch (error) {
            console.error('[MarkdownExportInject] 注入按钮失败', error);
        }
    }

    /**
     * 处理按钮点击
     * 直接调用导出服务，不显示 Modal
     */
    private async handleButtonClick(answerIndex: number): Promise<void> {
        console.info('[MarkdownExportInject] 按钮被点击，直接导出', { answerIndex });

        try {
            // 直接调用导出服务
            await markdownExportService.exportToMarkdown(answerIndex);
        } catch (error) {
            console.error('[MarkdownExportInject] 导出失败', error);
        }
    }

    /**
     * 启动注入器
     */
    // public start(): void {
    //     if (this.isActive) {
    //         console.warn('[MarkdownExportInject] 注入器已经启动');
    //         return;
    //     }

    //     this.isActive = true;
    //     console.info('[MarkdownExportInject] 注入器已启动');
    // }

    /**
     * 停止注入器
     */
    // public stop(): void {
    //     // if (!this.isActive) {
    //     //     return;
    //     // }

    //     // this.isActive = false;

    //     // 清理所有按钮
    //     // this.buttons.forEach((button) => {
    //     //     button.destroy();
    //     // });
    //     // this.buttons.clear();

    //     console.info('[MarkdownExportInject] 注入器已停止');
    // }

    /**
     * 销毁注入器
     */
    public destroy(): void {
        // this.stop();
        // this.answerUidSet.clear();
        
        // 箭头函数可以直接移除，因为它是类的属性
        document.removeEventListener(EchoSyncEventEnum.ANSWER_EXTRACTED, this.handleAnswerExtracted);
        
        console.info('[MarkdownExportInject] 注入器已销毁');
    }
}
