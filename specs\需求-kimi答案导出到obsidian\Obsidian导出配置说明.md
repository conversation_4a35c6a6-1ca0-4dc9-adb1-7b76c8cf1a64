# Obsidian 导出功能 - 配置说明

## Chrome Downloads API 限制

Chrome 扩展的 `chrome.downloads.download` API 有以下限制：

### ❌ 不支持的路径格式
- 绝对路径：`D:\syncthing\obsidian-mark\AIQ\file.md`
- 带盘符的路径：`C:\Users\<USER>\file.md`
- 反斜杠路径（Windows 风格）

### ✅ 支持的路径格式
- 相对路径：`AIQ/file.md`
- 仅文件名：`file.md`
- 正斜杠分隔：`folder/subfolder/file.md`

**重要：** 相对路径是相对于**浏览器的默认下载目录**

## 当前实现方案

### 路径转换逻辑

```typescript
// 用户配置的导出路径
exportPath = "D:\syncthing\obsidian-mark\AIQ"

// 提取最后一级目录名
folderName = "AIQ"

// 生成相对路径
relativePath = "AIQ/filename.md"

// 实际保存位置
// 浏览器下载目录 + AIQ/filename.md
// 例如：C:\Users\<USER>\Downloads\AIQ\filename.md
```

### 文件保存位置

```
浏览器默认下载目录/AIQ/filename.md
```

**示例：**
- Chrome 默认下载目录：`C:\Users\<USER>\Downloads`
- 相对路径：`AIQ/7.md`
- **实际保存位置**：`C:\Users\<USER>\Downloads\AIQ\7.md`

## 配置方法

### 方案 A：调整浏览器下载目录（推荐）

**步骤：**

1. **打开 Chrome 设置**
   - 地址栏输入：`chrome://settings/downloads`
   - 或：设置 → 下载内容

2. **修改下载位置**
   - 点击"更改"按钮
   - 选择 Obsidian vault 的上级目录
   - 例如：`D:\syncthing\obsidian-mark`

3. **结果**
   - 扩展配置的导出路径：`D:\syncthing\obsidian-mark\AIQ`
   - 提取的相对路径：`AIQ/filename.md`
   - 浏览器下载目录：`D:\syncthing\obsidian-mark`
   - **实际保存位置**：`D:\syncthing\obsidian-mark\AIQ\filename.md` ✅

**优点：**
- 文件直接保存到 Obsidian vault
- 无需手动移动文件
- 路径完全匹配用户配置

### 方案 B：配置为默认下载目录的子文件夹

**步骤：**

1. **保持浏览器默认下载目录**
   - 例如：`C:\Users\<USER>\Downloads`

2. **在扩展中配置相对路径**
   - 例如：`AIQ`（仅目录名）

3. **文件会保存到**
   - `C:\Users\<USER>\Downloads\AIQ\filename.md`

4. **手动移动或配置 Obsidian**
   - 方式1：定期手动移动文件到 Obsidian vault
   - 方式2：配置 Obsidian 监听下载目录的 AIQ 文件夹

**缺点：**
- 需要额外操作
- 不够便捷

## 日志说明

导出时会在控制台输出以下日志：

```
[Background-Obsidian] 相对路径: AIQ/7.md
[Background-Obsidian] 提示: 文件将保存到浏览器默认下载目录的子文件夹中
[Background-Obsidian] 建议: 将浏览器默认下载目录设置为: D:\syncthing\obsidian-mark
```

**建议操作：**
根据日志中的建议路径，调整浏览器的默认下载目录。

## 故障排查

### 问题：文件保存位置不对

**原因：**
浏览器默认下载目录 ≠ 你配置的 Obsidian 路径的上级目录

**解决：**
1. 检查 Chrome 下载目录设置
2. 按照"方案 A"调整下载目录
3. 重新测试导出

### 问题：提示 "Invalid filename"

**原因：**
- 文件名包含非法字符：`/\:*?"<>|`
- 使用了绝对路径

**解决：**
- 代码已自动过滤特殊字符
- 如果仍有问题，检查是否修改了代码

### 问题：文件重复下载

**原因：**
`conflictAction: 'uniquify'` 会自动重命名

**行为：**
- 第一次：`filename.md`
- 第二次：`filename (1).md`
- 第三次：`filename (2).md`

## 技术限制说明

### 为什么不能直接保存到任意路径？

**Chrome 安全策略：**
1. 扩展不能直接写入文件系统的任意位置
2. 必须通过 `chrome.downloads.download` API
3. 该 API 只支持相对于下载目录的路径

**替代方案（未采用）：**
- ❌ File System Access API：需要用户每次手动选择位置
- ❌ Native Messaging：需要安装额外的本地程序
- ✅ **当前方案**：配置浏览器下载目录（最简单）

## 未来改进方向

### 可能的改进方案

1. **自动检测下载目录**
   - 使用 `chrome.downloads.search` 获取最近下载文件的路径
   - 推断出浏览器的默认下载目录
   - 提示用户是否需要调整

2. **配置向导**
   - 首次使用时显示配置引导
   - 检测当前下载目录
   - 提供一键调整选项

3. **路径验证**
   - 在配置导出路径时验证
   - 检查是否与浏览器下载目录匹配
   - 给出明确的配置建议

## 总结

**推荐配置流程：**

1. ✅ 打开 Chrome 设置 → 下载内容
2. ✅ 修改下载位置为 Obsidian vault 的上级目录
3. ✅ 在扩展中配置完整的导出路径
4. ✅ 测试导出功能
5. ✅ 验证文件是否保存到正确位置

**配置完成后：**
- 导出的 Markdown 文件会直接保存到 Obsidian vault
- Obsidian 会自动检测到新文件
- 无需手动移动或导入

**配置示例：**
```
Chrome 下载目录: D:\syncthing\obsidian-mark
扩展导出路径:    D:\syncthing\obsidian-mark\AIQ
实际保存位置:    D:\syncthing\obsidian-mark\AIQ\filename.md ✅
```
