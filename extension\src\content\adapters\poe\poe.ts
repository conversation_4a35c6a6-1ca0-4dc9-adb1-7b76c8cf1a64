import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { PoeAnswerController } from "./PoeAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * Poe 平台适配器
 * 继承 BaseAIAdapter，提供 Poe 平台的完整集成
 * 
 * 主要特性：
 * - 自动检测 Poe 页面类型（欢迎页/聊天页/机器人页）
 * - 支持Poe独特的问答对(Message Tuple)结构
 * - 使用HTML解析提取答案内容（无复制按钮）
 * - 监听答案完成标志（Message_messageMetadataContainer__nBPq7）
 * - 支持多种AI模型的答案提取
 * 
 * Poe平台特点：
 * - 问答对结构：每个问答对包含一个问题和一个答案
 * - 答案在左侧，问题在右侧
 * - 答案完成时显示时间元素
 * - 支持代码块、列表、引用等富文本格式
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new PoeAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class PoeAdapter extends BaseAIAdapter {
    private poeAnswerController: PoeAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【PoeAdapter】PoeAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.poeAnswerController = new PoeAnswerController();
        this.poeAnswerController.init(this);
        
        console.log('【PoeAdapter】PoeAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【PoeAdapter】PoeAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 PoeAnswerController
            if (this.poeAnswerController) {
                this.poeAnswerController.destroy();
                this.poeAnswerController = null;
                console.log('【PoeAdapter】PoeAnswerController 清理完成');
            }
            
            console.log('【PoeAdapter】PoeAdapter destroy 完成');
            
        } catch (error) {
            console.error('【PoeAdapter】PoeAdapter destroy 失败:', error);
        }
    }
}
