/**
 * Background消息处理模块
 * 负责处理来自content script和popup的消息
 */

import { chatHistoryService } from '@/common/service/ChatHistoryService'
import { chatPromptService } from '@/common/service/ChatPromptService'
import { platformService } from '@/common/service/PlatformService'
import { databaseViewService } from '@/common/service/DatabaseViewService'
import { StorageService } from '@/common/service/storage'
import { MessageType, ChromeMessage } from '@/common/types'
import { databaseConnectionManager } from './databaseConnection'
import { healthMonitor } from './healthMonitor'
import { NotionApiService } from './NotionApiService'

export class MessageHandler {
  /**
   * 处理消息的主要方法
   */
  static async handleMessage(
    message: ChromeMessage, 
    sender: chrome.runtime.MessageSender, 
    sendResponse: (response: any) => void
  ): Promise<void> {
    console.log('【Background】收到消息:', message.type)

    try {
      // 确保数据库连接就绪
      const isReady = await databaseConnectionManager.ensureConnection()
      if (!isReady) {
        throw new Error('Database connection not ready')
      }

      switch (message.type) {
        // 新建聊天提示词
        case MessageType.DB_CHAT_PROMPT_CREATE:
          const createResult = await chatPromptService.createPrompt(message.payload)
          sendResponse(createResult)
          break
        // 获得提示词列表
        case MessageType.DB_CHAT_PROMPT_LIST_GET:
          const uniqueResult = await chatPromptService.getChatPromptList(message.payload)
          sendResponse(uniqueResult)
          break

        case MessageType.DB_PLATFORM_GET_LIST:
          const platformListResult = await platformService.findAllActive()
          sendResponse(platformListResult)
          break

        // 聊天历史相关操作
        case MessageType.DB_CHAT_HISTORY_CREATE:
          const createHistoryResult = await chatHistoryService.createChatHistory(message.payload)
          sendResponse(createHistoryResult)
          break

        // 数据库查看相关操作
        case MessageType.DB_GET_ALL_TABLES:
          const tablesResult = await databaseViewService.getAllTables()
          sendResponse(tablesResult)
          break

        case MessageType.DB_GET_TABLE_DATA:
          const tableDataResult = await databaseViewService.getTableData(
            message.payload.tableName,
            message.payload.page,
            message.payload.limit
          )
          sendResponse(tableDataResult)
          break

        case MessageType.DB_GET_TABLE_COUNT:
          const tableCountResult = await databaseViewService.getTableCount(message.payload.tableName)
          sendResponse(tableCountResult)
          break

        case MessageType.DB_DELETE_RECORD:
          const recordDeleteResult = await databaseViewService.deleteRecord(
            message.payload.tableName,
            message.payload.recordId
          )
          sendResponse(recordDeleteResult)
          break

        case MessageType.DB_CLEAR_TABLE:
          const clearTableResult = await databaseViewService.clearTable(message.payload.tableName)
          sendResponse(clearTableResult)
          break
        case MessageType.UPDATE_SETTINGS:
          await StorageService.saveSettings(message.payload)
          sendResponse({ success: true })
          break

        // Favicon相关操作
        case MessageType.UPDATE_PLATFORM_FAVICON:
          const faviconResult = await platformService.updateFavicon(message.payload)
          sendResponse(faviconResult)
          break

        case MessageType.GET_SETTINGS:
          try {
            const settings = await StorageService.getSettings()
            sendResponse({ success: true, data: settings })
          } catch (error) {
            console.error('获取设置失败:', error)
            sendResponse({ success: false, error: '获取设置失败' })
          }
          break

        // Obsidian 导出 - 文件下载
        case MessageType.OBSIDIAN_DOWNLOAD_FILE:
          await MessageHandler.handleObsidianDownload(message.payload, sendResponse)
          break

        // Notion 导出 - 验证配置
        case MessageType.NOTION_VALIDATE_CONFIG:
          const validationResult = await NotionApiService.validateConfig(message.payload)
          sendResponse({ success: true, data: validationResult })
          break

        // Notion 导出 - 创建页面
        case MessageType.NOTION_CREATE_PAGE:
          try {
            const pageResult = await NotionApiService.createPage(
              message.payload.config,
              message.payload.request
            )
            sendResponse({ success: true, data: pageResult })
          } catch (error) {
            sendResponse({
              success: false,
              error: error instanceof Error ? error.message : '创建页面失败'
            })
          }
          break
          
        default:
          MessageHandler.handleSystemMessages(message, sendResponse)
      }
    } catch (error) {
      console.error('【Background】消息处理错误:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 处理系统消息
   */
  private static handleSystemMessages(message: any, sendResponse: (response: any) => void): void {
    switch (message.type) {
      case 'SW_PING':
        sendResponse({ success: true, timestamp: Date.now(), status: 'alive' })
        break
      case 'SW_WAKE_UP':
        console.log('【Background】Service Worker唤醒信号')
        sendResponse({ success: true, timestamp: Date.now(), status: 'awake' })
        break
      case 'SW_HEALTH_CHECK':
        healthMonitor.performHealthCheck().then(healthStatus => {
          sendResponse({ success: true, data: healthStatus })
        })
        break
      case 'SW_SYSTEM_STATUS':
        healthMonitor.getSystemStatus().then(systemStatus => {
          sendResponse({ success: true, data: systemStatus })
        })
        break
      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  }

  /**
   * 处理 Obsidian 文件下载
   */
  private static async handleObsidianDownload(
    payload: {
      content: string
      filename: string
      exportPath: string
    },
    sendResponse: (response: any) => void
  ): Promise<void> {
    try {
      console.log('[Background-Obsidian] 开始下载文件:', payload.filename)

      // 在 Service Worker 中不能使用 URL.createObjectURL
      // 改用 Data URL
      const base64Content = btoa(unescape(encodeURIComponent(payload.content)))
      const dataUrl = `data:text/markdown;charset=utf-8;base64,${base64Content}`

      // Chrome Downloads API 的 filename 参数限制：
      // 1. 不能使用绝对路径（如 D:\path\file.md）
      // 2. 可以使用相对路径（如 folder/file.md），相对于浏览器默认下载目录
      // 3. 路径分隔符必须使用正斜杠 /
      
      // 从完整路径中提取相对路径
      // 例如：D:\syncthing\obsidian-mark\AIQ => AIQ
      // 然后组合：AIQ/filename.md
      let relativePath = payload.filename
      
      if (payload.exportPath) {
        // 提取路径的最后一部分作为文件夹名
        const pathParts = payload.exportPath.replace(/[/\\]+$/, '').split(/[/\\]/)
        const folderName = pathParts[pathParts.length - 1]
        
        if (folderName) {
          // 使用正斜杠作为路径分隔符
          relativePath = `${folderName}/${payload.filename}`
        }
      }

      console.log('[Background-Obsidian] 相对路径:', relativePath)
      console.log('[Background-Obsidian] 提示: 文件将保存到浏览器默认下载目录的子文件夹中')
      console.log('[Background-Obsidian] 建议: 将浏览器默认下载目录设置为:', payload.exportPath.replace(/[/\\][^/\\]*$/, ''))

      // 使用 Chrome Downloads API 下载
      chrome.downloads.download(
        {
          url: dataUrl,
          filename: relativePath,
          saveAs: false,
          conflictAction: 'uniquify'
        },
        (downloadId) => {
          if (chrome.runtime.lastError) {
            console.error('[Background-Obsidian] 下载失败:', chrome.runtime.lastError)
            sendResponse({
              success: false,
              error: chrome.runtime.lastError.message
            })
          } else {
            console.log('[Background-Obsidian] 下载成功, ID:', downloadId)
            sendResponse({
              success: true,
              downloadId: downloadId
            })
          }
        }
      )
    } catch (error) {
      console.error('[Background-Obsidian] 下载异常:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

}
