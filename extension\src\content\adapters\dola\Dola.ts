import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { DolaAnswerController } from "./DolaAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * Dola 平台适配器
 * 继承 BaseAIAdapter，提供 Dola 平台的完整集成
 * 
 * 主要特性：
 * - 支持 dola.com 和 cici.com 两个域名
 * - 自动检测 Dola 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 使用操作栏 opacity 变化检测答案完成
 * - 优先使用复制按钮，自动降级到 DOM 提取
 * 
 * URL 模式：
 * - Home页: https://www.dola.com/chat/ 或 https://www.dola.com/chat/?from_login=xxx
 * - Chat页: https://www.dola.com/chat/{chatId}
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new DolaAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class Do<PERSON><PERSON>dapter extends BaseAIAdapter {
    private dolaAnswerController: DolaAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【DolaAdapter】DolaAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.dolaAnswerController = new DolaAnswerController();
        this.dolaAnswerController.init(this);
        
        console.log('【DolaAdapter】DolaAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【DolaAdapter】DolaAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 DolaAnswerController
            if (this.dolaAnswerController) {
                this.dolaAnswerController.destroy();
                this.dolaAnswerController = null;
                console.log('【DolaAdapter】DolaAnswerController 清理完成');
            }
            
            console.log('【DolaAdapter】DolaAdapter destroy 完成');
            
        } catch (error) {
            console.error('【DolaAdapter】DolaAdapter destroy 失败:', error);
        }
    }
}
