---
type: "development_rules"
description: "UI组件开发规则和规范"
---

# UI组件规范

## 组件分类
- **可复用**: `components/` - 基础UI组件
- **Content**: `content/components/` - 页面注入组件
- **React**: `popup/components/`, `options/components/` - React组件

## Content Script组件
### 必须实现
```typescript
class ComponentName {
  private element: HTMLElement | null = null;

  render(): HTMLElement { /* 渲染 */ }
  destroy(): void { /* 清理 */ }
}
```

### 核心方法
- `render()`: 渲染DOM元素
- `destroy()`: 清理资源
- 使用`echosync:`前缀事件

## React组件
### 基础结构
```typescript
interface Props {
  className?: string;
  children?: React.ReactNode;
}

const Component: React.FC<Props> = ({ className, children }) => (
  <div className={clsx('base-styles', className)}>
    {children}
  </div>
);
```

## 样式规范
- **前缀**: `echosync-`避免冲突
- **Content**: 内联样式 + 最高z-index
- **React**: Tailwind CSS
- **响应式**: 移动优先

## 性能要求
- 事件防抖/节流
- 及时清理资源
- 长列表虚拟化(>50项)