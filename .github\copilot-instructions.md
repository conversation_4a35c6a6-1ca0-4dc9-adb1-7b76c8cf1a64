# GitHub Copilot 项目指令索引

为提升可维护性，原有说明已拆分为多个文件：

- `copilot-project-overview.md`
- `copilot-architecture.md`
- `copilot-coding-style.md`
- `copilot-workflow.md`
- `copilot-response-guidelines.md`
- `copilot-knowledge-base.md`
- `UIAgentRules.md` ⭐ **UI 设计强制规范**

如需完整项目规范，请按主题查看对应文件。

---

## 🎨 UI 开发特别说明

**所有 UI 相关代码必须遵守 `UIAgentRules.md` 中的 Linear 现代极简风设计规范。**

核心要求：
- ✅ 使用 CSS 变量（色彩、间距、字体）
- ✅ 严格 8pt 网格系统
- ✅ 150-200ms 动画时长
- ❌ 禁止过度圆角（>16px）
