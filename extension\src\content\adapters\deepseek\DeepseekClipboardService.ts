import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

/**
 * Deepseek 剪贴板管理服务
 * 继承 BaseClipboardService，提供 Deepseek 平台特定的剪贴板操作
 * 针对 Deepseek 的特殊 DOM 结构优化 Markdown 转换
 */
export class DeepseekClipboardService extends BaseClipboardService {
    

    /**
     * 创建自定义的 TurndownService 实例
     * 专门处理 Deepseek 的代码块和表格结构
     */
    private createCustomTurndownService(): TurndownService {
        const turndownService = new TurndownService({
            headingStyle: 'atx',
            hr: '---',
            bulletListMarker: '-',
            codeBlockStyle: 'fenced',
            fence: '```',
            emDelimiter: '_',
            strongDelimiter: '**',
            linkStyle: 'inlined',
            linkReferenceStyle: 'full',
            preformattedCode: true
        });

        // 自定义规则1：处理 Deepseek 的整个代码块容器
        turndownService.addRule('deepseekCodeBlockContainer', {
            filter: (node: HTMLElement) => {
                return node.classList && node.classList.contains('md-code-block');
            },
            replacement: (content: string, node: HTMLElement) => {
                // 提取代码语言（从横幅区域）
                const langElement = node.querySelector('.d813de27');
                const language = langElement?.textContent?.trim() || '';
                
                // 提取 pre 元素中的代码
                const preElement = node.querySelector('pre');
                if (!preElement) {
                    return '\n\n```\n' + content + '\n```\n\n';
                }
                
                // 提取纯文本代码内容
                const codeText = this.extractCodeText(preElement);
                
                return '\n\n```' + language + '\n' + codeText + '\n```\n\n';
            }
        });

        // 自定义规则2：处理内联代码
        turndownService.addRule('deepseekInlineCode', {
            filter: (node: HTMLElement) => {
                return node.nodeName === 'CODE' && 
                       !node.closest('.md-code-block') &&
                       node.classList.contains('inline-code');
            },
            replacement: (content: string, node: HTMLElement) => {
                const text = node.textContent || '';
                return '`' + text + '`';
            }
        });

        // 自定义规则3：处理表格 - 优化单元格处理
        turndownService.addRule('deepseekTable', {
            filter: 'table',
            replacement: (content: string, node: HTMLElement) => {
                const table = node as HTMLTableElement;
                let markdown = '\n\n';
                
                // 处理表头
                const thead = table.querySelector('thead');
                if (thead) {
                    const headerRow = thead.querySelector('tr');
                    if (headerRow) {
                        const headers: string[] = [];
                        const cells = headerRow.querySelectorAll('th');
                        cells.forEach(cell => {
                            const text = this.cleanCellText(cell.textContent || '');
                            headers.push(text);
                        });
                        
                        if (headers.length > 0) {
                            // 表头行
                            markdown += '| ' + headers.join(' | ') + ' |\n';
                            // 分隔行
                            markdown += '| ' + headers.map(() => '---').join(' | ') + ' |\n';
                        }
                    }
                }
                
                // 处理表体
                const tbody = table.querySelector('tbody');
                if (tbody) {
                    const bodyRows = tbody.querySelectorAll('tr');
                    bodyRows.forEach(row => {
                        const cells: string[] = [];
                        const tds = row.querySelectorAll('td');
                        tds.forEach(cell => {
                            const text = this.cleanCellText(cell.textContent || '');
                            cells.push(text);
                        });
                        
                        if (cells.length > 0) {
                            markdown += '| ' + cells.join(' | ') + ' |\n';
                        }
                    });
                }
                
                markdown += '\n';
                return markdown;
            }
        });

        // 自定义规则4：处理列表项
        turndownService.addRule('deepseekListItem', {
            filter: 'li',
            replacement: (content: string, node: HTMLElement) => {
                content = content
                    .replace(/^\n+/, '') // 移除开头的换行
                    .replace(/\n+$/, '\n') // 保留结尾的单个换行
                    .replace(/\n/gm, '\n  '); // 为多行内容添加缩进
                
                const prefix = node.parentElement?.nodeName === 'OL' ? '1. ' : '- ';
                return prefix + content + (node.nextElementSibling ? '\n' : '');
            }
        });

        // 自定义规则5：处理段落，确保空行
        turndownService.addRule('deepseekParagraph', {
            filter: (node: HTMLElement) => {
                return node.classList && node.classList.contains('ds-markdown-paragraph');
            },
            replacement: (content: string) => {
                return content ? '\n\n' + content.trim() + '\n\n' : '';
            }
        });

        return turndownService;
    }

    /**
     * 清理表格单元格文本
     * @param text 原始文本
     * @returns 清理后的文本
     */
    private cleanCellText(text: string): string {
        return text
            .trim()
            .replace(/\n+/g, ' ')  // 移除换行符
            .replace(/\s+/g, ' ')  // 合并多个空格
            .replace(/\|/g, '\\|'); // 转义管道符
    }

    /**
     * 从代码块元素中提取纯文本代码
     * 专门处理 Deepseek 的 token 化代码结构
     * @param preElement pre 元素
     * @returns 纯文本代码
     */
    private extractCodeText(preElement: HTMLElement): string {
        // 首先尝试简单的 textContent 方法
        // 因为 Deepseek 的代码块已经在 DOM 中保留了正确的文本
        let code = preElement.textContent || '';
        
        // 清理代码内容
        code = code
            .replace(/^[\s\n]+/, '')  // 移除开头的空白
            .replace(/[\s\n]+$/, '')  // 移除结尾的空白
            .trim();
        
        // 如果通过 textContent 获取失败，使用递归方法
        if (!code || code.length === 0) {
            code = this.extractCodeTextRecursive(preElement);
        }
        
        return code;
    }

    /**
     * 递归提取代码文本（降级方法）
     * @param element 元素节点
     * @returns 提取的代码文本
     */
    private extractCodeTextRecursive(element: HTMLElement): string {
        const lines: string[] = [];
        
        const extractText = (node: Node): void => {
            if (node.nodeType === Node.TEXT_NODE) {
                const text = node.textContent || '';
                if (text) {
                    lines.push(text);
                }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                const el = node as HTMLElement;
                
                // 跳过 UI 元素
                if (el.classList.contains('ds-atom-button') || 
                    el.classList.contains('md-code-block-banner') ||
                    el.classList.contains('md-code-block-banner-wrap')) {
                    return;
                }
                
                // 处理换行
                if (el.nodeName === 'BR') {
                    lines.push('\n');
                    return;
                }
                
                // 递归处理子节点
                node.childNodes.forEach(child => extractText(child));
            }
        };
        
        extractText(element);
        
        return lines.join('').trim();
    }

    /**
     * 清理 HTML 内容,移除所有 UI 元素和控制元素
     * @param htmlContent 原始 HTML 内容
     * @returns 清理后的 HTML 内容
     */
    private cleanHtmlContent(htmlContent: string): string {
        // 创建临时 DOM 容器
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;
        
        // 要移除的选择器列表
        const selectorsToRemove = [
            // 代码块相关的 UI 元素
            '.md-code-block-banner',
            '.md-code-block-banner-wrap',
            '.md-code-block-banner-lite',
            '.efa13877',  // 复制/下载按钮容器
            '.d813de27',  // 语言标签（会在转换时从父元素提取）
            
            // 所有按钮
            'button',
            '.ds-atom-button',
            '.ds-icon-button',
            '.ds-text-button',
            
            // SVG 图标占位符
            '[data-placeholder="svg-icon"]',
            
            // 其他 UI 元素
            '.code-info-button-text'
        ];
        
        // 批量移除元素
        selectorsToRemove.forEach(selector => {
            const elements = tempDiv.querySelectorAll(selector);
            elements.forEach(element => {
                // 对于代码块内的语言标签，保留其文本但移除元素
                if (selector === '.d813de27' && element.closest('.md-code-block')) {
                    // 语言标签会在 createCustomTurndownService 中处理
                }
                element.remove();
            });
        });
        
        // 移除代码块内所有 SVG 相关元素
        const codeBlocks = tempDiv.querySelectorAll('.md-code-block');
        codeBlocks.forEach(block => {
            const icons = block.querySelectorAll('.ds-icon, .ds-icon-button__icon');
            icons.forEach(icon => icon.remove());
        });
        
        return tempDiv.innerHTML;
    }

    /**
     * 降级方法：从 DOM 提取 markdown 内容（可选覆盖）
     * 如果 Deepseek 的 DOM 结构特殊，可以覆盖此方法
     * @param answerElement 答案元素
     * @returns Markdown 格式的内容
     */
    protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
        try {
            console.info('[DeepseekClipboardService] 使用 Deepseek 优化的降级方法');
            
            const selectors = SelectorManager.getSelector();
            const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
            
            if (!markdownNode) {
                throw new Error('未找到 markdown 内容节点');
            }

            // 清理 HTML 内容,移除 UI 元素
            const cleanedHtml = this.cleanHtmlContent(markdownNode.innerHTML);
            
            // 使用自定义的 TurndownService
            const turndownService = this.createCustomTurndownService();
            
            // 转换 HTML 到 Markdown
            let markdown = turndownService.turndown(cleanedHtml);
            
            // 后处理：清理多余的空行和格式化
            markdown = this.postProcessMarkdown(markdown);
            
            console.info('[DeepseekClipboardService] Markdown 转换成功', markdown.substring(0, 200));
            
            return markdown;
            
        } catch (error) {
            console.error('[DeepseekClipboardService] 降级方法失败', error);
            return '';
        }
    }

    /**
     * Markdown 后处理
     * 清理和优化转换后的 Markdown 格式
     * 特别处理代码块、表格和列表的格式问题
     * @param markdown 原始 markdown
     * @returns 清理后的 markdown
     */
    private postProcessMarkdown(markdown: string): string {
        let result = markdown;
        
        // 1. 清理代码块前后的多余文本
        // 移除代码块前的 "复制"、"下载"、"运行" 等文本
        result = result.replace(/([复制下载运行]+)(\n)?```/g, '```');
        
        // 2. 确保代码块格式正确
        // 代码块前后应该有空行
        result = result.replace(/([^\n])\n```/g, '$1\n\n```');
        result = result.replace(/```\n([^\n])/g, '```\n\n$1');
        
        // 3. 清理表格格式
        // 移除表格行前后多余的空行
        result = result.replace(/\n{2,}\|/g, '\n\n|');
        result = result.replace(/\|\n{2,}/g, '|\n');
        
        // 4. 标题处理
        // 确保标题前后有空行
        result = result.replace(/([^\n])\n(#{1,6} )/g, '$1\n\n$2');
        result = result.replace(/(#{1,6} [^\n]+)\n([^\n#])/g, '$1\n\n$2');
        
        // 5. 列表处理
        // 确保列表前后有空行
        result = result.replace(/([^\n])\n([*\-+] )/g, '$1\n\n$2');
        result = result.replace(/([*\-+] [^\n]+)\n([^\n*\-+])/g, '$1\n\n$2');
        
        // 6. 移除过多的空行（但保留代码块内的换行）
        // 将3个或以上连续换行替换为2个
        result = result.replace(/\n{3,}/g, '\n\n');
        
        // 7. 清理段落间距
        // 确保段落之间有适当的空行
        result = result.replace(/([。！？.!?])\n([^\n])/g, '$1\n\n$2');
        
        // 8. 清理首尾空白
        result = result.trim();
        
        // 9. 最终检查：确保代码块闭合正确
        const codeBlockMatches = result.match(/```/g);
        if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
            console.warn('[DeepseekClipboardService] 检测到代码块未正确闭合');
        }
        
        return result;
    }
}
