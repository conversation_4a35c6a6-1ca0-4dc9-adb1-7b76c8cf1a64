<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FAB Demo - Linear 现代极简风</title>
  <style>
    /* === CSS 变量定义（基于 CSSDesignTokens.md） === */
    :root {
      /* 主色系（蓝色 - 交互/选中） */
      --primary: #3B82F6;
      --primary-dark: #2563EB;
      --primary-light: #60A5FA;
      --primary-50: #EFF6FF;
      --primary-100: #DBEAFE;
      
      /* 辅助色（紫色 - 强化/强调） */
      --purple: #8B5CF6;
      --purple-dark: #7C3AED;
      --purple-50: #F5F3FF;
      --purple-100: #EDE9FE;
      
      /* 中性色（基础 UI） */
      --text-primary: #111827;
      --text-secondary: #374151;
      --text-tertiary: #6B7280;
      
      --bg-base: #FAFBFC;
      --bg-elevated: #FFFFFF;
      --bg-subtle: #F9FAFB;
      
      --border-light: #E5E7EB;
      --border-medium: #D1D5DB;
      
      /* 间距系统（8pt Grid） */
      --space-1: 4px;
      --space-2: 8px;
      --space-3: 12px;
      --space-4: 16px;
      --space-6: 24px;
      --space-8: 32px;
      --space-12: 48px;
      --space-16: 64px;
      
      /* 字体系统 */
      --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      --font-xs: 12px;
      --font-sm: 13px;
      --font-base: 14px;
      --font-md: 16px;
      --font-lg: 20px;
      --font-xl: 24px;
      --font-semibold: 600;
      --font-medium: 500;
      
      /* 圆角系统 */
      --radius-sm: 6px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --radius-xl: 16px;
      --radius-full: 9999px;
      
      /* 阴影系统 */
      --shadow-xs: 0 1px 2px rgba(0,0,0,0.05);
      --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
      --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1);
      --shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
      --shadow-hover-blue: 
        0 0 0 1px var(--primary),
        0 12px 24px -8px rgba(59, 130, 246, 0.15),
        0 24px 48px -8px rgba(0, 0, 0, 0.08);
      
      /* 动效系统 */
      --duration-instant: 100ms;
      --duration-fast: 150ms;
      --duration-normal: 200ms;
      --duration-slow: 300ms;
      --easing: cubic-bezier(0.4, 0, 0.2, 1);
      --easing-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    /* === 基础样式 === */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: var(--font-family);
      background: var(--bg-base);
      color: var(--text-primary);
      line-height: 1.5;
      min-height: 100vh;
      padding: var(--space-8);
    }

    /* === 演示内容区域 === */
    .demo-content {
      max-width: 800px;
      margin: 0 auto;
      padding: var(--space-16) 0;
    }

    .demo-title {
      font-size: var(--font-xl);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--space-8);
      text-align: center;
    }

    .demo-description {
      font-size: var(--font-base);
      color: var(--text-secondary);
      text-align: center;
      margin-bottom: var(--space-16);
      line-height: 1.6;
    }

    /* === FAB 浮动按钮 === */
    .fab {
      position: fixed;
      bottom: var(--space-8);
      right: var(--space-8);
      width: 56px;
      height: 56px;
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      border: none;
      border-radius: var(--radius-full);
      color: white;
      font-size: 24px;
      cursor: pointer;
      box-shadow: var(--shadow-lg);
      transition: all var(--duration-fast) var(--easing-spring);
      z-index: 1000;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .fab:hover {
      transform: translateY(-2px) scale(1.05);
      box-shadow: var(--shadow-hover-blue);
    }

    .fab:active {
      transform: translateY(-1px) scale(0.98);
    }

    /* === 悬浮面板 === */
    .floating-panel {
      position: fixed;
      bottom: 80px;
      right: var(--space-8);
      width: 320px;
      background: var(--bg-elevated);
      border: 1.5px solid var(--border-light);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      opacity: 0;
      transform: translateY(20px) scale(0.95);
      transition: all var(--duration-normal) var(--easing-spring);
      pointer-events: none;
      z-index: 999;
      overflow: hidden;
    }

    .floating-panel.active {
      opacity: 1;
      transform: translateY(0) scale(1);
      pointer-events: auto;
    }

    /* Linear 特色：顶部蓝色边框 */
    .floating-panel::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary), var(--primary-dark));
    }

    /* === 面板头部 === */
    .panel-header {
      padding: var(--space-6) var(--space-6) var(--space-4);
      border-bottom: 1px solid var(--border-light);
    }

    .panel-title {
      font-size: var(--font-md);
      font-weight: var(--font-semibold);
      color: var(--text-primary);
      margin-bottom: var(--space-1);
    }

    .panel-subtitle {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
    }

    /* === 面板内容区域 === */
    .panel-content {
      position: relative;
      height: 300px;
      overflow: hidden;
    }

    .panel-section {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      padding: var(--space-4);
      opacity: 0;
      transform: translateX(100%);
      transition: all var(--duration-normal) var(--easing);
    }

    .panel-section.active {
      opacity: 1;
      transform: translateX(0);
    }

    .panel-section.prev {
      transform: translateX(-100%);
    }

    /* === 文字列表（第一段） === */
    .text-list {
      list-style: none;
    }

    .text-item {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all var(--duration-fast) var(--easing);
      margin-bottom: var(--space-2);
      border: 1px solid transparent;
    }

    .text-item:hover {
      background: var(--bg-subtle);
      border-color: var(--border-light);
      transform: translateY(-1px);
    }

    .text-item:active {
      transform: translateY(0) scale(0.98);
    }

    .text-item-title {
      font-size: var(--font-base);
      font-weight: var(--font-medium);
      color: var(--text-primary);
      margin-bottom: var(--space-1);
    }

    .text-item-desc {
      font-size: var(--font-sm);
      color: var(--text-tertiary);
    }

    /* === 编辑管理页（第二段） === */
    .edit-section {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
    }

    .back-button {
      align-self: flex-start;
      background: none;
      border: none;
      color: var(--primary);
      font-size: var(--font-sm);
      cursor: pointer;
      padding: var(--space-2);
      border-radius: var(--radius-sm);
      transition: all var(--duration-fast) var(--easing);
      display: flex;
      align-items: center;
      gap: var(--space-1);
    }

    .back-button:hover {
      background: var(--primary-50);
      transform: translateX(-2px);
    }

    .edit-form {
      display: flex;
      flex-direction: column;
      gap: var(--space-4);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
    }

    .form-label {
      font-size: var(--font-sm);
      font-weight: var(--font-medium);
      color: var(--text-secondary);
    }

    .form-input {
      padding: var(--space-3) var(--space-4);
      border: 1.5px solid var(--border-light);
      border-radius: var(--radius-md);
      font-size: var(--font-base);
      transition: all var(--duration-normal) var(--easing);
      background: var(--bg-elevated);
    }

    .form-input:hover {
      border-color: var(--border-medium);
    }

    .form-input:focus {
      outline: none;
      border-color: var(--primary);
      box-shadow: var(--shadow-focus);
      transform: scale(1.01);
    }

    .form-textarea {
      min-height: 80px;
      resize: vertical;
    }

    .action-buttons {
      display: flex;
      gap: var(--space-3);
      margin-top: var(--space-2);
    }

    .btn {
      padding: var(--space-3) var(--space-4);
      border-radius: var(--radius-md);
      font-size: var(--font-base);
      font-weight: var(--font-medium);
      cursor: pointer;
      transition: all var(--duration-fast) var(--easing);
      border: none;
      flex: 1;
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--primary), var(--primary-dark));
      color: white;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-focus);
    }

    .btn-primary:active {
      transform: translateY(0) scale(0.98);
    }

    .btn-secondary {
      background: var(--bg-elevated);
      color: var(--text-secondary);
      border: 1.5px solid var(--border-light);
    }

    .btn-secondary:hover {
      background: var(--bg-subtle);
      border-color: var(--border-medium);
      transform: translateY(-1px);
    }

    .btn-secondary:active {
      transform: translateY(0) scale(0.98);
    }

    /* === 响应式支持 === */
    @media (max-width: 768px) {
      .floating-panel {
        right: var(--space-4);
        left: var(--space-4);
        width: auto;
      }
      
      .fab {
        bottom: var(--space-6);
        right: var(--space-6);
      }
    }

    /* === 可访问性支持 === */
    @media (prefers-reduced-motion: reduce) {
      *,
      *::before,
      *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
  </style>
</head>
<body>
  <!-- 演示内容区域 -->
  <div class="demo-content">
    <h1 class="demo-title">FAB 浮动按钮演示</h1>
    <p class="demo-description">
      基于 Linear 现代极简风设计理念，演示浮动按钮和多段式悬浮页面交互。<br>
      点击右下角的浮动按钮体验微交互效果。
    </p>
  </div>

  <!-- FAB 浮动按钮 -->
  <button class="fab" id="fabButton">
    ✨
  </button>

  <!-- 悬浮面板 -->
  <div class="floating-panel" id="floatingPanel">
    <!-- 面板头部 -->
    <div class="panel-header">
      <div class="panel-title">快速操作</div>
      <div class="panel-subtitle">选择一个操作项目</div>
    </div>

    <!-- 面板内容 -->
    <div class="panel-content">
      <!-- 第一段：文字列表 -->
      <div class="panel-section active" id="listSection">
        <ul class="text-list">
          <li class="text-item" data-item="project">
            <div class="text-item-title">项目管理</div>
            <div class="text-item-desc">创建和管理您的项目</div>
          </li>
          <li class="text-item" data-item="task">
            <div class="text-item-title">任务管理</div>
            <div class="text-item-desc">添加和跟踪任务进度</div>
          </li>
          <li class="text-item" data-item="note">
            <div class="text-item-title">笔记管理</div>
            <div class="text-item-desc">记录重要信息和想法</div>
          </li>
          <li class="text-item" data-item="setting">
            <div class="text-item-title">系统设置</div>
            <div class="text-item-desc">配置应用偏好设置</div>
          </li>
        </ul>
      </div>

      <!-- 第二段：编辑管理页 -->
      <div class="panel-section" id="editSection">
        <div class="edit-section">
          <button class="back-button" id="backButton">
            ← 返回列表
          </button>
          
          <div class="edit-form">
            <div class="form-group">
              <label class="form-label" for="itemTitle">标题</label>
              <input type="text" class="form-input" id="itemTitle" placeholder="输入标题">
            </div>
            
            <div class="form-group">
              <label class="form-label" for="itemDesc">描述</label>
              <textarea class="form-input form-textarea" id="itemDesc" placeholder="输入描述信息"></textarea>
            </div>
            
            <div class="action-buttons">
              <button class="btn btn-secondary" id="cancelButton">取消</button>
              <button class="btn btn-primary" id="saveButton">保存</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // DOM 元素
    const fabButton = document.getElementById('fabButton');
    const floatingPanel = document.getElementById('floatingPanel');
    const listSection = document.getElementById('listSection');
    const editSection = document.getElementById('editSection');
    const backButton = document.getElementById('backButton');
    const textItems = document.querySelectorAll('.text-item');
    const itemTitle = document.getElementById('itemTitle');
    const itemDesc = document.getElementById('itemDesc');
    const cancelButton = document.getElementById('cancelButton');
    const saveButton = document.getElementById('saveButton');

    // 状态管理
    let isPanelOpen = false;
    let currentItem = null;

    // 项目数据
    const itemData = {
      project: { title: '项目管理', desc: '创建和管理您的项目' },
      task: { title: '任务管理', desc: '添加和跟踪任务进度' },
      note: { title: '笔记管理', desc: '记录重要信息和想法' },
      setting: { title: '系统设置', desc: '配置应用偏好设置' }
    };

    // FAB 按钮点击事件
    fabButton.addEventListener('click', () => {
      isPanelOpen = !isPanelOpen;
      floatingPanel.classList.toggle('active', isPanelOpen);
      
      // 如果关闭面板，重置到列表页
      if (!isPanelOpen) {
        showListSection();
      }
    });

    // 文字项点击事件
    textItems.forEach(item => {
      item.addEventListener('click', () => {
        const itemType = item.dataset.item;
        currentItem = itemType;
        showEditSection(itemType);
      });
    });

    // 返回按钮点击事件
    backButton.addEventListener('click', () => {
      showListSection();
    });

    // 取消按钮点击事件
    cancelButton.addEventListener('click', () => {
      showListSection();
    });

    // 保存按钮点击事件
    saveButton.addEventListener('click', () => {
      // 这里可以添加保存逻辑
      console.log('保存数据:', {
        type: currentItem,
        title: itemTitle.value,
        desc: itemDesc.value
      });
      
      // 显示保存成功反馈（简单示例）
      saveButton.textContent = '已保存';
      setTimeout(() => {
        saveButton.textContent = '保存';
        showListSection();
      }, 1000);
    });

    // 显示列表页
    function showListSection() {
      listSection.classList.add('active');
      listSection.classList.remove('prev');
      editSection.classList.remove('active');
      editSection.classList.add('prev');
      
      // 清空表单
      itemTitle.value = '';
      itemDesc.value = '';
      currentItem = null;
    }

    // 显示编辑页
    function showEditSection(itemType) {
      const data = itemData[itemType];
      
      // 填充表单数据
      itemTitle.value = data.title;
      itemDesc.value = data.desc;
      
      // 切换页面
      listSection.classList.remove('active');
      listSection.classList.add('prev');
      editSection.classList.add('active');
      editSection.classList.remove('prev');
    }

    // 点击面板外部关闭
    document.addEventListener('click', (e) => {
      if (isPanelOpen && 
          !floatingPanel.contains(e.target) && 
          !fabButton.contains(e.target)) {
        isPanelOpen = false;
        floatingPanel.classList.remove('active');
        showListSection();
      }
    });

    // 键盘支持
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && isPanelOpen) {
        isPanelOpen = false;
        floatingPanel.classList.remove('active');
        showListSection();
      }
    });
  </script>
</body>
</html>
