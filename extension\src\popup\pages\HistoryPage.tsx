import React, { useState } from 'react'
import { StatusIndicator } from '../components/button'

export function HistoryPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')

  const timeFilters = [
    { id: 'today', label: '今天' },
    { id: 'week', label: '本周' },
    { id: 'month', label: '本月' },
    { id: 'all', label: '全部' }
  ]

  // Mock conversation data
  const conversations = [
    {
      id: 1,
      title: '关于React Hooks的讨论',
      preview: '讨论了useState和useEffect的使用场景...',
      time: '2小时前',
      status: 'exported'
    },
    {
      id: 2,
      title: 'Python数据分析问题',
      preview: '如何使用pandas处理大型数据集...',
      time: '昨天',
      status: 'pending'
    },
    {
      id: 3,
      title: 'UI设计最佳实践',
      preview: '探讨了现代UI设计的原则和方法...',
      time: '3天前',
      status: 'exported'
    }
  ]

  const getStatusIndicator = (status: string) => {
    switch (status) {
      case 'exported':
        return <StatusIndicator type="success">已导出</StatusIndicator>
      case 'pending':
        return <StatusIndicator type="warning">待导出</StatusIndicator>
      default:
        return null
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 搜索与筛选 */}
      <div className="card fade-in">
        <div className="card-title">🔍 搜索与筛选</div>
        <div className="card-content">
          <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
            <input
              type="text"
              placeholder="搜索对话内容..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              style={{
                flex: 1,
                padding: '8px',
                border: '1px solid rgba(15, 23, 42, 0.12)',
                borderRadius: '8px',
                fontSize: '14px'
              }}
            />
            <button className="btn btn-primary btn-small">搜索</button>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            {timeFilters.map((filter) => (
              <button
                key={filter.id}
                className={`btn btn-secondary btn-small ${activeFilter === filter.id ? 'active' : ''}`}
                onClick={() => setActiveFilter(filter.id)}
                style={{
                  background: activeFilter === filter.id ? 'var(--blue-500)' : 'var(--card-bg)',
                  color: activeFilter === filter.id ? 'white' : 'var(--blue-600)'
                }}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 对话记录 */}
      <div className="card fade-in">
        <div className="card-title">💬 对话记录</div>
        <div className="card-content">
          {conversations.map((conversation, index) => (
            <div
              key={conversation.id}
              style={{
                borderBottom: index < conversations.length - 1 ? '1px solid rgba(15, 23, 42, 0.06)' : 'none',
                padding: '12px 0',
                marginBottom: index < conversations.length - 1 ? '12px' : '0'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <div style={{ fontWeight: 600 }}>{conversation.title}</div>
                <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>{conversation.time}</div>
              </div>
              <div style={{ fontSize: '12px', color: 'var(--text-muted)', marginBottom: '8px' }}>
                {conversation.preview}
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                {getStatusIndicator(conversation.status)}
                <button className="btn btn-secondary btn-small">查看</button>
                <button className="btn btn-secondary btn-small">导出</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
