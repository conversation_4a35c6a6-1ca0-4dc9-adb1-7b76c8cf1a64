import { TextUtils } from './TextUtils';

describe('TextUtils', () => {
    describe('truncateTitle', () => {
        it('应该返回短文本（不截断）', () => {
            const input = '短标题';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toBe('短标题');
        });

        it('应该截断长中文文本并添加省略号', () => {
            const input = '这是一个非常非常非常非常长的标题需要被截断';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toBe('这是一个非常非常非常非常长的标题...');
            expect(result.length).toBeLessThanOrEqual(23); // 20 + '...'
        });

        it('应该在英文词边界截断', () => {
            const input = 'How to use TypeScript with React and Redux';
            const result = TextUtils.truncateTitle(input, 20);
            // 应该在 'use' 或 'TypeScript' 之后截断，而不是在单词中间
            expect(result).toBe('How to use...');
            expect(result).not.toContain('TypeScri'); // 不应该在单词中间截断
        });

        it('应该处理中英文混合文本', () => {
            const input = '如何使用 TypeScript 开发 Chrome 扩展程序的完整指南';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toContain('...');
            expect(result.length).toBeLessThanOrEqual(23);
        });

        it('应该移除末尾的标点符号', () => {
            const input = '这是一个标题，包含逗号，需要截断，';
            const result = TextUtils.truncateTitle(input, 15);
            expect(result).not.toMatch(/[，,]\.\.\.$/); // 省略号前不应有标点
            expect(result).toContain('...');
        });

        it('应该处理空字符串', () => {
            const result = TextUtils.truncateTitle('', 20);
            expect(result).toBe('');
        });

        it('应该处理 null 和 undefined', () => {
            expect(TextUtils.truncateTitle(null as any, 20)).toBe('');
            expect(TextUtils.truncateTitle(undefined as any, 20)).toBe('');
        });

        it('应该移除首尾空白', () => {
            const input = '  标题有空格  ';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toBe('标题有空格');
        });

        it('应该使用默认长度40', () => {
            const input = '这是一个超过四十个字符的长标题文本内容需要被智能截断处理以便符合显示要求';
            const result = TextUtils.truncateTitle(input);
            expect(result.length).toBeLessThanOrEqual(43); // 40 + '...'
        });
    });

    describe('sanitizeFilename', () => {
        it('应该替换非法字符', () => {
            const input = '文件名:包含/非法\\字符?.txt';
            const result = TextUtils.sanitizeFilename(input);
            expect(result).toBe('文件名-包含-非法-字符-.txt');
            expect(result).not.toMatch(/[<>:"/\\|?*]/);
        });

        it('应该处理多个空格', () => {
            const input = '文件名   有   多个   空格';
            const result = TextUtils.sanitizeFilename(input);
            expect(result).toBe('文件名 有 多个 空格');
        });

        it('应该移除首尾空格', () => {
            const input = '  文件名  ';
            const result = TextUtils.sanitizeFilename(input);
            expect(result).toBe('文件名');
        });
    });

    describe('generateSafeFilename', () => {
        it('应该同时截断和清理文件名', () => {
            const input = '如何使用 TypeScript/React 开发 Chrome 扩展？这是一个非常长的标题';
            const result = TextUtils.generateSafeFilename(input, 20);
            expect(result).toContain('...');
            expect(result).not.toMatch(/[<>:"/\\|?*]/);
            expect(result.length).toBeLessThanOrEqual(23);
        });
    });

    describe('词边界检测', () => {
        it('应该正确识别中文边界', () => {
            const input = '中文标题不需要词边界检测因为每个字都是独立的';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toBe('中文标题不需要词边界检测因为每个字都是...');
        });

        it('应该在空格处截断英文', () => {
            const input = 'This is a very long title that needs truncation';
            const result = TextUtils.truncateTitle(input, 20);
            expect(result).toBe('This is a very long...');
            expect(result).not.toContain('titl'); // 不应在单词中间
        });

        it('应该处理没有空格的长英文单词', () => {
            const input = 'Supercalifragilisticexpialidocious';
            const result = TextUtils.truncateTitle(input, 20);
            // 整个都是一个单词，无法找到词边界，保持原截断
            expect(result.length).toBeLessThanOrEqual(23);
            expect(result).toContain('...');
        });
    });
});
