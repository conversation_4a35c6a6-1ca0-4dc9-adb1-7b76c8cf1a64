---
type: "agent_requested"
description: "Background模块开发规则"
---

# Background规则

## 核心职责
- **Service Worker**: 事件驱动后台服务
- **消息中心**: 处理跨模块通信
- **数据管理**: 统一数据库操作

## 目录结构
```
background/
├── index.ts              # 入口(≤20行)
├── messageHandler.ts     # 消息路由
├── databaseConnection.ts # 数据库连接
└── keepAlive.ts         # 保活机制
```

## 消息处理
```typescript
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 1. 验证格式 2. 路由服务 3. 返回结果
});
```

## 核心规则
- 消息类型: DB_*, CONFIG_*, STATUS_*
- 返回格式: DatabaseResult
- 延迟连接: 首次消息时初始化
- 错误处理: 统一格式和日志
