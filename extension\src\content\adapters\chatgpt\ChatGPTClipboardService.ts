import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

// 创建 Turndown 实例
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  hr: '---'
});

// 添加规则：处理 <code class="language-xx"> 的代码块
turndownService.addRule('fencedCodeSpanBlock', {
  filter: (node) => {
    // 只处理 <code> 节点，并且类名里含 language-xxx
    return (
      node.nodeName === 'CODE' &&
      /language-/.test(node.className)
    );
  },
  replacement: (content, node) => {
    // 使用 innerText 比 textContent 更好，能忽略 <span> 嵌套
    const code = node.innerText || node.textContent || '';
    const match = node.className.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

// ✅ 如果你的页面里也有 <pre><code> 组合，可以附加另一个规则兜底：
turndownService.addRule('preCodeBlock', {
  filter: (node) => node.nodeName === 'PRE' && node.querySelector('code'),
  replacement: (content, node) => {
    const codeNode = node.querySelector('code');
    const code = codeNode?.innerText || node.innerText || '';
    const match = codeNode?.className?.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

/**
 * ChatGPT 剪贴板管理服务
 * 继承 BaseClipboardService，提供 ChatGPT 平台特定的剪贴板操作
 * 
 * 主要特性：
 * - 使用父类的通用点击+剪贴板流程
 * - 自定义 Turndown 规则处理暗色主题代码块和 ChatGPT 特殊格式
 */
export class ChatGPTClipboardService extends BaseClipboardService {
    
      /**
       * 降级方法：直接从 DOM 提取内容（通用实现，子类可覆盖）
       * @param answerElement 答案元素
       * @returns Markdown 格式的内容
       */
      protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
        const selectors = SelectorManager.getSelector();
        const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
        
        if (!markdownNode) {
          throw new Error('未找到 markdown 内容节点');
        }
    
        // 使用 Turndown 转换 HTML 到 Markdown
        // const turndownService = new TurndownService({
        //   headingStyle: 'atx',
        //   codeBlockStyle: 'fenced',
        //   emDelimiter: '*'
        // });
        
        return turndownService.turndown(markdownNode.innerHTML);
      }
}
