import './ObsidianExportModal.css';
import { AnswerModel } from '../../model/AnswerModel';
import { TagSelector } from './TagSelector';
import { ObsidianExportService } from '../../service/ObsidianExportService';
import { TextUtils } from '../../utils/TextUtils';
import { markdownRenderer } from '../../utils/MarkdownRenderer';

// 注入必要的 CSS 样式
import 'highlight.js/styles/atom-one-dark.css';
import 'katex/dist/katex.min.css';

/**
 * Obsidian 导出 Modal 组件
 * 显示导出设置界面，收集用户输入
 */
export class ObsidianExportModal {
    private overlay: HTMLElement | null = null;
    private modal: HTMLElement | null = null;
    private answerIndex: number;
    private tagSelector: TagSelector | null = null;
    private titleInput: HTMLInputElement | null = null;
    private modalBody: HTMLElement | null = null;
    private scrollTopButton: HTMLButtonElement | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
    }

    /**
     * 创建 Modal 结构
     */
    private createModal(): void {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'obsidian-modal-overlay';
        this.overlay.addEventListener('click', this.handleOverlayClick.bind(this));

        // 创建 Modal 容器
        this.modal = document.createElement('div');
        this.modal.className = 'obsidian-modal';
        this.modal.addEventListener('click', (e) => e.stopPropagation());

        // 创建 Header
        const header = this.createHeader();
        this.modal.appendChild(header);

        // 创建 Body
        const body = this.createBody();
        this.modalBody = body;
        this.modal.appendChild(body);

        // 创建 Footer
        const footer = this.createFooter();
        this.modal.appendChild(footer);

        this.overlay.appendChild(this.modal);
        this.initializeScrollEnhancements();
    }

    /**
     * 创建 Header
     */
    private createHeader(): HTMLElement {
        const header = document.createElement('div');
        header.className = 'modal-header';

        // Logo 和标题
        const titleContainer = document.createElement('div');
        titleContainer.className = 'header-title';

        // 使用 Obsidian 官方风格 SVG Logo
        const logo = document.createElement('div');
        logo.className = 'obsidian-logo';
        logo.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <radialGradient id="obsidian-grad-b" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-48 -185 123 -32 179 429.7)">
                        <stop stop-color="#fff" stop-opacity=".4"/>
                        <stop offset="1" stop-opacity=".1"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(41 -310 229 30 341.6 351.3)">
                        <stop stop-color="#fff" stop-opacity=".6"/>
                        <stop offset="1" stop-color="#fff" stop-opacity=".1"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-d" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(57 -261 178 39 190.5 296.3)">
                        <stop stop-color="#fff" stop-opacity=".8"/>
                        <stop offset="1" stop-color="#fff" stop-opacity=".4"/>
                    </radialGradient>
                    <radialGradient id="obsidian-grad-e" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(-79 -133 153 -90 321.4 464.2)">
                        <stop stop-color="#fff" stop-opacity=".3"/>
                        <stop offset="1" stop-opacity=".3"/>
                    </radialGradient>
                </defs>
                <path d="M359.9 434.3c-2.6 19.1-21.3 34-40 28.9-26.4-7.3-57-18.7-84.7-20.8l-42.3-3.2a27.9 27.9 0 0 1-18-8.4l-73-75a27.9 27.9 0 0 1-5.4-31s45.1-99 46.8-104.2c1.7-5.1 7.8-50 11.4-74.2a28 28 0 0 1 9-16.6l86.2-77.5a28 28 0 0 1 40.6 3.5l72.5 92a29.7 29.7 0 0 1 6.2 18.3c0 17.4 1.5 53.2 11.1 76.3a303 303 0 0 0 35.6 58.5 14 14 0 0 1 1.1 15.7c-6.4 10.8-18.9 31.4-36.7 57.9a143.3 143.3 0 0 0-20.4 59.8Z" fill="#6C31E3"/>
                <path d="M182.7 436.4c33.9-68.7 33-118 18.5-153-13.2-32.4-37.9-52.8-57.3-65.5-.4 1.9-1 3.7-1.8 5.4L96.5 324.8a27.9 27.9 0 0 0 5.5 31l72.9 75c2.3 2.3 5 4.2 7.8 5.6Z" fill="url(#obsidian-grad-b)"/>
                <path d="M274.9 297c9.1.9 18 2.9 26.8 6.1 27.8 10.4 53.1 33.8 74 78.9 1.5-2.6 3-5.1 4.6-7.5a1222 1222 0 0 0 36.7-57.9 14 14 0 0 0-1-15.7 303 303 0 0 1-35.7-58.5c-9.6-23-11-58.9-11.1-76.3 0-6.6-2.1-13.1-6.2-18.3l-72.5-92-1.2-1.5c5.3 17.5 5 31.5 1.7 44.2-3 11.8-8.6 22.5-14.5 33.8-2 3.8-4 7.7-5.9 11.7a140 140 0 0 0-15.8 58c-1 24.2 3.9 54.5 20 95Z" fill="url(#obsidian-grad-c)"/>
                <path d="M274.8 297c-16.1-40.5-21-70.8-20-95 1-24 8-42 15.8-58l6-11.7c5.8-11.3 11.3-22 14.4-33.8a78.5 78.5 0 0 0-1.7-44.2 28 28 0 0 0-39.4-2l-86.2 77.5a28 28 0 0 0-9 16.6L144.2 216c0 .7-.2 1.3-.3 2 19.4 12.6 44 33 57.3 65.3 2.6 6.4 4.8 13.1 6.4 20.4a200 200 0 0 1 67.2-6.8Z" fill="url(#obsidian-grad-d)"/>
                <path d="M320 463.2c18.6 5.1 37.3-9.8 39.9-29a153 153 0 0 1 15.9-52.2c-21-45.1-46.3-68.5-74-78.9-29.5-11-61.6-7.3-94.2.6 7.3 33.1 3 76.4-24.8 132.7 3.1 1.6 6.6 2.5 10.1 2.8l43.9 3.3c23.8 1.7 59.3 14 83.2 20.7Z" fill="url(#obsidian-grad-e)"/>
            </svg>
        `;

        const title = document.createElement('h3');
        title.textContent = '导出到 Obsidian';

        titleContainer.appendChild(logo);
        titleContainer.appendChild(title);

        // 关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-btn';
        closeButton.textContent = '×';
        closeButton.setAttribute('aria-label', '关闭');
        closeButton.addEventListener('click', this.hide.bind(this));

        header.appendChild(titleContainer);
        header.appendChild(closeButton);

        return header;
    }

    /**
     * 创建 Body
     */
    private createBody(): HTMLElement {
        const body = document.createElement('div');
        body.className = 'modal-body';

        // 获取问答数据
        const qaPair = AnswerModel.getInstance().getQAPair(this.answerIndex);
        if (!qaPair) {
            console.error('[ObsidianExportModal] 无法获取问答数据，索引:', this.answerIndex);
            return body;
        }

        // 标题输入
        const titleGroup = this.createFormGroup(
            '标题',
            this.createTitleInput(qaPair.prompt)
        );
        body.appendChild(titleGroup);

        // Tag 选择器
        this.tagSelector = new TagSelector();
        const tagGroup = this.createFormGroup(
            '标签',
            this.tagSelector.render()
        );
        body.appendChild(tagGroup);

        // 元数据
        const metadataGroup = this.createFormGroup(
            '创建时间',
            this.createMetadata()
        );
        body.appendChild(metadataGroup);

        // 正文预览
        const contentGroup = this.createFormGroup(
            '正文',
            this.createContentPreview(qaPair.answer)
        );
        body.appendChild(contentGroup);

        return body;
    }

    /**
     * 创建表单组
     */
    private createFormGroup(label: string, content: HTMLElement): HTMLElement {
        const group = document.createElement('div');
        group.className = 'form-group';

        const labelElement = document.createElement('label');
        labelElement.className = 'form-label';
        labelElement.textContent = label;

        group.appendChild(labelElement);
        group.appendChild(content);

        return group;
    }

    /**
     * 创建标题输入框
     */
    private createTitleInput(defaultTitle: string): HTMLInputElement {
        this.titleInput = document.createElement('input');
        this.titleInput.type = 'text';
        this.titleInput.className = 'form-input';
        
        // 使用智能截断，使用默认长度（从 TextUtils 配置）
        const truncatedTitle = TextUtils.truncateTitle(defaultTitle);
        this.titleInput.value = truncatedTitle;
        this.titleInput.placeholder = '请输入标题';

        return this.titleInput;
    }

    /**
     * 创建内容预览
     */
    private createContentPreview(content: string): HTMLElement {
        const preview = document.createElement('div');
        preview.className = 'content-preview markdown-body';

        // 渲染完整 Markdown 内容（不再截断）
        const renderedHtml = markdownRenderer.render(content);
        preview.innerHTML = renderedHtml;

        return preview;
    }

    /**
     * 初始化滚动体验：隐藏双滚动条并提供回到顶部控制
     */
    private initializeScrollEnhancements(): void {
        if (!this.modal || !this.modalBody) {
            return;
        }

        if (!this.scrollTopButton) {
            this.scrollTopButton = document.createElement('button');
            this.scrollTopButton.type = 'button';
            this.scrollTopButton.className = 'modal-scroll-top';
            this.scrollTopButton.innerHTML = `
                <span class="scroll-top-icon">↑</span>
                <span class="scroll-top-text">回到顶部</span>
                <span class="scroll-top-hint">Alt + ↑</span>
            `;
            this.scrollTopButton.setAttribute('aria-label', '回到顶部 (Alt + ↑)');
            this.scrollTopButton.addEventListener('click', () => this.scrollToTop());
            this.modal.appendChild(this.scrollTopButton);
        }

        this.modalBody.addEventListener('scroll', this.handleBodyScroll);
        this.updateScrollTopVisibility();
    }

    /**
     * 滚动到内容顶部
     */
    private scrollToTop(): void {
        this.modalBody?.scrollTo({ top: 0, behavior: 'smooth' });
    }

    /**
     * 根据滚动位置更新按钮显示
     */
    private updateScrollTopVisibility(): void {
        if (!this.modalBody || !this.scrollTopButton) {
            return;
        }

        const shouldShow = this.modalBody.scrollTop > 160;
        this.scrollTopButton.classList.toggle('visible', shouldShow);
    }

    private handleBodyScroll = (): void => {
        this.updateScrollTopVisibility();
    };

    /**
     * 创建元数据显示
     */
    private createMetadata(): HTMLElement {
        const metadata = document.createElement('div');
        metadata.className = 'metadata';

        const now = new Date();
        const formattedTime = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        metadata.textContent = formattedTime;

        return metadata;
    }

    /**
     * 创建 Footer
     */
    private createFooter(): HTMLElement {
        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        const exportButton = document.createElement('button');
        exportButton.className = 'export-btn';
        exportButton.textContent = 'Add to Obsidian';
        exportButton.addEventListener('click', this.handleExport.bind(this));

        footer.appendChild(exportButton);

        return footer;
    }

    /**
     * 处理遮罩层点击
     */
    private handleOverlayClick(event: MouseEvent): void {
        if (event.target === this.overlay) {
            this.hide();
        }
    }

    /**
     * 处理导出
     */
    private async handleExport(): Promise<void> {
        try {
            // 获取表单数据
            const title = this.titleInput?.value.trim() || '';
            const tags = this.tagSelector?.getTags() || [];

            if (!title) {
                this.showToast('标题不能为空', 'warning');
                return;
            }

            // 禁用按钮，防止重复点击
            const exportButton = this.modal?.querySelector('.export-btn') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = true;
                exportButton.textContent = '导出中...';
            }

            // 调用导出服务
            await ObsidianExportService.getInstance().exportToObsidian({
                answerIndex: this.answerIndex,
                title,
                tags
            });

            // 成功提示
            this.showToast('✅ 导出成功！', 'success');

            // 延迟关闭 Modal
            setTimeout(() => {
                this.hide();
            }, 1000);

        } catch (error) {
            console.error('[ObsidianExportModal] 导出失败', error);
            
            // 恢复按钮状态
            const exportButton = this.modal?.querySelector('.export-btn') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = false;
                exportButton.textContent = 'Add to Obsidian';
            }
        }
    }

    /**
     * 显示 Toast 提示
     */
    private showToast(message: string, type: 'success' | 'warning' | 'error'): void {
        // 简单的 Toast 实现
        const toast = document.createElement('div');
        toast.className = `obsidian-toast obsidian-toast-${type}`;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 动画显示
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 3秒后自动关闭
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }

    /**
     * 显示 Modal
     */
    public show(): void {
        if (!this.overlay) {
            this.createModal();
        }

        if (this.overlay) {
            document.body.appendChild(this.overlay);

            // 添加动画
            requestAnimationFrame(() => {
                this.overlay?.classList.add('show');
            });

            // 绑定 ESC 键
            document.addEventListener('keydown', this.handleKeyDown);

            // 聚焦标题输入框
            setTimeout(() => {
                this.titleInput?.focus();
                this.titleInput?.select();
            }, 100);
        }
    }

    /**
     * 隐藏 Modal
     */
    public hide(): void {
        if (this.overlay) {
            this.overlay.classList.remove('show');

            document.removeEventListener('keydown', this.handleKeyDown);
            this.modalBody?.removeEventListener('scroll', this.handleBodyScroll);

            setTimeout(() => {
                this.overlay?.remove();
                this.overlay = null;
                this.modal = null;
                this.titleInput = null;
                this.modalBody = null;

                if (this.scrollTopButton) {
                    this.scrollTopButton.remove();
                    this.scrollTopButton = null;
                }

                // 销毁 TagSelector
                if (this.tagSelector) {
                    this.tagSelector.destroy();
                    this.tagSelector = null;
                }
            }, 300);
        }
    }

    /**
     * 处理键盘事件
     */
    private handleKeyDown = (event: KeyboardEvent): void => {
        if (event.key === 'Escape') {
            this.hide();
        }

        if (event.altKey && event.key === 'ArrowUp') {
            event.preventDefault();
            this.scrollToTop();
        }
    };
}
