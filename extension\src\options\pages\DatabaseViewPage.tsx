import React, { useEffect, useState } from 'react'
import { Database, RefreshCw, Table, Trash2 } from 'lucide-react'
import { useDatabaseStore } from '../stores/databaseStore'
import DataTable from '../components/DataTable'
import Pagination from '../components/Pagination'
import LoadingSpinner from '../components/LoadingSpinner'
import ErrorMessage from '../components/ErrorMessage'
import ConfirmDialog from '../components/ConfirmDialog'

/**
 * 数据库查看页面
 *
 * 提供数据库表的可视化查看功能，支持：
 * - 表列表展示和切换
 * - 表数据的分页展示
 * - 数据格式化和长文本处理
 * - 错误处理和加载状态
 *
 * @returns React组件
 */
export const DatabaseViewPage: React.FC = () => {
  const {
    tables,
    currentTable,
    tableData,
    pagination,
    loading,
    error,
    loadTables,
    selectTable,
    changePage,
    refreshData,
    deleteRecord,
    clearTable,
    clearError
  } = useDatabaseStore()

  console.log("tableData", tableData)
  // 清空表确认对话框状态
  const [clearConfirmDialog, setClearConfirmDialog] = useState<{
    isOpen: boolean
    position: { x: number; y: number }
  }>({
    isOpen: false,
    position: { x: 0, y: 0 }
  })

  // 页面初始化时加载表信息
  useEffect(() => {
    loadTables()
  }, [loadTables])

  // 处理清空表按钮点击
  const handleClearTableClick = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    setClearConfirmDialog({
      isOpen: true,
      position: {
        x: rect.left + rect.width / 2,
        y: rect.top
      }
    })
  }

  // 确认清空表
  const handleConfirmClearTable = () => {
    clearTable()
    setClearConfirmDialog({ isOpen: false, position: { x: 0, y: 0 } })
  }

  // 取消清空表
  const handleCancelClearTable = () => {
    setClearConfirmDialog({ isOpen: false, position: { x: 0, y: 0 } })
  }

  return (
    <div className="w-full p-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex items-center gap-3 mb-2">
          <Database className="w-6 h-6 text-blue-500" />
          <h1 className="text-2xl font-bold text-gray-900">数据库查看</h1>
        </div>
        <p className="text-gray-600">
          查看和浏览IndexedDB中的所有表数据，用于开发调试和数据管理。
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="mb-6">
          <ErrorMessage
            message={error}
            onRetry={() => {
              clearError()
              if (currentTable) {
                refreshData()
              } else {
                loadTables()
              }
            }}
            onDismiss={clearError}
          />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-6 xl:grid-cols-8 gap-6">
        {/* 左侧表列表 */}
        <div className="lg:col-span-1 xl:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
              <h2 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <Table className="w-4 h-4" />
                数据表
              </h2>
            </div>
            
            <div className="p-2">
              {loading && !currentTable ? (
                <div className="py-8">
                  <LoadingSpinner size="sm" text="加载表信息..." />
                </div>
              ) : (
                <div className="space-y-1">
                  {tables.map((table) => (
                    <button
                      key={table.name}
                      onClick={() => selectTable(table.name)}
                      className={`
                        w-full text-left px-3 py-2 rounded-md text-sm transition-colors
                        ${currentTable === table.name
                          ? 'bg-blue-100 text-blue-900 border border-blue-200'
                          : 'hover:bg-gray-100 text-gray-700'
                        }
                      `}
                    >
                      <div className="font-medium">{table.displayName}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {table.description}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 右侧数据展示区域 */}
        <div className="lg:col-span-5 xl:col-span-7">
          {!currentTable ? (
            <div className="bg-white rounded-lg border border-gray-200 p-12">
              <div className="text-center text-gray-500">
                <Database className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <h3 className="text-lg font-medium mb-2">选择数据表</h3>
                <p className="text-sm">
                  请从左侧选择一个数据表来查看其内容
                </p>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              {/* 表头工具栏 */}
              <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-medium text-gray-900">
                      {tables.find(t => t.name === currentTable)?.displayName || currentTable}
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      {tables.find(t => t.name === currentTable)?.description}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button
                      onClick={refreshData}
                      disabled={loading}
                      className="
                        inline-flex items-center gap-2 px-3 py-2
                        text-sm font-medium text-gray-700
                        bg-white border border-gray-300 rounded-md
                        hover:bg-gray-50 disabled:opacity-50
                        disabled:cursor-not-allowed transition-colors
                      "
                    >
                      <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                      刷新
                    </button>

                    <button
                      onClick={handleClearTableClick}
                      disabled={loading}
                      className="
                        inline-flex items-center gap-2 px-3 py-2
                        text-sm font-medium text-red-700
                        bg-red-50 border border-red-300 rounded-md
                        hover:bg-red-100 hover:border-red-400
                        disabled:opacity-50 disabled:cursor-not-allowed
                        transition-colors
                      "
                    >
                      <Trash2 className="w-4 h-4" />
                      删除所有
                    </button>
                  </div>
                </div>
              </div>

              {/* 数据表格 */}
              <DataTable
                data={tableData}
                loading={loading}
                tableName={currentTable}
                onDeleteRow={(rowData) => deleteRecord(rowData)}
              />

              {/* 分页组件 */}
              {!loading && tableData.length > 0 && (
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  total={pagination.total}
                  limit={pagination.limit}
                  onPageChange={changePage}
                />
              )}
            </div>
          )}
        </div>
      </div>

      {/* 清空表确认对话框 */}
      <ConfirmDialog
        isOpen={clearConfirmDialog.isOpen}
        title="确认清空表"
        message={`确定要清空表 "${tables.find(t => t.name === currentTable)?.displayName || currentTable}" 的所有数据吗？此操作将永久删除所有记录，不可撤销！`}
        confirmText="清空"
        cancelText="取消"
        onConfirm={handleConfirmClearTable}
        onCancel={handleCancelClearTable}
        position={clearConfirmDialog.position}
        variant="danger"
      />
    </div>
  )
}

export default DatabaseViewPage
