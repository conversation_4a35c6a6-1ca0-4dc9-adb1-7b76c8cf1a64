import './NotionExportModal.css';
import { AnswerModel } from '../../model/AnswerModel';
import { TagSelector } from './TagSelector';
import { notionExportService } from '../../service/NotionExportService';
import { TextUtils } from '../../utils/TextUtils';
import { markdownRenderer } from '../../utils/MarkdownRenderer';

// 注入必要的 CSS 样式
import 'highlight.js/styles/github.css';
import 'katex/dist/katex.min.css';

/**
 * Notion 导出 Modal 组件
 * 显示导出设置界面，收集用户输入
 */
export class NotionExportModal {
    private overlay: HTMLElement | null = null;
    private modal: HTMLElement | null = null;
    private answerIndex: number;
    private tagSelector: TagSelector | null = null;
    private titleInput: HTMLInputElement | null = null;
    private emojiInput: HTMLInputElement | null = null;
    private modalBody: HTMLElement | null = null;
    private scrollTopButton: HTMLButtonElement | null = null;

    constructor(answerIndex: number) {
        this.answerIndex = answerIndex;
    }

    /**
     * 创建 Modal 结构
     */
    private createModal(): void {
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'notion-export-modal-overlay';
        this.overlay.addEventListener('click', this.handleOverlayClick.bind(this));

        // 创建 Modal 容器
        this.modal = document.createElement('div');
        this.modal.className = 'notion-export-modal';
        this.modal.addEventListener('click', (e) => e.stopPropagation());

        // 创建 Header
        const header = this.createHeader();
        this.modal.appendChild(header);

        // 创建 Body
        const body = this.createBody();
        this.modalBody = body;
        this.modal.appendChild(body);

        // 创建 Footer
        const footer = this.createFooter();
        this.modal.appendChild(footer);

        this.overlay.appendChild(this.modal);
        this.initializeScrollEnhancements();
    }

    /**
     * 创建 Header
     */
    private createHeader(): HTMLElement {
        const header = document.createElement('div');
        header.className = 'modal-header';

        // Logo 和标题
        const titleContainer = document.createElement('div');
        titleContainer.className = 'header-title';

        // 使用 Notion Logo（内联 SVG）
        const logo = document.createElement('div');
        logo.className = 'notion-logo';
        logo.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 59.9 62.6" xmlns="http://www.w3.org/2000/svg">
                <style type="text/css">
                    .st0{fill:#000000;}
                    .st1{fill:#FFFFFF;fill-rule:evenodd;clip-rule:evenodd;}
                </style>
                <g>
                    <g>
                        <path class="st0" d="M3.8,2.7l34.6-2.6c4.2-0.4,5.3-0.1,8,1.8l11.1,7.8c1.8,1.3,2.4,1.7,2.4,3.2v42.7c0,2.7-1,4.3-4.4,4.5
                            l-40.2,2.4c-2.6,0.1-3.8-0.2-5.1-1.9L2.1,50.1c-1.5-2-2.1-3.4-2.1-5.1V7C0,4.8,1,2.9,3.8,2.7L3.8,2.7z M3.8,2.7"/>
                        <path class="st1" d="M38.4,0.1L3.8,2.7C1,2.9,0,4.8,0,7v38c0,1.7,0.6,3.2,2.1,5.1l8.1,10.6c1.3,1.7,2.6,2.1,5.1,1.9l40.2-2.4
                            c3.4-0.2,4.4-1.8,4.4-4.5V12.9c0-1.4-0.5-1.8-2.2-3c-0.1-0.1-0.2-0.1-0.3-0.2L46.4,2C43.8,0,42.7-0.2,38.4,0.1L38.4,0.1z
                            M16.2,12.2c-3.3,0.2-4,0.3-5.9-1.3L5.6,7.2C5.1,6.7,5.3,6.1,6.6,6l33.3-2.4c2.8-0.2,4.2,0.7,5.3,1.6l5.7,4.1
                            c0.3,0.1,0.9,0.8,0.1,0.8l-34.4,2.1L16.2,12.2z M12.4,55.3V19c0-1.6,0.5-2.3,1.9-2.4l39.5-2.3c1.3-0.1,1.9,0.7,1.9,2.3v36
                            c0,1.6-0.3,2.9-2.4,3l-37.8,2.2C13.4,58,12.4,57.2,12.4,55.3L12.4,55.3z M49.7,21c0.2,1.1,0,2.2-1.1,2.3l-1.8,0.4v26.8
                            c-1.6,0.9-3,1.3-4.2,1.3c-1.9,0-2.4-0.6-3.9-2.4L26.7,30.6v18.1l3.8,0.9c0,0,0,2.2-3,2.2l-8.4,0.5c-0.2-0.5,0-1.7,0.8-1.9l2.2-0.6
                            v-24l-3-0.3c-0.2-1.1,0.4-2.7,2.1-2.8l9-0.6l12.4,19V24.3l-3.2-0.4c-0.2-1.3,0.7-2.3,1.9-2.4L49.7,21z M49.7,21"/>
                    </g>
                </g>
            </svg>
        `;

        const title = document.createElement('h3');
        title.textContent = '导出到 Notion';

        titleContainer.appendChild(logo);
        titleContainer.appendChild(title);

        // 关闭按钮
        const closeButton = document.createElement('button');
        closeButton.className = 'close-btn';
        closeButton.textContent = '×';
        closeButton.setAttribute('aria-label', '关闭');
        closeButton.addEventListener('click', this.hide.bind(this));

        header.appendChild(titleContainer);
        header.appendChild(closeButton);

        return header;
    }

    /**
     * 创建 Body
     */
    private createBody(): HTMLElement {
        const body = document.createElement('div');
        body.className = 'modal-body';

        // 获取问答数据
        const qaPair = AnswerModel.getInstance().getQAPair(this.answerIndex);
        if (!qaPair) {
            console.error('[NotionExportModal] 无法获取问答数据，索引:', this.answerIndex);
            return body;
        }

        // // Emoji 图标输入（可选）
        // const emojiGroup = this.createFormGroup(
        //     '图标 (可选)',
        //     this.createEmojiInput()
        // );
        // body.appendChild(emojiGroup);

        // 标题输入
        const titleGroup = this.createFormGroup(
            '标题 *',
            this.createTitleInput(qaPair.prompt)
        );
        body.appendChild(titleGroup);

        // Tag 选择器
        this.tagSelector = new TagSelector();
        const tagGroup = this.createFormGroup(
            '标签',
            this.tagSelector.render()
        );
        body.appendChild(tagGroup);

        // 正文预览
        const contentGroup = this.createFormGroup(
            '正文预览',
            this.createContentPreview(qaPair.answer)
        );
        body.appendChild(contentGroup);

        return body;
    }

    /**
     * 创建表单组
     */
    private createFormGroup(label: string, content: HTMLElement): HTMLElement {
        const group = document.createElement('div');
        group.className = 'form-group';

        const labelElement = document.createElement('label');
        labelElement.className = 'form-label';
        labelElement.textContent = label;

        group.appendChild(labelElement);
        group.appendChild(content);

        return group;
    }

    /**
     * 创建 Emoji 输入框
     */
    private createEmojiInput(): HTMLInputElement {
        this.emojiInput = document.createElement('input');
        this.emojiInput.type = 'text';
        this.emojiInput.className = 'form-input emoji-input';
        this.emojiInput.placeholder = '输入一个 Emoji，如 📝';
        this.emojiInput.maxLength = 2; // 限制长度

        return this.emojiInput;
    }

    /**
     * 创建标题输入框
     */
    private createTitleInput(defaultTitle: string): HTMLInputElement {
        this.titleInput = document.createElement('input');
        this.titleInput.type = 'text';
        this.titleInput.className = 'form-input';
        
        // 使用智能截断，使用默认长度（从 TextUtils 配置）
        const truncatedTitle = TextUtils.truncateTitle(defaultTitle);
        this.titleInput.value = truncatedTitle;
        this.titleInput.placeholder = '请输入标题';

        return this.titleInput;
    }

    /**
     * 创建内容预览
     */
    private createContentPreview(content: string): HTMLElement {
        const preview = document.createElement('div');
        preview.className = 'content-preview markdown-body';

        // 渲染完整 Markdown 内容（不再截断）
        const renderedHtml = markdownRenderer.render(content);
        preview.innerHTML = renderedHtml;

        return preview;
    }

    /**
     * 初始化滚动体验，消除内层滚动条并提供快捷返回顶部
     */
    private initializeScrollEnhancements(): void {
        if (!this.modal || !this.modalBody) {
            return;
        }

        if (!this.scrollTopButton) {
            this.scrollTopButton = document.createElement('button');
            this.scrollTopButton.type = 'button';
            this.scrollTopButton.className = 'modal-scroll-top';
            this.scrollTopButton.innerHTML = `
                <span class="scroll-top-icon">↑</span>
                <span class="scroll-top-text">回到顶部</span>
                <span class="scroll-top-hint">Alt + ↑</span>
            `;
            this.scrollTopButton.setAttribute('aria-label', '回到顶部 (Alt + ↑)');
            this.scrollTopButton.addEventListener('click', () => this.scrollToTop());
            this.modal.appendChild(this.scrollTopButton);
        }

        this.modalBody.addEventListener('scroll', this.handleBodyScroll);
        this.updateScrollTopVisibility();
    }

    private scrollToTop(): void {
        this.modalBody?.scrollTo({ top: 0, behavior: 'smooth' });
    }

    private updateScrollTopVisibility(): void {
        if (!this.modalBody || !this.scrollTopButton) {
            return;
        }

        const shouldShow = this.modalBody.scrollTop > 160;
        this.scrollTopButton.classList.toggle('visible', shouldShow);
    }

    private handleBodyScroll = (): void => {
        this.updateScrollTopVisibility();
    };

    /**
     * 创建 Footer
     */
    private createFooter(): HTMLElement {
        const footer = document.createElement('div');
        footer.className = 'modal-footer';

        // 取消按钮
        const cancelButton = document.createElement('button');
        cancelButton.className = 'btn-secondary';
        cancelButton.textContent = '取消';
        cancelButton.addEventListener('click', this.hide.bind(this));

        // 导出按钮
        const exportButton = document.createElement('button');
        exportButton.className = 'btn-primary';
        exportButton.textContent = '导出到 Notion';
        exportButton.addEventListener('click', this.handleExport.bind(this));

        footer.appendChild(cancelButton);
        footer.appendChild(exportButton);

        return footer;
    }

    /**
     * 处理导出
     */
    private async handleExport(): Promise<void> {
        const title = this.titleInput?.value.trim();
        const tags = this.tagSelector?.getTags() || [];
        const emoji = this.emojiInput?.value.trim();

        if (!title) {
            this.showError('请输入标题');
            return;
        }

        try {
            // 禁用按钮，显示加载状态
            const exportButton = this.modal?.querySelector('.btn-primary') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = true;
                exportButton.textContent = '导出中...';
            }

            // 调用导出服务
            await notionExportService.exportToNotion({
                answerIndex: this.answerIndex,
                title,
                tags,
                icon: emoji || undefined
            });

            // 导出成功，关闭 Modal
            this.hide();

        } catch (error) {
            console.error('[NotionExportModal] 导出失败', error);
            this.showError(error instanceof Error ? error.message : '导出失败');
            
            // 恢复按钮状态
            const exportButton = this.modal?.querySelector('.btn-primary') as HTMLButtonElement;
            if (exportButton) {
                exportButton.disabled = false;
                exportButton.textContent = '导出到 Notion';
            }
        }
    }

    /**
     * 显示错误信息
     */
    private showError(message: string): void {
        // 简单的 alert，后续可以改为更优雅的提示
        alert(message);
    }

    /**
     * 处理遮罩层点击
     */
    private handleOverlayClick(event: MouseEvent): void {
        if (event.target === this.overlay) {
            this.hide();
        }
    }

    /**
     * 显示 Modal
     */
    public show(): void {
        if (!this.overlay) {
            this.createModal();
        }

        if (this.overlay) {
            document.body.appendChild(this.overlay);
            // 触发动画
            setTimeout(() => {
                this.overlay?.classList.add('show');
            }, 10);

            document.addEventListener('keydown', this.handleKeyDown);
        }
    }

    /**
     * 隐藏 Modal
     */
    public hide(): void {
        if (this.overlay) {
            this.overlay.classList.remove('show');
            document.removeEventListener('keydown', this.handleKeyDown);
            this.modalBody?.removeEventListener('scroll', this.handleBodyScroll);
            setTimeout(() => {
                this.overlay?.remove();
                this.overlay = null;
                this.modal = null;
                this.modalBody = null;
                if (this.scrollTopButton) {
                    this.scrollTopButton.remove();
                    this.scrollTopButton = null;
                }
                
                // 清理 TagSelector
                if (this.tagSelector) {
                    this.tagSelector.destroy();
                    this.tagSelector = null;
                }
            }, 300);
        }
    }

    private handleKeyDown = (event: KeyboardEvent): void => {
        if (event.key === 'Escape') {
            this.hide();
            return;
        }

        if (event.altKey && event.key === 'ArrowUp') {
            event.preventDefault();
            this.scrollToTop();
        }
    };
}
