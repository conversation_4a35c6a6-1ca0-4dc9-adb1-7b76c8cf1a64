import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { MonicaClipboardService } from "./MonicaClipboardService";

/**
 * Monica 答案管理服务
 * 继承 BaseAnswerService，提供 Monica 平台特定的答案处理逻辑
 * 
 * 主要特性：
 * - 监听 DOM 变化检测答案更新
 * - 使用 MonicaClipboardService 直接转换 HTML 到 Markdown
 * - 依赖父类 BaseAnswerService 处理答案完成信号
 * 
 * 使用示例：
 * ```typescript
 * const answerService = new MonicaAnswerService();
 * answerService.init(adapter);
 * ```
 */
export class MonicaAnswerService extends BaseAnswerService {
    private clipboardService: MonicaClipboardService;

    constructor() {
        super();
        this.clipboardService = new MonicaClipboardService();
    }

    /**
     * 获取剪贴板服务实例（BaseAnswerService 抽象方法实现）
     * @returns MonicaClipboardService 实例
     */
    protected getClipboardService(): MonicaClipboardService {
        return this.clipboardService;
    }
}
