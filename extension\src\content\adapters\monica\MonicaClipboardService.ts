import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

// 创建 Turndown 实例，用于 HTML 转 Markdown
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  hr: '---'
});

// 添加规则：处理 Monica 的代码块格式
// Monica 使用 <code class="hljs language-xx"> 格式
turndownService.addRule('monicaCodeBlock', {
  filter: (node) => {
    // 处理带有 hljs 和 language- 类名的 <code> 节点
    return (
      node.nodeName === 'CODE' &&
      /hljs/.test(node.className) &&
      /language-/.test(node.className)
    );
  },
  replacement: (content, node) => {
    // 使用 innerText 获取代码内容，避免嵌套标签干扰
    const code = node.innerText || node.textContent || '';
    const match = node.className.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

// 处理 <pre> 包裹的代码块
turndownService.addRule('monicaPreCodeBlock', {
  filter: (node) => node.nodeName === 'PRE' && node.querySelector('code'),
  replacement: (content, node) => {
    const codeNode = node.querySelector('code');
    const code = codeNode?.innerText || node.innerText || '';
    const match = codeNode?.className?.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

/**
 * Monica 剪贴板管理服务
 * 继承 BaseClipboardService，提供 Monica 平台特定的剪贴板操作
 * 
 * 主要特性：
 * - Monica 平台支持复制按钮功能，使用父类的通用实现
 * - 自定义 Turndown 规则处理 Monica 的代码块格式
 * - 降级方案：当复制按钮不可用时，使用 HTML 转 Markdown
 * 
 * 使用示例：
 * ```typescript
 * const clipboardService = new MonicaClipboardService();
 * const content = await clipboardService.getClipboardContent(answerElement);
 * ```
 */
export class MonicaClipboardService extends BaseClipboardService {
    
    /**
     * 注释掉此方法，使用父类的复制按钮实现
     * Monica 平台支持复制按钮，优先使用复制按钮，失败时降级到 HTML 转 Markdown
     */
    // public async getClipboardContent(answerElement: Element): Promise<string> {
    //     console.log('【MonicaClipboardService】Monica 平台直接使用 HTML 转 Markdown');
    //     
    //     try {
    //         // 直接调用降级方法
    //         return await this.getFallbackClipboardContent(answerElement);
    //     } catch (error) {
    //         console.error('【MonicaClipboardService】获取剪贴板内容失败:', error);
    //         throw error;
    //     }
    // }

    /**
     * 降级方法：直接从 DOM 提取内容并转换为 Markdown
     * 当复制按钮不可用时，父类会自动调用此方法
     * 
     * @param answerElement 答案元素
     * @returns Markdown 格式的内容
     */
    protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
        const selectors = SelectorManager.getSelector();
        const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
        
        if (!markdownNode) {
            console.warn('【MonicaClipboardService】未找到 markdown 节点，尝试使用答案元素本身');
            // 如果没找到 markdown 节点，尝试使用答案元素本身
            return turndownService.turndown(answerElement.innerHTML);
        }
    
        // 使用 Turndown 转换 HTML 到 Markdown
        const markdown = turndownService.turndown(markdownNode.innerHTML);
        console.log('【MonicaClipboardService】HTML 转 Markdown 完成，长度:', markdown.length);
        
        return markdown;
    }
}
