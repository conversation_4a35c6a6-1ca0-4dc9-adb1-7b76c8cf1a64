import { BaseClipboardService } from "@/content/core/BaseClipboardService";
import TurndownService from 'turndown';
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";

// 创建 Turndown 实例，配置 Dola 平台特定的 Markdown 转换规则
const turndownService = new TurndownService({
  headingStyle: 'atx',
  codeBlockStyle: 'fenced',
  emDelimiter: '*',
  hr: '---'
});

// 处理 Dola 的代码块格式
turndownService.addRule('fencedCodeSpanBlock', {
  filter: (node) => {
    // 处理 <code> 节点，类名含 language-xxx
    return (
      node.nodeName === 'CODE' &&
      /language-/.test(node.className)
    );
  },
  replacement: (content, node) => {
    const code = node.innerText || node.textContent || '';
    const match = node.className.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

// 兜底规则：处理 <pre><code> 组合
turndownService.addRule('preCodeBlock', {
  filter: (node) => node.nodeName === 'PRE' && node.querySelector('code'),
  replacement: (content, node) => {
    const codeNode = node.querySelector('code');
    const code = codeNode?.innerText || node.innerText || '';
    const match = codeNode?.className?.match(/language-([\w-]+)/);
    const lang = match ? match[1] : '';
    return `\n\`\`\`${lang}\n${code.trim()}\n\`\`\`\n`;
  }
});

/**
 * Dola 剪贴板管理服务
 * 继承 BaseClipboardService，提供 Dola 平台特定的剪贴板操作
 * 
 * 主要特性：
 * - 使用父类的通用点击+剪贴板流程
 * - 自定义 Turndown 规则处理 Dola 特殊格式
 * - 支持 flow-markdown-body 和 mdbox-theme-next 样式
 */
export class DolaClipboardService extends BaseClipboardService {
    
  /**
   * 模拟点击并获取内容（Dola 特殊实现）
   * Dola 平台不需要点击复制按钮，直接从 DOM 提取内容
   * 
   * @param answerElement 答案元素
   * @param copyButton 复制按钮（Dola 平台可选，不使用）
   * @returns 内容字符串
   */
  public async simulateClickAndGetContent(
    answerElement: Element, 
    copyButton?: Element | null
  ): Promise<string> {
    try {
      // Dola 平台不需要点击复制按钮，直接使用 DOM 提取
      console.info('[DolaClipboardService] 使用 DOM 提取方案（不依赖复制按钮）');
      const fallbackContent = await this.getFallbackClipboardContent(answerElement);
      
      if (fallbackContent && fallbackContent.trim()) {
        console.info('[DolaClipboardService] ✅ DOM 提取成功，内容长度:', fallbackContent.length);
      } else {
        console.warn('[DolaClipboardService] ⚠️ DOM 提取未获取到内容');
      }
      return fallbackContent;
    } catch (error) {
      console.error('[DolaClipboardService] 内容提取失败', error);
      return ""
    }
  }

  /**
   * 降级方法：直接从 DOM 提取内容
   * 
   * @param answerElement 答案元素
   * @returns Markdown 格式的内容
   * 
   * @throws {Error} 如果未找到 markdown 内容节点
   */
  protected async getFallbackClipboardContent(answerElement: Element): Promise<string> {
    const selectors = SelectorManager.getSelector();
    const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
    
    if (!markdownNode) {
      throw new Error('未找到 markdown 内容节点');
    }

    // 使用 Turndown 转换 HTML 到 Markdown
    return turndownService.turndown(markdownNode.innerHTML);
  }

  /**
   * 从答案元素中提取纯文本内容（备用方法）
   * 
   * @param answerElement 答案元素
   * @returns 纯文本内容
   */
  protected getTextContent(answerElement: Element): string {
    const selectors = SelectorManager.getSelector();
    const markdownNode = DOMUtils.findElementInContainer(answerElement, selectors.markdown);
    
    if (markdownNode) {
      return markdownNode.textContent?.trim() || '';
    }
    
    return answerElement.textContent?.trim() || '';
  }
}
