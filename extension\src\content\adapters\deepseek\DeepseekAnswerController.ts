import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { BasePageService } from "@/content/core/BasePageService";
import { DeepseekAnswerService } from "./DeepseekAnswerService";
import { DeepseekPageService } from "./DeepseekPageService";

/**
 * Deepseek 平台答案捕获控制器
 * 继承 BaseAnswerController，提供 Deepseek 平台特定的服务实例
 * 大部分流程编排逻辑已在基类实现，无需额外代码
 */
export class DeepseekAnswerController extends BaseAnswerController {
    /**
     * 创建 Deepseek 平台的服务实例
     */
    protected createServices(): {
        answerService: BaseAnswerService;
        pageService: BasePageService;
    } {
        return {
            answerService: new DeepseekAnswerService(),
            pageService: new DeepseekPageService()
        };
    }

    // 如果 Deepseek 有特殊的初始化逻辑，可以覆盖以下方法：
    
    /**
     * 检查页面类型并初始化服务（可选覆盖）
     * 如果需要特殊的页面检测或初始化逻辑
     */
    // protected checkPageTypeAndInitServices(): void {
    //     // 自定义实现
    //     super.checkPageTypeAndInitServices();
    // }

    /**
     * 启动聊天监听器（可选覆盖）
     * 如果 Deepseek 的聊天列表选择器特殊
     */
    // protected startChatListeners(): void {
    //     // 自定义实现
    //     super.startChatListeners();
    // }
}
