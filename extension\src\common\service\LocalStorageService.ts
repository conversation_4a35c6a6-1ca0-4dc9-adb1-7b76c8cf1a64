import { Singleton } from '../base';

/**
 * Notion 配置接口
 */
export interface NotionConfig {
    token: string;          // Integration Token
    pageId: string;         // Parent Page ID (32位)
    pageName: string;       // Page 名称（显示用）
    lastVerified?: number;  // 最后验证时间戳
}

/**
 * LocalStorage 服务
 * 管理不需要跨设备同步的本地数据（如 Notion Token）
 */
export class LocalStorageService extends Singleton<LocalStorageService> {
    private static readonly NOTION_CONFIG_KEY = 'notion_config';

    /**
     * 保存 Notion 配置
     */
    public async saveNotionConfig(config: NotionConfig): Promise<void> {
        try {
            await chrome.storage.local.set({
                [LocalStorageService.NOTION_CONFIG_KEY]: config
            });
            console.log('[LocalStorageService] Notion 配置已保存', {
                pageName: config.pageName,
                pageId: config.pageId
            });
        } catch (error) {
            console.error('[LocalStorageService] 保存 Notion 配置失败', error);
            throw error;
        }
    }

    /**
     * 获取 Notion 配置
     */
    public async getNotionConfig(): Promise<NotionConfig | null> {
        try {
            const result = await chrome.storage.local.get(LocalStorageService.NOTION_CONFIG_KEY);
            const config = result[LocalStorageService.NOTION_CONFIG_KEY] as NotionConfig | undefined;
            
            if (config) {
                console.log('[LocalStorageService] 获取 Notion 配置成功', {
                    pageName: config.pageName,
                    hasToken: !!config.token
                });
                return config;
            }
            
            console.log('[LocalStorageService] Notion 配置不存在');
            return null;
        } catch (error) {
            console.error('[LocalStorageService] 获取 Notion 配置失败', error);
            throw error;
        }
    }

    /**
     * 检查 Notion 是否已配置
     */
    public async hasNotionConfig(): Promise<boolean> {
        const config = await this.getNotionConfig();
        return config !== null && !!config.token && !!config.pageId;
    }

    /**
     * 清除 Notion 配置
     */
    public async clearNotionConfig(): Promise<void> {
        try {
            await chrome.storage.local.remove(LocalStorageService.NOTION_CONFIG_KEY);
            console.log('[LocalStorageService] Notion 配置已清除');
        } catch (error) {
            console.error('[LocalStorageService] 清除 Notion 配置失败', error);
            throw error;
        }
    }

    /**
     * 更新最后验证时间
     */
    public async updateLastVerified(): Promise<void> {
        const config = await this.getNotionConfig();
        if (config) {
            config.lastVerified = Date.now();
            await this.saveNotionConfig(config);
        }
    }
}

/**
 * 导出单例实例
 */
export const localStorageService = LocalStorageService.getInstance();
