import { SelectorConfig } from "./SelectorManager";

/**
 * ChatGPT 适配器的选择器配置
 * 整合了所有 ChatGPT 平台特有的选择器
 */
export const chatgptSelector: SelectorConfig = {
  inputField: [
    // 'textarea[name="prompt-textarea"]',
    '#prompt-textarea',                           // 主输入框ID
    // 'textarea[data-virtualkeyboard="true"]',      // 带虚拟键盘属性的输入框
    // '.ProseMirror[contenteditable="true"]',       // ProseMirror编辑器
    // '[data-testid="composer-input"]',             // 输入组件测试ID
    // 'textarea[placeholder*="询问"]',               // 中文占位符
    // 'textarea[placeholder*="Ask"]',               // 英文占位符
    // '[contenteditable="true"]'                    // 通用可编辑元素
  ],
  
  sendButton: [
    '[data-testid="send-button"]',                // 发送按钮测试ID
    'button[aria-label*="Send"]',                 // 英文发送按钮
    'button[aria-label*="发送"]',                 // 中文发送按钮
    'form button[type="submit"]',                 // 表单提交按钮
    '[data-testid="composer-send-button"]'        // 输入组件发送按钮
  ],
  
  headerContent: [

 ],

  // ChatGPT 特有的选择器
  chatContentList: [
    '.flex.flex-col:has(> article)',
    '.flex.flex-col.text-sm.thread-xl\\:pt-header-height.pb-25:has(> article)',
    // '[role="presentation"]',                      // 聊天列表容器
    // 'main[class*="flex"]',                        // 主对话区域
    // '[data-testid="conversation"]',               // 对话容器
    // 'main > div > div'                            // 主容器内的对话区
  ],

  promptItem: [
    'article[data-turn="user"]:has(> h5)',
    // 'article[data-testid^="conversation-turn-"][data-turn="user"]',  // 用户回合标记（精确匹配）
    // 'article[data-turn="user"]',                  // 用户回合标记（备用）
    // '[data-message-author-role="user"]',          // 用户消息角色
    // 'article[data-testid*="user"]'                // 用户消息测试ID
  ],

  promptContent: [
    '.user-message-bubble-color > .whitespace-pre-wrap',        // 用户消息内容
    // '[data-message-author-role="user"] .whitespace-pre-wrap',   // 用户消息内容（备用）
    // 'article[data-turn="user"] .text-message'                   // 用户文本消息
  ],

  promptAction: [
    'article[data-turn="user"] .trailing-pair',   // 用户消息操作区
    'article[data-turn="user"] button'            // 用户消息按钮组
  ],

  answerItem: [
    'article[data-turn="assistant"]:has(> h6)',  
    // 'article[data-testid^="conversation-turn-"][data-turn="assistant"]',      // AI回复消息内容
    // '[data-message-author-role="assistant"]',     // AI消息角色
    // 'article[data-testid*="assistant"]',          // AI消息测试ID
    // 'article.agent-turn'                          // AI回合类名
  ],

  answerCompletion: [
    '.z-0.flex.min-h-\\[46px\\].justify-start',         // 直接使用答案元素作为容器（最可靠）
    // '[data-message-author-role="assistant"]',     // AI消息角色（备用）
    // 'article[data-testid*="assistant"]'           // AI消息测试ID（备用）
  ],

  copyButton: [
    '[data-testid="copy-turn-action-button"]',    // 主复制按钮（最可靠）
    // 'button[aria-label*="Copy"]',                 // 英文复制按钮
    // 'button[aria-label*="复制"]',                 // 中文复制按钮
    // 'button[aria-label*="Copy message"]',         // 英文复制消息
    // 'button[aria-label*="复制消息"]'              // 中文复制消息
  ],

  markdown: [
    '.markdown.prose',                             // ChatGPT markdown容器
    // '[data-message-author-role="assistant"] .markdown',  // AI消息中的markdown
    // 'article[data-turn="assistant"] .markdown',    // AI回合中的markdown
    // '.prose.dark',                                 // 暗色主题markdown
    // '[data-message-author-role="assistant"] .prose'  // AI消息中的prose
  ]
};
