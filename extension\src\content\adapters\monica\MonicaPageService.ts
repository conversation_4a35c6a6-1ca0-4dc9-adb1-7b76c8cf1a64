import { BasePageService } from "@/content/core/BasePageService";

/**
 * Monica 页面状态管理服务
 * 继承 BasePageService，提供 Monica 平台特定的 URL 匹配规则
 * 
 * Monica URL 模式：
 * - Home页: https://monica.im/home/<USER>/{provider}/{model}
 * - Chat页: https://monica.im/home/<USER>/{provider}/{model}?convId={chatId}
 * 
 * 通过 convId 参数区分 Home 和 Chat 页面
 */
export class MonicaPageService extends BasePageService {
    /**
     * 平台名称
     */
    protected getPlatformName(): string {
        return 'Monica';
    }

    /**
     * 获取 Monica 的 URL 匹配模式
     * 
     * Home 和 Chat 页面的路径相同，通过 convId 查询参数区分
     * 注意：需要配合 isWelcomePage 和 isChatPage 方法中的额外检查
     */
    protected getUrlPatterns(): { home: RegExp; chat: RegExp } {
        return {
            // Home页模式: 基础路径匹配（需要额外检查没有 convId）
            // 匹配: https://monica.im/home/<USER>/Monica/monica
            // 匹配: https://monica.im/home/<USER>/GPT-5/gpt_5
            // 匹配: https://monica.im/home/<USER>/Monica/monica?other=value
            home: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+/i,
            
            // Chat页模式: 基础路径匹配（需要额外检查有 convId）
            // 匹配: https://monica.im/home/<USER>/Monica/monica?convId=conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208
            chat: /^https:\/\/monica\.im\/home\/<USER>\/[^/]+\/[^/]+/i
        };
    }

    /**
     * 从 URL 提取 chatId
     * Monica 的 chatId 在 convId 查询参数中，需要 URL 解码
     * 
     * @returns chatId 或 null
     */
    public extractChatId(): string | null {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const convId = urlParams.get('convId');
            
            if (!convId) {
                return null;
            }
            
            // URL 解码 convId
            // 例如: conv%3A257771e1-5b9b-4a41-b217-2aa336cd3208
            // 解码为: conv:257771e1-5b9b-4a41-b217-2aa336cd3208
            return decodeURIComponent(convId);
        } catch (error) {
            console.error('【MonicaPageService】提取 chatId 失败:', error);
            return null;
        }
    }

    /**
     * 基于 URL 判断页面类型（Monica 特定实现）
     * 重写父类方法，添加 convId 参数检查
     * 
     * @returns 页面类型信息
     */
    public getPageTypeFromUrl(): { isHome: boolean; isChat: boolean; chatId?: string } {
        const url = window.location.href;
        const patterns = this.getUrlPatterns();
        
        // Monica 特殊逻辑：通过 convId 参数区分页面类型
        const hasConvId = url.includes('convId=');
        const matchesBasePath = patterns.home.test(url);
        
        const isHome = matchesBasePath && !hasConvId;
        const isChat = matchesBasePath && hasConvId;
        const chatId = isChat ? this.extractChatId() : undefined;
        
        console.info('【MonicaPageService】getPageTypeFromUrl', {
            url,
            hasConvId,
            matchesBasePath,
            isHome,
            isChat,
            chatId
        });
        
        return { isHome, isChat, chatId };
    }

    /**
     * 检测欢迎页面（Monica 特定实现）
     * 
     * 策略：
     * 1. URL 路径匹配 /home/<USER>/{provider}/{model}
     * 2. 且 URL 中没有 convId 参数
     * 3. 降级使用 DOM 检测
     */
    public isWelcomePage(): boolean {
        const url = window.location.href;
        const patterns = this.getUrlPatterns();
        
        // 检查 URL 匹配且没有 convId 参数
        if (patterns.home.test(url) && !url.includes('convId=')) {
            return true;
        }
        
        // 降级使用 DOM 检测
        // Monica 的欢迎页通常有模型选择器和欢迎信息
        return document.querySelector('.bot-card--GoMWF') !== null ||
               document.querySelector('.bot-info--Jxx7u') !== null;
    }
    
    /**
     * 检测聊天页面（Monica 特定实现）
     * 
     * 策略：
     * 1. URL 路径匹配 /home/<USER>/{provider}/{model}
     * 2. 且 URL 中有 convId 参数
     * 3. 降级使用 DOM 检测
     */
    public isChatPage(): boolean {
        const url = window.location.href;
        const patterns = this.getUrlPatterns();
        
        // 检查 URL 匹配且有 convId 参数
        if (patterns.chat.test(url) && url.includes('convId=')) {
            return true;
        }
        
        // 降级使用 DOM 检测
        // 存在聊天消息记录
        return document.querySelector('.chat-reply--ntVOt') !== null ||
               document.querySelector('.chat-question--yrvtP') !== null;
    }
}
