import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { GeminiClipboardService } from "./GeminiClipboardService";

/**
 * Gemini 答案管理服务
 * 继承 BaseAnswerService，提供 Gemini 平台特定的答案处理逻辑
 * 
 * 主要特性：
 * - 监听 DOM 变化检测答案更新
 * - 使用 message-actions 组件出现作为答案完成信号
 * - 集成 GeminiClipboardService 提取答案内容
 * - 支持 Gemini 的 Angular Material 组件结构
 */
export class GeminiAnswerService extends BaseAnswerService {
    private clipboardService: GeminiClipboardService;

    constructor() {
        super();
        this.clipboardService = new GeminiClipboardService();
        console.info('[GeminiAnswerService] Gemini 答案服务已初始化');
    }

    /**
     * 获取剪贴板服务实例（BaseAnswerService 抽象方法实现）
     * @returns GeminiClipboardService 实例
     */
    protected getClipboardService(): GeminiClipboardService {
        return this.clipboardService;
    }
}
