import { BaseAIAdapter } from "@/content/core/BaseAIAdapter";
import { GrokAnswerController } from "./GrokAnswerController";
import { PlatformEntity } from "@/common/types/database_entity";

/**
 * Grok 平台适配器
 * 继承 BaseAIAdapter，提供 Grok 平台的完整集成
 * 
 * 主要特性：
 * - 自动检测 Grok 页面类型（欢迎页/聊天页）
 * - 监听用户提问并捕获 AI 回答
 * - 支持暗色主题的代码块提取
 * - 优先使用复制按钮，自动降级到 DOM 提取
 * 
 * 使用示例：
 * ```typescript
 * const adapter = new GrokAdapter(platform);
 * await adapter.initialize();
 * ```
 */
export class GrokAdapter extends BaseAIAdapter {
    private grokAnswerController: GrokAnswerController | null = null;

    constructor(platform: PlatformEntity) {
        console.log('【GrokAdapter】GrokAdapter constructor called');
        super(platform);
        
        // 创建并初始化答案控制器
        this.grokAnswerController = new GrokAnswerController();
        this.grokAnswerController.init(this);
        
        console.log('【GrokAdapter】GrokAdapter initialized with platform:', platform);
    }

    /**
     * 销毁适配器，清理所有资源
     */
    destroy(): void {
        try {
            console.log('【GrokAdapter】GrokAdapter destroy called');
            
            // 调用父类销毁方法
            super.destroy();
            
            // 清理 GrokAnswerController
            if (this.grokAnswerController) {
                this.grokAnswerController.destroy();
                this.grokAnswerController = null;
                console.log('【GrokAdapter】GrokAnswerController 清理完成');
            }
            
            console.log('【GrokAdapter】GrokAdapter destroy 完成');
            
        } catch (error) {
            console.error('【GrokAdapter】GrokAdapter destroy 失败:', error);
        }
    }
}
