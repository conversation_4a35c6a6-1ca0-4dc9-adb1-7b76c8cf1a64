# 微交互设计规范 (Microinteractions)

## 📚 文档关系

- **MicrointeractionsGuide.md**（当前文档）- 微交互设计思想和场景分析
- **CSSDesignTokens.md** - 完整 CSS 代码实现
- **UIAgentRules.md** - 核心设计原则和 AI 提示词
---

## 🎯 微交互核心原则

**微交互定义**：产品中围绕单一任务的小型、包含反馈的时刻。

### Apple 设计哲学
1. **即时反馈**：用户操作应立即得到响应（100-200ms）
2. **愉悦感**：微妙的动效增强体验愉悦度
3. **引导性**：通过动效引导用户注意力
4. **自然感**：模拟物理世界的真实感（弹性、惯性）

---

## 🎯 Apple 风格设计要点

1. **物理真实感**：模拟真实世界的运动（惯性、弹跳）
2. **流畅连贯**：动画之间自然过渡，无突兀感
3. **细节精致**：每个小细节都有精心设计的动效
4. **克制使用**：动效服务于功能，不喧宾夺主
5. **一致性**：相同类型的交互使用相同的动效


## ⚡ 动效时长标准

| 时长 | 用途 | 使用场景 |
|------|------|----------|
| 100ms | 即时反馈 | 按钮按下、开关切换 |
| 150ms | 快速反馈 | 悬停状态、图标变化 |
| 200ms | 标准动画 | 卡片展开、菜单显示 |
| 300ms | 慢速动画 | 页面过渡、复杂动画 |

**推荐**：大部分微交互在 150-200ms


### 1. 按钮点击反馈

**设计目标**：提供即时、愉悦的触觉反馈

| 方案 | 效果 | 时长 | 适用场景 |
|------|------|------|----------|
| **缩放 + 阴影** | 悬停上浮 -1px<br>按下缩小到 0.98 | 150ms | ✅ 推荐：适合所有按钮 |
| **涟漪扩散** | 点击位置向外扩散圆形波纹 | 400ms | Material Design 风格 |

**关键点**：
- 悬停状态必须即时响应（150ms）
- 按下状态要有"按压感"（scale 0.98）
- 使用弹性缓动增强愉悦感

---

### 2. 卡片交互

**设计目标**：营造"悬浮"和"可点击"的感觉

| 状态 | 效果 | 时长 |
|------|------|------|
| **默认** | 浅边框 + 无阴影 | - |
| **悬停** | 上浮 -4px + 蓝色阴影 + 顶部边框显现 | 200ms |
| **按下** | 上浮 -2px + 轻微缩小 0.99 | 200ms |

**关键点**：
- 上浮距离要适中（-4px），不宜过大
- 顶部 3px 蓝色边框是 Linear 特色
- 阴影从无到有的渐变要自然

---

### 3. 开关/切换按钮

**设计目标**：清晰的开/关状态切换

| 元素 | 效果 | 时长 |
|------|------|------|
| **背景** | 灰色 → 蓝色 | 200ms |
| **圆形滑块** | 左侧 → 右侧（translateX 20px） | 200ms |
| **点击反馈** | 滑块轻微缩小 0.95 | 100ms |

**关键点**：
- 使用弹性缓动让滑动更自然
- 点击时滑块微缩放增强反馈
- 背景色变化要与滑块同步

---

### 4. 输入框焦点

**设计目标**：引导用户注意当前输入位置

| 状态 | 效果 | 时长 |
|------|------|------|
| **悬停** | 边框颜色加深 | 200ms |
| **焦点** | 蓝色边框 + 蓝色阴影 + 微放大 1.01 | 200ms |

**关键点**：
- 焦点状态要明显（颜色 + 阴影）
- 微妙放大（1.01）增强吸引力
- 避免过度动画影响输入体验

---

### 5. 图标变化

#### 收藏/点赞图标
**设计目标**：愉悦的情感反馈

| 状态 | 效果 | 时长 |
|------|------|------|
| **悬停** | 放大 1.1 + 颜色变化 | 200ms |
| **按下** | 缩小 0.95 | 100ms |
| **激活** | 心跳动画（1 → 1.15 → 1） | 300ms |

**关键点**：
- 心跳动画要有弹性感（spring easing）
- 颜色变化要与动画同步
- 避免过度频繁触发动画

#### 汉堡菜单图标
**设计目标**：清晰的展开/收起状态

| 状态 | 效果 | 时长 |
|------|------|------|
| **展开** | 三条线变成 X 形<br>（上下旋转 45°/-45°，中间透明度 0） | 200ms |

**关键点**：
- 使用 transform-origin 控制旋转中心
- 三条线同步变化，无延迟

---

### 6. 加载状态

#### 骨架屏
**设计目标**：减少等待焦虑，提示内容正在加载

**效果**：从左到右的光泽扫过动画  
**时长**：1.5s 循环

**关键点**：
- 背景渐变要自然（灰色 → 浅灰 → 灰色）
- 循环动画要流畅，无卡顿感

#### 脉动效果
**效果**：透明度 1 → 0.5 → 1 循环  
**时长**：2s 循环

**适用**：简单加载提示、占位符

#### 旋转加载器
**效果**：圆形边框旋转 360°  
**时长**：0.8s 循环

**适用**：按钮加载状态、全局 loading

---

### 7. 通知/Toast 提示

**设计目标**：吸引注意但不打断操作

| 阶段 | 效果 | 时长 |
|------|------|------|
| **进入** | 从上方滑入（translateY -20px → 0）<br>透明度 0 → 1 | 300ms |
| **停留** | 静止显示 | 3-5s |
| **退出** | 向上滑出 + 淡出 | 300ms |

**关键点**：
- 左侧彩色边框区分状态（成功/错误/警告）
- 进入/退出动画要对称
- 自动消失时机要合理（3-5s）

---

### 8. 下拉菜单

**设计目标**：流畅展开，自然收起

| 状态 | 效果 | 时长 |
|------|------|------|
| **展开** | 向下移动 + 放大（scale 0.95 → 1）<br>透明度 0 → 1 | 200ms |
| **收起** | 反向动画 | 200ms |
| **菜单项悬停** | 背景色变化 | 150ms |

**关键点**：
- 使用 transform 而非改变 height（性能更好）
- 菜单展开的原点要准确（从触发按钮位置）
- 菜单项悬停要有即时反馈

---

### 9. Modal/弹窗

**设计目标**：聚焦内容，流畅进出

| 元素 | 效果 | 时长 |
|------|------|------|
| **背景遮罩** | 透明 → 半透明黑色 | 200ms |
| **弹窗内容** | 缩小 + 下移（scale 0.9 + translateY 20px）<br>透明度 0 → 1 | 300ms |

**关键点**：
- 使用弹性缓动（spring）增强愉悦感
- 背景和内容动画要同步
- 关闭动画是打开的反向

---

### 10. 滚动触发动画

**设计目标**：内容渐进呈现，引导视线

| 效果 | 实现 | 时长 |
|------|------|------|
| **淡入上移** | 透明度 0 → 1<br>向上移动（translateY 30px → 0） | 600ms |
| **交错动画** | 每个元素延迟 100ms | - |

**关键点**：
- 元素出现要有序（从上到下，从左到右）
- 延迟时间不宜过长（100-150ms）
- 滚动到视口即触发，不要过早或过晚

---

### 缩放比例参考

| 场景 | 推荐比例 | 用途 |
|------|---------|------|
| 按下反馈 | 0.95-0.98 | 反馈按压感 |
| 悬停提示 | 1.02-1.05 | 提示可交互 |
| 图标强调 | 1.1-1.15 | 吸引注意 |
| 输入焦点 | 1.01 | 微妙强调 |

### 设计原则
- ✅ **允许**：微小缩放（0.95-1.15）+ 其他效果（位移/颜色）
- ✅ **允许**：弹性动画（心跳、脉动）
- ❌ **禁止**：单纯大幅缩放（无功能意义）
- ❌ **禁止**：卡片纯缩放（应配合 translateY）

---

## 🌊 缓动函数选择

| 缓动类型 | 适用场景 | 特点 |
|----------|----------|------|
| **标准缓动** | 按钮、卡片、输入框 | 流畅自然，适合大部分场景 |
| **弹性效果** | 开关切换、收藏动画 | 增强愉悦感，Apple 风格 |
| **快速退出** | 关闭弹窗、隐藏菜单 | 快速响应用户操作 |
| **慢速进入** | 展开内容、显示更多 | 引导用户注意 |


