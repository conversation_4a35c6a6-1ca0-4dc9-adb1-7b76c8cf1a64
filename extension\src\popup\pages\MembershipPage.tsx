import React from 'react'
import { StatusIndicator, ProgressBar } from '../components/button'

export function MembershipPage() {
  // Mock data - TODO: Replace with real data from store
  const currentPlan = {
    name: 'Pro 套餐',
    price: '$9.9/月',
    nextBilling: '2024-02-15',
    features: [
      '无限对话次数',
      '高级AI模型',
      '多设备同步',
      '优先客服支持'
    ]
  }

  const upgradeOptions = [
    {
      name: 'Plus 套餐',
      price: '$19.9/月',
      description: '包含Pro所有功能，plus更多高级特性',
      color: 'purple'
    },
    {
      name: 'Max 套餐',
      price: '$39.9/月',
      description: '企业级功能，无限制使用',
      color: 'gray'
    }
  ]

  const usageStats = {
    conversations: { current: 847, max: '无限' },
    storage: { current: 2.3, max: 10, unit: 'GB' }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 当前套餐 */}
      <div className="card fade-in">
        <div className="card-title">👑 当前套餐</div>
        <div className="card-content">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
            <div>
              <div style={{ fontWeight: 600, fontSize: '18px', color: 'var(--blue-600)' }}>
                {currentPlan.name}
              </div>
              <div style={{ fontSize: '12px', color: 'var(--text-muted)' }}>
                {currentPlan.price} • 下次扣费: {currentPlan.nextBilling}
              </div>
            </div>
            <StatusIndicator type="success">活跃</StatusIndicator>
          </div>
          <div style={{ margin: '12px 0' }}>
            {currentPlan.features.map((feature, index) => (
              <div key={index} style={{ fontSize: '14px', marginBottom: '8px' }}>
                ✓ {feature}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 升级选项 */}
      <div className="card fade-in">
        <div className="card-title">🚀 升级选项</div>
        <div className="card-content">
          {upgradeOptions.map((option, index) => (
            <div 
              key={index}
              style={{ 
                border: `1px solid rgba(124, 58, 237, 0.2)`, 
                borderRadius: '12px', 
                padding: '16px', 
                marginBottom: index < upgradeOptions.length - 1 ? '12px' : '0',
                background: option.color === 'purple' ? 'rgba(124, 58, 237, 0.02)' : 'rgba(15, 23, 42, 0.02)'
              }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <div style={{ 
                  fontWeight: 600, 
                  color: option.color === 'purple' ? 'var(--purple-600)' : 'var(--text-primary)' 
                }}>
                  {option.name}
                </div>
                <div style={{ 
                  fontWeight: 600, 
                  color: option.color === 'purple' ? 'var(--purple-600)' : 'var(--text-primary)' 
                }}>
                  {option.price}
                </div>
              </div>
              <div style={{ fontSize: '12px', color: 'var(--text-muted)', marginBottom: '12px' }}>
                {option.description}
              </div>
              <button 
                className="btn btn-secondary" 
                style={{ 
                  width: '100%', 
                  background: option.color === 'purple' ? 'var(--purple-500)' : 'var(--card-bg)', 
                  color: option.color === 'purple' ? 'white' : 'var(--blue-600)',
                  border: option.color === 'purple' ? 'none' : '1px solid rgba(37, 99, 235, 0.2)'
                }}
              >
                升级到{option.name.split(' ')[0]}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* 使用统计 */}
      <div className="card fade-in">
        <div className="card-title">📊 使用统计</div>
        <div className="card-content">
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
              <span style={{ fontSize: '14px' }}>本月对话</span>
              <span style={{ fontSize: '14px', fontWeight: 600 }}>
                {usageStats.conversations.current} / {usageStats.conversations.max}
              </span>
            </div>
            <ProgressBar value={60} showAnimation />
          </div>
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
              <span style={{ fontSize: '14px' }}>存储空间</span>
              <span style={{ fontSize: '14px', fontWeight: 600 }}>
                {usageStats.storage.current}{usageStats.storage.unit} / {usageStats.storage.max}{usageStats.storage.unit}
              </span>
            </div>
            <ProgressBar value={23} showAnimation />
          </div>
        </div>
      </div>
    </div>
  )
}
