import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import { BasePageService } from "@/content/core/BasePageService";
import { KimiAnswerService } from "./KimiAnswerService";
import { KimiPageService } from "./KimiPageService";

/**
 * Kimi 平台答案捕获控制器
 * 继承 BaseAnswerController，提供 Kimi 平台特定的服务实例
 * 大部分流程编排逻辑已在基类实现
 */
export class KimiAnswerController extends BaseAnswerController {
    /**
     * 创建 Kimi 平台的服务实例
     */
    protected createServices(): {
        answerService: BaseAnswerService;
        pageService: BasePageService;
    } {
        return {
            answerService: new KimiAnswerService(),
            pageService: new KimiPageService()
        };
    }
}
