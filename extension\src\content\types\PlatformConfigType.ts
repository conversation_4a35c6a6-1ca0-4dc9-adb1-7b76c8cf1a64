import { PlatformName } from '@/common/types'

/**
 * 平台配置接口
 * 定义每个AI平台的基本信息和选择器配置
 */
export interface PlatformConfig {
    /** 平台名称（唯一标识） */
    name: PlatformName
    /** 平台 URL */
    url: string
  
    /** 平台识别模式 */
    patterns: {
      /** 主机名匹配正则 */
      hostname: RegExp
      /** 有效路径匹配正则（可选） */
      validPath?: RegExp
    }
  }
  
  /**
   * 选择器匹配结果
   */
  export interface SelectorMatchResult {
    element: Element | null
    selector: string
    index: number
  }
  
  /**
   * 正则匹配结果
   */
  export interface RegexMatchResult {
    matched: boolean
    groups?: RegExpMatchArray
    content?: string
  }
  
  
  /**
   * 消息提取结果
   */
  export interface MessageExtractionResult {
    role: 'user' | 'assistant'
    content: string
    timestamp: number
    element: Element
  }
  
