import React from 'react'
import { cn } from '@/common/utils'

interface UserStatusBadgeProps {
  plan: 'free' | 'pro' | 'plus' | 'max'
  className?: string
}

export const UserStatusBadge: React.FC<UserStatusBadgeProps> = ({
  plan,
  className
}) => {
  const getPlanConfig = (plan: string) => {
    switch (plan) {
      case 'free':
        return { label: 'FREE', icon: '🆓' }
      case 'pro':
        return { label: 'PRO', icon: '⭐' }
      case 'plus':
        return { label: 'PLUS', icon: '💎' }
      case 'max':
        return { label: 'MAX', icon: '👑' }
      default:
        return { label: 'FREE', icon: '🆓' }
    }
  }

  const config = getPlanConfig(plan)

  return (
    <span className={cn('user-status-badge', plan, className)}>
      <span className="badge-icon">{config.icon}</span>
      <span className="badge-text">{config.label}</span>
    </span>
  )
}
