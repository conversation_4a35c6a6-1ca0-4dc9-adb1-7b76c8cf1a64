---
type: "development_rules"
description: "Popup模块开发规则"
---

# Popup规则

## 核心职责
- **快速访问**: 插件功能入口
- **轻量级**: 界面简洁，加载快速
- **实时状态**: 显示插件状态

## 技术栈
- React 18 + TypeScript 5
- Tailwind CSS 3 + shadcn/ui

## 目录结构
```
popup/
├── components/  # 弹窗组件
├── hooks/       # 自定义Hooks
├── App.tsx      # 主应用
└── main.tsx     # 入口
```

## 核心规则
- 适配小尺寸窗口(320x600px)
- 使用MessagingService与Background通信
- 处理DatabaseResult格式数据
- 支持键盘导航
- 提供加载和错误状态

## 检查清单
- [ ] React + TypeScript开发
- [ ] 响应式布局
- [ ] 遵循database.md规则
- [ ] 错误处理和重试
- [ ] 文件≤300行
