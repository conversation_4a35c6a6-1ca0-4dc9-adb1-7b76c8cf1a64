/* Notion 配置引导 Modal 样式 */

/* CSS 变量 */
:root {
  --notion-black: #000000;
  --notion-white: #FFFFFF;
  --notion-gray-100: #F7F6F3;
  --notion-gray-200: #E3E2E0;
  --notion-gray-300: #D3D1CB;
  --notion-gray-600: #6F6F6F;
  --notion-gray-800: #373530;
  
  --notion-primary: #2383E2;
  --notion-success: #0F7B6C;
  --notion-error: #E03E3E;
  
  --notion-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 10px 15px rgba(0, 0, 0, 0.1);
}

/* 遮罩层 */
.notion-setup-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notion-setup-modal-overlay.show {
  opacity: 1;
}

/* Modal 容器 */
.notion-setup-modal {
  background: var(--notion-white);
  border-radius: 12px;
  box-shadow: var(--notion-shadow);
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.notion-setup-modal-overlay.show .notion-setup-modal {
  transform: scale(1);
}

/* Header */
.setup-header {
  padding: 24px;
  border-bottom: 1px solid var(--notion-gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--notion-gray-800);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--notion-gray-600);
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: var(--notion-gray-100);
  color: var(--notion-gray-800);
}

/* Body */
.setup-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
}

.step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--notion-gray-200);
  color: var(--notion-gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.step.active {
  background: var(--notion-primary);
  color: white;
}

.step.completed {
  background: var(--notion-success);
  color: white;
}

.step-line {
  width: 100px;
  height: 2px;
  background: var(--notion-gray-200);
  margin: 0 8px;
  transition: all 0.3s ease;
}

.step-line.active {
  background: var(--notion-primary);
}

/* 步骤 1 内容 */
.setup-body h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--notion-gray-800);
  text-align: center;
}

.setup-body > p {
  margin: 0 0 24px 0;
  color: var(--notion-gray-600);
  text-align: center;
  font-size: 14px;
}

.setup-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setup-step {
  display: flex;
  gap: 16px;
}

.step-number {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--notion-gray-100);
  color: var(--notion-gray-800);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-content {
  flex: 1;
}

.step-content h5 {
  margin: 0 0 8px 0;
  font-size: 15px;
  font-weight: 600;
  color: var(--notion-gray-800);
}

.step-content p {
  margin: 4px 0;
  font-size: 14px;
  color: var(--notion-gray-600);
  line-height: 1.5;
}

.step-content ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.step-content li {
  font-size: 14px;
  color: var(--notion-gray-600);
  line-height: 1.5;
  margin: 4px 0;
}

.step-content a {
  color: var(--notion-primary);
  text-decoration: none;
}

.step-content a:hover {
  text-decoration: underline;
}

/* 步骤 2 表单 */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--notion-gray-800);
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--notion-gray-300);
  border-radius: 6px;
  font-size: 14px;
  color: var(--notion-gray-800);
  background: var(--notion-white);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: var(--notion-primary);
  box-shadow: 0 0 0 3px rgba(35, 131, 226, 0.1);
}

.form-group small {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  color: var(--notion-gray-600);
}

/* 验证状态 */
.validation-status {
  margin-top: 16px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  display: none;
}

.validation-status.loading,
.validation-status.success,
.validation-status.error {
  display: block;
}

.validation-status.loading {
  background: var(--notion-gray-100);
  color: var(--notion-gray-800);
}

.validation-status.success {
  background: rgba(15, 123, 108, 0.1);
  color: var(--notion-success);
}

.validation-status.error {
  background: rgba(224, 62, 62, 0.1);
  color: var(--notion-error);
}

/* 测试部分 */
.test-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--notion-gray-200);
}

.btn-test {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--notion-primary);
  background: white;
  color: var(--notion-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.btn-test:hover:not(:disabled) {
  background: var(--notion-primary);
  color: white;
}

.btn-test:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-test:active:not(:disabled) {
  transform: scale(0.98);
}

.test-result {
  margin-top: 12px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  display: none;
}

.test-result.loading,
.test-result.success,
.test-result.error {
  display: block;
}

.test-result.loading {
  background: var(--notion-gray-100);
  color: var(--notion-gray-800);
}

.test-result.success {
  background: rgba(15, 123, 108, 0.1);
  color: var(--notion-success);
  font-weight: 500;
}

.test-result.error {
  background: rgba(224, 62, 62, 0.1);
  color: var(--notion-error);
}

.form-group small a {
  color: var(--notion-primary);
  text-decoration: none;
}

.form-group small a:hover {
  text-decoration: underline;
}

/* Footer */
.setup-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--notion-gray-200);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 按钮样式 */
.btn-primary,
.btn-secondary {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: var(--notion-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1a6ec4;
}

.btn-primary:active:not(:disabled) {
  transform: scale(0.98);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: transparent;
  color: var(--notion-gray-800);
  border: 1px solid var(--notion-gray-300);
}

.btn-secondary:hover {
  background: var(--notion-gray-100);
}

.btn-secondary:active {
  transform: scale(0.98);
}

/* 响应式 */
@media (max-width: 640px) {
  .notion-setup-modal {
    width: 95%;
    max-height: 95vh;
  }

  .setup-header,
  .setup-body,
  .setup-footer {
    padding: 16px;
  }

  .step-line {
    width: 60px;
  }
}
