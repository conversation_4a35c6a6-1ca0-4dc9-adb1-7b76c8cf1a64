/* Obsidian 导出 Modal 样式 */

/* 固定为浅色主题，避免跟随宿主页面的暗色 scheme */
.obsidian-modal {
  /* 莫兰迪蓝色系配色 */
  --obsidian-bg: #f8f9fb;
  --obsidian-bg-card: #ffffff;
  --obsidian-bg-hover: #f0f3f7;
  --obsidian-border: #d8dfe8;
  --obsidian-text: #3d4b5c;
  --obsidian-text-light: #7a8a9e;
  --obsidian-primary: #7c9cbf;
  --obsidian-secondary: #9fb4cd;
  --obsidian-accent: #a4b8d1;
  --obsidian-radius-sm: 8px;
  --obsidian-radius-md: 12px;
  --obsidian-spacing-xs: 4px;
  --obsidian-spacing-sm: 8px;
  --obsidian-spacing-md: 12px;
  --obsidian-spacing-lg: 16px;
  --obsidian-spacing-xl: 24px;
  color-scheme: only light;
  color: var(--obsidian-text);
  background: var(--obsidian-bg);
  position: relative;
}

.obsidian-modal * {
  color-scheme: only light;
}

/* 遮罩层 */
.obsidian-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.obsidian-modal-overlay.show {
  opacity: 1;
}

/* Modal 容器 */
.obsidian-modal {
  background: var(--obsidian-bg-card, #FFFFFF);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(124, 156, 191, 0.15), 0 2px 8px rgba(124, 156, 191, 0.08);
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: scale(0.9);
  transition: transform 0.3s ease;
  border: 1px solid rgba(124, 156, 191, 0.12);
}

.obsidian-modal-overlay.show .obsidian-modal {
  transform: scale(1);
}

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--obsidian-spacing-lg, 16px) var(--obsidian-spacing-xl, 24px);
  border-bottom: 1px solid var(--obsidian-border, #d8dfe8);
  background: linear-gradient(135deg, rgba(124, 156, 191, 0.04), rgba(159, 180, 205, 0.02));
}

.header-title {
  display: flex;
  align-items: center;
  gap: var(--obsidian-spacing-md, 12px);
}

.obsidian-logo {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.obsidian-logo svg {
  width: 100%;
  height: 100%;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--obsidian-text, #3d4b5c);
  letter-spacing: -0.01em;
}

.close-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: transparent;
  color: var(--obsidian-text-light, #7a8a9e);
  font-size: 24px;
  line-height: 1;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(124, 156, 191, 0.1);
  color: var(--obsidian-text, #3d4b5c);
}

/* Body */
.modal-body {
  display: flex;
  flex-direction: column;
  gap: var(--obsidian-spacing-md, 12px);
  flex: 1;
  overflow-y: auto;
  padding: var(--obsidian-spacing-lg, 16px) var(--obsidian-spacing-xl, 24px);
  padding-bottom: calc(var(--obsidian-spacing-xl, 24px) + 96px);
  background: var(--obsidian-bg);
}

.form-group {
  margin: 0;
  padding: var(--obsidian-spacing-md, 12px) var(--obsidian-spacing-lg, 16px);
  border-radius: var(--obsidian-radius-md, 12px);
  border: 1px solid rgba(124, 156, 191, 0.15);
  background: var(--obsidian-bg-card);
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.06);
  transition: all 0.2s ease;
}

.form-group:hover {
  border-color: rgba(124, 156, 191, 0.25);
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.1);
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  margin-bottom: var(--obsidian-spacing-sm, 8px);
  font-size: 13px;
  font-weight: 500;
  color: var(--obsidian-text-light, #7a8a9e);
  letter-spacing: 0.01em;
}

.form-input {
  width: 100%;
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 12px);
  border: 1px solid var(--obsidian-border, #d8dfe8);
  border-radius: 8px;
  font-size: 14px;
  color: var(--obsidian-text, #3d4b5c);
  transition: all 0.2s ease;
  box-sizing: border-box;
  background: #ffffff;
}

.form-input:focus {
  outline: none;
  border-color: var(--obsidian-primary, #7c9cbf);
  box-shadow: 0 0 0 3px rgba(124, 156, 191, 0.12);
  background: #ffffff;
}

.content-preview {
  padding: var(--obsidian-spacing-lg, 16px);
  background: rgba(124, 156, 191, 0.04);
  border-radius: var(--obsidian-radius-md, 12px);
  font-size: 14px;
  color: var(--obsidian-text, #3d4b5c);
  border: 1px solid rgba(124, 156, 191, 0.12);
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.04);
  max-height: none;
  overflow: visible;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Markdown 样式 - Obsidian 风格 */
.obsidian-modal .markdown-body h1,
.obsidian-modal .markdown-body h2,
.obsidian-modal .markdown-body h3,
.obsidian-modal .markdown-body h4,
.obsidian-modal .markdown-body h5,
.obsidian-modal .markdown-body h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--obsidian-text, #1F2937);
  line-height: 1.3;
}

.obsidian-modal .markdown-body h1:first-child,
.obsidian-modal .markdown-body h2:first-child,
.obsidian-modal .markdown-body h3:first-child {
  margin-top: 0;
}

.obsidian-modal .markdown-body h1 { font-size: 24px; }
.obsidian-modal .markdown-body h2 { font-size: 20px; }
.obsidian-modal .markdown-body h3 { font-size: 18px; }
.obsidian-modal .markdown-body h4 { font-size: 16px; }
.obsidian-modal .markdown-body h5 { font-size: 14px; }
.obsidian-modal .markdown-body h6 { font-size: 14px; color: var(--obsidian-text-light, #6B7280); }

.obsidian-modal .markdown-body p {
  margin: 0 0 12px 0;
  color: var(--obsidian-text, #1F2937);
}

.obsidian-modal .markdown-body strong {
  font-weight: 600;
  color: var(--obsidian-text, #1F2937);
}

.obsidian-modal .markdown-body em {
  font-style: italic;
}

.obsidian-modal .markdown-body code.inline-code {
  background: rgba(139, 92, 246, 0.1);
  color: #8B5CF6;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 85%;
}

.obsidian-modal .markdown-body pre {
  background: #F3F4F6;
  border: 1px solid var(--obsidian-border, #E5E7EB);
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 12px 0;
}

.obsidian-modal .markdown-body pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  font-family: 'Monaco', 'Menlo', 'Courier New', monospace;
  font-size: 13px;
  color: #1F2937;
}

.obsidian-modal .markdown-body ul,
.obsidian-modal .markdown-body ol {
  margin: 0 0 12px 0;
  padding-left: 24px;
}

.obsidian-modal .markdown-body li {
  margin-bottom: 4px;
  color: var(--obsidian-text, #1F2937);
}

.obsidian-modal .markdown-body blockquote {
  border-left: 3px solid #8B5CF6;
  padding-left: 16px;
  margin: 12px 0;
  color: var(--obsidian-text-light, #6B7280);
}

.obsidian-modal .markdown-body a {
  color: var(--obsidian-primary, #3B82F6);
  text-decoration: none;
}

.obsidian-modal .markdown-body a:hover {
  text-decoration: underline;
}

.obsidian-modal .markdown-body hr {
  border: none;
  border-top: 1px solid var(--obsidian-border, #E5E7EB);
  margin: 16px 0;
}

.obsidian-modal .markdown-body table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

.obsidian-modal .markdown-body table th,
.obsidian-modal .markdown-body table td {
  border: 1px solid var(--obsidian-border, #E5E7EB);
  padding: 8px 12px;
  text-align: left;
}

.obsidian-modal .markdown-body table th {
  background: var(--obsidian-bg-hover, #F3F4F6);
  font-weight: 600;
}

/* KaTeX 数学公式样式 */
.obsidian-modal .markdown-body .katex {
  font-size: 1.1em;
}

.obsidian-modal .markdown-body .katex-display {
  margin: 16px 0;
  overflow-x: auto;
  overflow-y: hidden;
}

.obsidian-modal .markdown-body .math-error {
  color: #EF4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.metadata {
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 12px);
  background: rgba(124, 156, 191, 0.06);
  border-radius: 8px;
  font-size: 13px;
  color: var(--obsidian-text-light, #7a8a9e);
}

/* Tag 选择器 */
.tag-selector {
  display: flex;
  flex-direction: column;
  gap: var(--obsidian-spacing-sm, 8px);
}

.tag-input-wrapper {
  position: relative;
}

.tag-input {
  width: 100%;
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-md, 12px);
  border: 1px solid var(--obsidian-border, #d8dfe8);
  border-radius: 8px;
  font-size: 13px;
  color: var(--obsidian-text, #3d4b5c);
  transition: all 0.2s ease;
  box-sizing: border-box;
  background: #ffffff;
}

.tag-input:focus {
  outline: none;
  border-color: var(--obsidian-primary, #7c9cbf);
  box-shadow: 0 0 0 3px rgba(124, 156, 191, 0.12);
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  min-height: 28px;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  background: linear-gradient(135deg, var(--obsidian-primary, #7c9cbf), var(--obsidian-secondary, #9fb4cd));
  color: white;
  border-radius: 14px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(124, 156, 191, 0.2);
}

.tag-text {
  line-height: 1;
}

.tag-remove {
  width: 16px;
  height: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 14px;
  line-height: 1;
  cursor: pointer;
  border-radius: 50%;
  transition: background 0.2s ease;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Footer */
.modal-footer {
  padding: var(--obsidian-spacing-lg, 16px) var(--obsidian-spacing-xl, 24px);
  border-top: 1px solid var(--obsidian-border, #d8dfe8);
  display: flex;
  justify-content: flex-end;
  background: rgba(124, 156, 191, 0.02);
}

.export-btn {
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-xl, 24px);
  background: linear-gradient(135deg, var(--obsidian-primary, #7c9cbf), var(--obsidian-secondary, #9fb4cd));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
  box-shadow: 0 2px 8px rgba(124, 156, 191, 0.25);
}

.export-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.35);
}

.export-btn:active {
  transform: translateY(0);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.obsidian-modal .modal-scroll-top {
  position: absolute;
  right: var(--obsidian-spacing-xl, 24px);
  bottom: calc(var(--obsidian-spacing-lg, 16px) + 56px);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 999px;
  border: none;
  background: linear-gradient(135deg, var(--obsidian-primary, #7c9cbf), var(--obsidian-secondary, #9fb4cd));
  color: #ffffff;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(124, 156, 191, 0.25);
  opacity: 0;
  transform: translateY(12px);
  pointer-events: none;
  transition: opacity 0.25s ease, transform 0.25s ease, box-shadow 0.2s ease;
}

.obsidian-modal .modal-scroll-top:hover {
  box-shadow: 0 6px 16px rgba(124, 156, 191, 0.35);
}

.obsidian-modal .modal-scroll-top:active {
  transform: translateY(0) scale(0.98);
}

.obsidian-modal .modal-scroll-top.visible {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.obsidian-modal .modal-scroll-top .scroll-top-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.18);
  font-size: 12px;
  line-height: 1;
}

.obsidian-modal .modal-scroll-top .scroll-top-text {
  letter-spacing: 0.4px;
}

.obsidian-modal .modal-scroll-top .scroll-top-hint {
  font-size: 11px;
  opacity: 0.85;
}

.export-btn {
  padding: var(--obsidian-spacing-sm, 8px) var(--obsidian-spacing-lg, 24px);
  background: linear-gradient(135deg, var(--obsidian-primary, #3B82F6), var(--obsidian-secondary, #8B5CF6));
  color: white;
  border: none;
  border-radius: var(--obsidian-radius-sm, 4px);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 150px;
}

.export-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.export-btn:active {
  transform: translateY(0);
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Toast 提示 */
.obsidian-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: var(--obsidian-spacing-md, 16px) var(--obsidian-spacing-lg, 24px);
  background: #ffffff;
  color: var(--obsidian-text, #1F2937);
  border-radius: var(--obsidian-radius-sm, 4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10001;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.obsidian-toast.show {
  opacity: 1;
  transform: translateX(0);
}

.obsidian-toast-success {
  border-left: 4px solid #10B981;
}

.obsidian-toast-warning {
  border-left: 4px solid #F59E0B;
}

.obsidian-toast-error {
  border-left: 4px solid #EF4444;
}

/* 响应式 */
@media (max-width: 768px) {
  .obsidian-modal {
    width: 100%;
    max-width: 95vw;
    max-height: 90vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding: var(--obsidian-spacing-md, 16px);
  }

  .modal-body {
    padding-bottom: calc(var(--obsidian-spacing-md, 16px) + 88px);
  }

  .obsidian-modal .modal-scroll-top {
    right: var(--obsidian-spacing-md, 16px);
    bottom: calc(var(--obsidian-spacing-md, 16px) + 72px);
  }
}
