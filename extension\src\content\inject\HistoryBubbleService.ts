import { ChatPromptListResp } from '@/common/types/content_vo'
import { HistoryBubble, HistoryBubbleOptions } from '@/content/inject/components/HistoryBubble'
import { toast } from '@/content/inject/components/Toast'

/**
 * 历史记录模态页服务类
 * 负责交互逻辑和事件处理
 */
export class HistoryBubbleService {
  private component: HistoryBubble

  constructor(options: HistoryBubbleOptions = {}) {
    // 创建UI组件，传入事件处理器
    this.component = new HistoryBubble(options, {
      onItemClick: (chat) => this.handleItemClick(chat)
    })
  }

  /**
   * 处理历史记录项点击事件
   */
  private handleItemClick(chat): void {
    // 显示Toast提示
    toast.success('提示词已复制', { duration: 2000 })

    // 触发自定义事件
    const event = new CustomEvent('echosync:history-item-click', {
      detail: { chat }
    })
    document.dispatchEvent(event)
  }

  /**
   * 更新聊天历史数据
   */
  public updateHistory(history: ChatPromptListResp[]): void {
    this.component.updateHistory(history)
  }

  /**
   * 显示气泡
   */
  public show(anchorElement: HTMLElement): void {
    this.component.show(anchorElement)
  }

  /**
   * 显示模态框
   * @param anchorElement 锚点元素（通常是悬浮球），模态框将从该元素位置展开
   */
  public showModal(anchorElement?: HTMLElement): void {
    this.component.show(anchorElement)
  }

  /**
   * 隐藏气泡
   */
  public hide(): void {
    this.component.hide()
  }

  /**
   * 切换显示状态
   */
  public toggle(anchorElement: HTMLElement): void {
    this.component.toggle(anchorElement)
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.component.destroy()
  }

  /**
   * 获取可见状态
   */
  public get visible(): boolean {
    return this.component.visible
  }
}
