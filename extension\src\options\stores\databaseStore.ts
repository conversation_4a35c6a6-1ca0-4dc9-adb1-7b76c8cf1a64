import { create } from 'zustand'
import { databaseViewProxy } from '@/common/service/DatabaseViewProxy'
import type { PaginatedResult } from '@/common/types/comm_vo'

// 表信息类型
export interface TableInfo {
  name: string
  displayName: string
  description: string
}

// 分页信息类型
export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}

// 数据库状态接口
interface DatabaseState {
  // 表相关状态
  tables: TableInfo[]
  currentTable: string | null
  
  // 数据相关状态
  tableData: any[]
  pagination: PaginationInfo
  
  // UI状态
  loading: boolean
  error: string | null
  
  // Actions
  loadTables: () => Promise<void>
  selectTable: (tableName: string) => Promise<void>
  loadTableData: (tableName: string, page?: number, limit?: number) => Promise<void>
  changePage: (page: number) => Promise<void>
  refreshData: () => Promise<void>
  deleteRecord: (recordData: any) => Promise<void>
  clearTable: () => Promise<void>
  clearError: () => void
  reset: () => void
}

// 初始状态
const initialState = {
  tables: [],
  currentTable: null,
  tableData: [],
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  },
  loading: false,
  error: null
}

/**
 * 数据库查看页面状态管理store
 *
 * 使用zustand管理数据库查看页面的状态，包括：
 * - 表信息和当前选中的表
 * - 表数据和分页信息
 * - 加载状态和错误处理
 * - 数据操作的action方法
 */
export const useDatabaseStore = create<DatabaseState>((set, get) => ({
  ...initialState,

  /**
   * 加载所有表信息
   */
  loadTables: async () => {
    set({ loading: true, error: null })
    
    try {
      const result = await databaseViewProxy.getAllTables()
      
      if (result.success && result.data) {
        set({ 
          tables: result.data,
          loading: false 
        })
      } else {
        set({ 
          error: result.error || '加载表信息失败',
          loading: false 
        })
      }
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '加载表信息失败',
        loading: false 
      })
    }
  },

  /**
   * 选择表并加载数据
   */
  selectTable: async (tableName: string) => {
    const { currentTable } = get()
    
    // 如果选择的是当前表，不需要重新加载
    if (currentTable === tableName) {
      return
    }

    set({ 
      currentTable: tableName,
      pagination: { ...initialState.pagination } // 重置分页
    })
    
    await get().loadTableData(tableName, 1, 20)
  },

  /**
   * 加载表数据
   */
  loadTableData: async (tableName: string, page: number = 1, limit: number = 20) => {
    set({ loading: true, error: null })
    
    try {
      const result = await databaseViewProxy.getTableData(tableName, page, limit)
      
      if (result.success && result.data) {
        const paginatedResult = result.data as PaginatedResult<any>
        
        set({ 
          tableData: paginatedResult.data,
          pagination: {
            page: paginatedResult.page,
            limit: paginatedResult.limit,
            total: paginatedResult.total,
            totalPages: paginatedResult.totalPages
          },
          loading: false 
        })
      } else {
        set({ 
          error: result.error || '加载表数据失败',
          loading: false 
        })
      }
    } catch (error) {
      set({ 
        error: error instanceof Error ? error.message : '加载表数据失败',
        loading: false 
      })
    }
  },

  /**
   * 切换页面
   */
  changePage: async (page: number) => {
    const { currentTable, pagination } = get()
    
    if (!currentTable || page === pagination.page) {
      return
    }
    
    await get().loadTableData(currentTable, page, pagination.limit)
  },

  /**
   * 刷新当前数据
   */
  refreshData: async () => {
    const { currentTable, pagination } = get()

    if (!currentTable) {
      return
    }

    await get().loadTableData(currentTable, pagination.page, pagination.limit)
  },

  /**
   * 删除记录
   */
  deleteRecord: async (recordData: any) => {
    const { currentTable } = get()

    if (!currentTable || !recordData.id) {
      return
    }

    set({ loading: true, error: null })

    try {
      const result = await databaseViewProxy.deleteRecord(currentTable, recordData.id)

      if (result.success) {
        // 删除成功后刷新数据
        await get().refreshData()
      } else {
        set({
          error: result.error || '删除记录失败',
          loading: false
        })
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '删除记录失败',
        loading: false
      })
    }
  },

  /**
   * 清空当前表的所有数据
   */
  clearTable: async () => {
    const { currentTable } = get()

    if (!currentTable) {
      return
    }

    set({ loading: true, error: null })

    try {
      const result = await databaseViewProxy.clearTable(currentTable)

      if (result.success) {
        // 清空成功后刷新数据
        await get().refreshData()
      } else {
        set({
          error: result.error || '清空表数据失败',
          loading: false
        })
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : '清空表数据失败',
        loading: false
      })
    }
  },

  /**
   * 清除错误信息
   */
  clearError: () => {
    set({ error: null })
  },

  /**
   * 重置状态
   */
  reset: () => {
    set(initialState)
  }
}))

// 导出类型
export type { DatabaseState }
