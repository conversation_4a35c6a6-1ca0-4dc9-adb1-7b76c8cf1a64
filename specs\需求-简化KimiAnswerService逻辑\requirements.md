# 简化KimiAnswerService逻辑需求文档

## 1. 需求概述

基于当前的架构设计，将Kimi答案捕获模块重构为更清晰的Controller-Service架构模式。由于现在采用销毁逻辑，每次新页面都会重新绑定监听器，因此简化了页面跳转处理逻辑，只需要区分home页和chat页即可。

## 2. 核心需求

### 2.1 架构重构

**目标**: 将现有的单一服务类重构为Controller-Service分层架构

**具体调整**:
1. `KimiAnswerService` → `KimiAnswerController` 
   - 职责：流程编排和控制逻辑
   - 只保留核心流程逻辑，实现细节迁移到Service层
   
2. 其他类重命名为Service模式：
   - `KimiConversationManager` → `KimiConversationService` 
   - `KimiPageDetector` → `KimiPageService`
   - `KimiClipboardManager` → `KimiClipboardService`

### 2.2 页面类型识别

**基于URL区分页面类型**:

- **Home页**: `https://www.kimi.com` 
  - 特征：域名后无额外路径或仅有简单路径
  - 行为：只打印日志，不开启监听
  
- **Chat页**: `https://www.kimi.com/chat/{chat_id}`
  - 特征：chat后面跟随编码，该编码是聊天列表的唯一标识符
  - 示例：`https://www.kimi.com/chat/d2qr08fkgnun211bap2g`
  - 行为：开启完整的问答监听功能

### 2.3 监听策略调整

**Home页处理**:
- 仅输出调试信息
- 不启动任何DOM监听器
- 保持轻量级运行状态

**Chat页处理**:
- 启动完整的监听机制
- 监听DOM结构：
  - `class="main"` 作为根容器
  - `class="chat-page chat"` 作为聊天页面标识
  - `class="chat-header"` 获取聊天标题
  - `class="chat-content-container"` 作为聊天内容区域
  - `class="chat-content-list"` 作为聊天内容列表容器

## 3. 技术实现要求

### 3.1 Controller层设计

**KimiAnswerController职责**:
- 初始化流程控制
- 页面类型判断和路由
- Service层协调
- 错误处理和日志记录
- 资源生命周期管理

### 3.2 Service层设计

**各Service的职责分工**:

1. **KimiPageService**:
   - URL解析和页面类型识别
   - 页面状态监测
   - 页面跳转事件处理
   - 基于URL模式匹配页面类型

2. **KimiConversationService**: 
   - 对话数据管理
   - 问答对存储和组织
   - 数据清理和同步
   - 对话标题提取和维护


3. **KimiClipboardService**:
   - 剪贴板操作管理
   - 内容提取优化(通过点击复制获取Markdown)
   - 降级处理机制
   - 剪贴板权限管理

4. **KimiAnswerService**(新增):
   - 答案监听和捕捉逻辑
   - MutationObserver管理
   - 问答对匹配和关联
   - 答案完成事件处理

### 3.3 DOM监听逻辑详述

**核心DOM结构和监听机制**:

#### 3.3.1 页面结构层次
- `class="main"` - 页面主容器
- `class="chat-page chat"` - 聊天页面标识
- `class="chat-header"` - 聊天标题区域
- `class="chat-content-container"` - 聊天内容容器
- `class="chat-content-list"` - 聊天内容列表（核心监听目标）

#### 3.3.2 问答对结构识别
**问答对在DOM中的表现**:
- `class="chat-content-item chat-content-item-user"` - 用户问题节点
- `class="chat-content-item chat-content-item-assistant"` - AI答案节点
- 问答对按时间顺序依次排列，成对存在

#### 3.3.3 答案完成状态检测
**答案加载完成的标识**:
- 当监听到新增 `class="chat-content-item chat-content-item-assistant"` 时
- 需要同步捕捉其内部的 `class="segment-assistant-actions-content"` 节点
- 该节点存在表明答案已完全加载完毕
- 点击该节点会触发Kimi的内置复制功能

#### 3.3.4 内容提取机制优化
**新的内容获取方案**:
- **传统方案**: 递归解析HTML结构提取文本（复杂且易出错）
- **优化方案**: 利用Kimi内置复制功能
  1. 检测到 `segment-assistant-actions-content` 节点
  2. 通过代码模拟点击该节点
  3. 监听剪贴板变化，直接获取Markdown格式内容
  4. 无需HTML解析，直接得到结构化的Markdown文本

**优势**:
- 避免复杂的HTML递归解析逻辑
- 获取的内容格式与Kimi原生复制一致
- 减少DOM结构变化带来的兼容性问题
- 提高内容提取的准确性和稳定性

## 4. 监听机制实现细节

### 4.1 监听目标和事件

**主要监听容器**: `class="chat-content-list"`
- 作为聊天内容的核心容器
- 包含所有问答对的DOM节点
- MutationObserver的主要监听目标

**监听事件类型**: 
- `childList: true` - 监听子节点的添加和删除
- `subtree: true` - 监听整个子树的变化

### 4.2 问答对识别规则

**问题识别**:
- 选择器: `class="chat-content-item chat-content-item-user"`
- 表示用户输入的问题内容
- 按时间顺序在对话列表中出现

**答案识别**:
- 选择器: `class="chat-content-item chat-content-item-assistant"`
- 表示AI生成的答案内容
- 紧跟在对应问题之后出现

**配对规则**:
- 问答按时间顺序依次排列
- 每个assistant节点对应前一个user节点
- 成对存在，保持一一对应关系

### 4.3 答案完成状态检测

**完成标志节点**: `class="segment-assistant-actions-content"`

**检测逻辑**:
1. 监听到新增 `chat-content-item-assistant` 节点时
2. 等待其内部 `segment-assistant-actions-content` 节点出现
3. 该节点存在表明答案已完全加载完毕
4. 此时可以安全地进行内容提取操作

**时序要求**:
- 必须等待 `segment-assistant-actions-content` 出现后再提取
- 过早提取可能获取到不完整的答案内容
- 需要设置合理的等待超时机制

### 4.4 内容提取新机制

**传统方法的问题**:
- 需要递归遍历复杂的HTML DOM结构
- 解析markdown、代码块等复杂内容困难
- 容易因DOM结构变化而失效
- 提取的格式与实际显示不一致

**新方法优势**:
- 利用Kimi内置的复制功能
- 自动处理所有格式转换
- 获取标准的Markdown格式内容
- 避免复杂的DOM解析逻辑

**实现步骤**:
1. 检测到 `segment-assistant-actions-content` 节点
2. 通过JavaScript代码模拟点击该节点
3. 触发Kimi的内置复制到剪贴板功能
4. 监听剪贴板内容变化
5. 获取复制到剪贴板的Markdown内容
6. 将获取的内容与对应问题配对存储

**异常处理**:
- 剪贴板权限检查和处理
- 复制操作失败的降级方案
- 超时处理和重试机制
- 内容为空或格式错误的处理

## 5. 实施步骤

### 5.1 第一阶段：重命名和重构

1. 重命名现有文件：
   - `KimiAnswerService.ts` → `KimiAnswerController.ts`
   - `KimiConversationManager.ts` → `KimiConversationService.ts`  
   - `KimiPageDetector.ts` → `KimiPageService.ts`

   - `KimiClipboardManager.ts` → `KimiClipboardService.ts`

### 5.2 第二阶段：Controller层简化

1. 提取Controller中的实现细节到对应Service
2. Controller只保留流程编排逻辑
3. 简化Controller的方法和状态管理

### 5.3 第三阶段：页面类型识别

1. 在 `KimiPageService` 中实现URL解析
2. 添加基于URL的页面类型识别
3. 替换现有的DOM检测方式

### 5.4 第四阶段：监听策略优化

1. 在Controller中根据页面类型决定监听策略
2. Home页只输出日志
3. Chat页启动完整监听

### 5.5 第五阶段：内容提取优化

1. 实现新的剪贴板基础的内容提取机制
2. 替换原有的HTML解析逻辑
3. 添加答案完成状态监测
4. 完善异常处理和降级机制

## 6. 预期效果

### 6.1 架构优势

- **职责清晰**: Controller负责流程，Service负责实现
- **代码可维护性提升**: 单一职责原则，易于修改和扩展
- **测试友好**: 分层架构便于单元测试
- **复用性增强**: Service层可以被其他模块复用

### 6.2 性能优化

- **资源节约**: Home页不启动监听器，减少资源消耗
- **精准监听**: 只在需要的页面启动监听
- **简化逻辑**: 去除复杂的页面跳转处理
- **内容提取优化**: 直接获取Markdown，避免HTML解析开销

### 6.3 维护优势

- **逻辑清晰**: 页面类型判断基于简单的URL模式
- **易于调试**: 分层架构便于问题定位
- **扩展方便**: 新增页面类型或功能更容易实现

## 7. 风险评估

### 7.1 潜在风险

- URL模式变化风险：Kimi可能修改URL结构
- 兼容性风险：重构可能引入新的bug
- 依赖关系：需要确保所有引用都正确更新

### 7.2 风险缓解

- 保留原有DOM检测作为备选方案
- 充分测试各种页面场景
- 分步骤实施，每步验证功能正常

## 8. 验收标准

1. **功能完整性**: 重构后所有原有功能正常工作
2. **架构清晰性**: Controller-Service职责分离明确
3. **性能提升**: Home页资源消耗明显降低
4. **代码质量**: 符合项目编码规范和设计模式
5. **测试覆盖**: 关键功能有对应的单元测试

## 9. 相关文件清单

### 9.1 需要修改的文件

- `extension/src/content/adapters/kimi/KimiAnswerService.ts` → `KimiAnswerController.ts`
- `extension/src/content/adapters/kimi/KimiConversationManager.ts` → `KimiConversationService.ts`
- `extension/src/content/adapters/kimi/KimiPageDetector.ts` → `KimiPageService.ts`  
- `extension/src/content/adapters/kimi/KimiClipboardManager.ts` → `KimiClipboardService.ts`

### 9.2 可能需要更新引用的文件

- `extension/src/content/adapters/kimi/kimi.ts`
- 其他引用这些类的文件

## 10. 开发时间估算

- **第一阶段（重命名）**: 0.5天
- **第二阶段（Controller重构）**: 1天  
- **第三阶段（页面识别）**: 0.5天
- **第四阶段（监听优化）**: 0.5天
- **第五阶段（内容提取优化）**: 1天
- **测试和调试**: 0.5天

**总计**: 约4天工作量