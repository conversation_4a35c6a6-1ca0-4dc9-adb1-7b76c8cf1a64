import { PageState } from "../types/ExportTypes";


/**
 * 页面服务基类
 * 提供页面状态检测的通用实现，子类可覆盖特定方法
 */
export abstract class BasePageService {
  /**
   * 平台名称（子类需要提供）
   */
  protected abstract getPlatformName(): string;

  /**
   * 获取 URL 匹配模式（子类需要提供）
   * 返回用于匹配页面类型的正则表达式
   */
  protected abstract getUrlPatterns(): {
    home: RegExp;
    chat: RegExp;
  };

  /**
   * 从 URL 中提取 chatId（子类可覆盖）
   * @param url 当前页面 URL
   * @returns chatId 或 null
   */
  protected extractChatIdFromUrl(url: string): string | null {
    const patterns = this.getUrlPatterns();
    const match = url.match(patterns.chat);
    return match?.[1] || null;
  }

  /**
   * 基于 URL 判断页面类型（通用实现）
   * @returns 页面类型信息
   */
  public getPageTypeFromUrl(): { isHome: boolean; isChat: boolean; chatId?: string } {
    const url = window.location.href;
    const patterns = this.getUrlPatterns();
    
    const isHome = patterns.home.test(url);
    const isChat = patterns.chat.test(url);
    const chatId = isChat ? this.extractChatIdFromUrl(url) : undefined;
    
    return { isHome, isChat, chatId };
  }

  /**
   * 检测欢迎页面（通用实现，子类可覆盖）
   * @returns 是否为欢迎页面
   */
  public isWelcomePage(): boolean {
    const urlResult = this.getPageTypeFromUrl();
    return urlResult.isHome;
  }

  /**
   * 检测聊天页面（通用实现，子类可覆盖）
   * @returns 是否为聊天页面
   */
  public isChatPage(): boolean {
    const urlResult = this.getPageTypeFromUrl();
    return urlResult.isChat;
  }

  /**
   * 获取当前页面状态（通用实现）
   * @returns 页面状态对象
   */
  public getCurrentPageState(): PageState {
    const urlResult = this.getPageTypeFromUrl();
    
    return {
      isWelcomePage: urlResult.isHome || this.isWelcomePage(),
      isChatPage: urlResult.isChat || this.isChatPage(),
      hasTransitioned: false,
      chatId: urlResult.chatId
    };
  }

  /**
   * 获取当前聊天 ID（通用实现）
   * @returns chatId 或 null
   */
  public getCurrentChatId(): string | null {
    const urlResult = this.getPageTypeFromUrl();
    return urlResult.chatId || null;
  }
}
