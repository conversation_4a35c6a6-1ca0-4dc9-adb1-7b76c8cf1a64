import React from 'react'

export function ManualPage() {
  const gettingStartedSteps = [
    {
      title: '1. 启用悬浮球',
      description: '在首页开启悬浮球功能，它会在网页上显示一个小球，方便快速访问AI功能。'
    },
    {
      title: '2. 开始对话',
      description: '点击悬浮球或使用快捷键开始与AI对话，支持文本输入和语音输入。'
    },
    {
      title: '3. 管理记录',
      description: '在历史页面查看所有对话记录，支持搜索、筛选和导出功能。'
    }
  ]

  const features = [
    {
      icon: '🔮',
      title: '智能悬浮球',
      description: '在任何网页上快速访问AI助手'
    },
    {
      icon: '💬',
      title: '对话管理',
      description: '自动保存和同步所有对话记录'
    },
    {
      icon: '📤',
      title: '多格式导出',
      description: '支持导出到Obsidian、Notion、Markdown'
    },
    {
      icon: '🔄',
      title: '多设备同步',
      description: '在所有设备间同步数据和设置'
    }
  ]

  const shortcuts = [
    { action: '打开/关闭悬浮球', key: 'Ctrl + Shift + A' },
    { action: '快速对话', key: 'Ctrl + Shift + C' },
    { action: '全屏模式', key: 'Ctrl + F' },
    { action: '打开设置', key: 'Ctrl + ,' }
  ]

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 快速开始 */}
      <div className="card fade-in">
        <div className="card-title">🚀 快速开始</div>
        <div className="card-content">
          {gettingStartedSteps.map((step, index) => (
            <div key={index} style={{ marginBottom: index < gettingStartedSteps.length - 1 ? '16px' : '0' }}>
              <div style={{ fontWeight: 600, marginBottom: '8px' }}>{step.title}</div>
              <div style={{ fontSize: '14px', color: 'var(--text-muted)', marginBottom: '12px' }}>
                {step.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 主要功能 */}
      <div className="card fade-in">
        <div className="card-title">✨ 主要功能</div>
        <div className="card-content">
          {features.map((feature, index) => (
            <div key={index} style={{ marginBottom: index < features.length - 1 ? '12px' : '0' }}>
              <div style={{ fontWeight: 600, marginBottom: '4px' }}>
                {feature.icon} {feature.title}
              </div>
              <div style={{ fontSize: '14px', color: 'var(--text-muted)' }}>
                {feature.description}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 快捷键 */}
      <div className="card fade-in">
        <div className="card-title">⌨️ 快捷键</div>
        <div className="card-content">
          {shortcuts.map((shortcut, index) => (
            <div 
              key={index} 
              style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                marginBottom: index < shortcuts.length - 1 ? '8px' : '0' 
              }}
            >
              <span style={{ fontWeight: 600 }}>{shortcut.action}</span>
              <span 
                style={{ 
                  background: 'rgba(15, 23, 42, 0.06)', 
                  padding: '2px 8px', 
                  borderRadius: '4px', 
                  fontSize: '12px' 
                }}
              >
                {shortcut.key}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
