import { NotionConfig } from '@/common/service/LocalStorageService';
import { CreatePageRequest, NotionApiResponse, NotionConfigValidation } from '@/common/types/NotionTypes';

/**
 * Notion API 代理服务
 * 在 Background Script 中调用 Notion API（避免 CORS）
 */
export class NotionApiService {
    private static readonly API_BASE = 'https://api.notion.com/v1';
    private static readonly API_VERSION = '2022-06-28'; // Notion API 版本

    /**
     * 验证 Notion 配置（Token 和 Page ID）
     */
    public static async validateConfig(config: NotionConfig): Promise<NotionConfigValidation> {
        try {
            console.log('[NotionApiService] 验证配置', { pageId: config.pageId });

            // 尝试获取 Page 信息
            const response = await fetch(`${this.API_BASE}/pages/${config.pageId}`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${config.token}`,
                    'Notion-Version': this.API_VERSION,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const error = await response.json();
                console.error('[NotionApiService] 验证失败', error);
                
                if (response.status === 401) {
                    return {
                        valid: false,
                        error: 'Token 无效或已过期'
                    };
                } else if (response.status === 404) {
                    return {
                        valid: false,
                        error: 'Page 不存在或无访问权限'
                    };
                } else {
                    return {
                        valid: false,
                        error: `验证失败: ${error.message || '未知错误'}`
                    };
                }
            }

            const pageData = await response.json();
            console.log('[NotionApiService] 验证成功', pageData);

            // 提取页面标题
            const pageTitle = this.extractPageTitle(pageData);

            return {
                valid: true,
                pageTitle
            };

        } catch (error) {
            console.error('[NotionApiService] 验证配置异常', error);
            return {
                valid: false,
                error: error instanceof Error ? error.message : '网络错误'
            };
        }
    }

    /**
     * 创建 Notion 页面
     */
    public static async createPage(
        config: NotionConfig,
        request: CreatePageRequest
    ): Promise<NotionApiResponse> {
        try {
            console.log('[NotionApiService] 创建页面', {
                parent: request.parent.page_id,
                title: request.properties.title.title[0]?.text.content
            });

            const response = await fetch(`${this.API_BASE}/pages`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${config.token}`,
                    'Notion-Version': this.API_VERSION,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(request)
            });

            if (!response.ok) {
                const error = await response.json();
                console.error('[NotionApiService] 创建页面失败', error);
                throw new Error(error.message || '创建页面失败');
            }

            const result = await response.json();
            console.log('[NotionApiService] 创建页面成功', result.id);

            return result;

        } catch (error) {
            console.error('[NotionApiService] 创建页面异常', error);
            throw error;
        }
    }

    /**
     * 提取页面标题
     */
    private static extractPageTitle(pageData: any): string {
        try {
            // Notion 页面标题在 properties.title 或 properties.Name
            const properties = pageData.properties || {};
            const titleProp = properties.title || properties.Name || properties.name;
            
            if (titleProp && titleProp.title && titleProp.title.length > 0) {
                return titleProp.title[0].plain_text || titleProp.title[0].text?.content || 'Untitled';
            }

            return 'Untitled';
        } catch (error) {
            console.error('[NotionApiService] 提取标题失败', error);
            return 'Untitled';
        }
    }
}
