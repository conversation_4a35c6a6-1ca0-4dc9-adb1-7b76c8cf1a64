# 技术方案设计

## 架构概述

采用依赖注入（DI）模式重构content模块，实现控制反转（IoC），解决模块间耦合问题。

## 技术栈

- **语言**: TypeScript 5
- **设计模式**: 依赖注入、单例模式、工厂模式
- **架构模式**: 控制反转（IoC）

## 核心组件设计

### 1. DI容器（DIContainer）

```typescript
interface DIContainer {
  register<T>(token: string, factory: () => T, singleton?: boolean): void;
  resolve<T>(token: string): T;
  clear(): void;
}
```

**职责**：
- 管理依赖的注册和解析
- 支持单例和瞬态生命周期
- 提供类型安全的依赖解析

### 2. 依赖标识符（Tokens）

```typescript
const TOKENS = {
  INPUT_MODEL: 'InputModel',
  // 未来可扩展其他模型
} as const;
```

**职责**：
- 定义依赖的唯一标识符
- 提供类型安全的token管理

### 3. 接口定义

```typescript
interface IInputModel {
  generateChatUid(): void;
  getCurrChatUid(): string;
  setChatUid(uid: string): void;
  clear(): void;
}
```

**职责**：
- 定义模型的契约接口
- 支持依赖倒置原则
- 便于测试时的mock实现

## 模块改造方案

### 1. InputCapture改造

**改造前**：
```typescript
export class InputCapture extends BaseCapture {
  private inputModel = InputModel.getInstance();
}
```

**改造后**：
```typescript
export class InputCapture extends BaseCapture {
  constructor(
    adapter: BaseAIAdapter,
    private inputModel: IInputModel
  ) {
    super();
    this.adapter = adapter;
  }
}
```

### 2. ArchiveButtonInject改造

**改造前**：
```typescript
export class ArchiveButtonInject {
  private inputModel = InputModel.getInstance();
}
```

**改造后**：
```typescript
export class ArchiveButtonInject {
  constructor(
    adapter: BaseAIAdapter,
    private inputModel: IInputModel
  ) {
    this.adapter = adapter;
  }
}
```

### 3. BaseAIAdapter改造

**新增DI容器配置**：
```typescript
export abstract class BaseAIAdapter {
  protected container: DIContainer;

  constructor() {
    this.container = new SimpleDIContainer();
    this.setupDependencies();
  }

  private setupDependencies(): void {
    // 注册InputModel为单例
    this.container.register(
      TOKENS.INPUT_MODEL,
      () => InputModel.getInstance(),
      true
    );
  }

  protected initCapture(): void {
    const inputModel = this.container.resolve<IInputModel>(TOKENS.INPUT_MODEL);
    this.inputCapture = new InputCapture(this, inputModel);
  }

  protected initInject(): void {
    const inputModel = this.container.resolve<IInputModel>(TOKENS.INPUT_MODEL);
    this.archiveButtonInject = new ArchiveButtonInject(this, inputModel);
  }
}
```

## 实现细节

### 1. 简单DI容器实现

```typescript
export class SimpleDIContainer implements DIContainer {
  private dependencies = new Map<string, { factory: () => any; singleton: boolean; instance?: any }>();

  register<T>(token: string, factory: () => T, singleton = false): void {
    this.dependencies.set(token, { factory, singleton });
  }

  resolve<T>(token: string): T {
    const dependency = this.dependencies.get(token);
    if (!dependency) {
      throw new Error(`Dependency not found: ${token}`);
    }

    if (dependency.singleton) {
      if (!dependency.instance) {
        dependency.instance = dependency.factory();
      }
      return dependency.instance;
    }

    return dependency.factory();
  }

  clear(): void {
    this.dependencies.clear();
  }
}
```

### 2. 文件结构

```
content/
├── di/
│   ├── DIContainer.ts        # DI容器接口
│   ├── SimpleDIContainer.ts  # 简单DI容器实现
│   ├── tokens.ts            # 依赖标识符
│   └── index.ts             # DI模块导出
├── interfaces/
│   ├── IInputModel.ts       # InputModel接口
│   └── index.ts             # 接口导出
├── model/
│   └── InputModel.ts        # 实现IInputModel接口
├── adapters/
│   └── BaseAIAdapter.ts     # 集成DI容器
├── capture/
│   └── InputCapture.ts      # 使用依赖注入
└── inject/
    └── ArchiveButtonInject.ts # 使用依赖注入
```

## 测试策略

### 1. 单元测试改进

```typescript
// 测试InputCapture
describe('InputCapture', () => {
  let mockInputModel: jest.Mocked<IInputModel>;
  let inputCapture: InputCapture;

  beforeEach(() => {
    mockInputModel = {
      generateChatUid: jest.fn(),
      getCurrChatUid: jest.fn().mockReturnValue('test-uid'),
      setChatUid: jest.fn(),
      clear: jest.fn()
    };
    
    inputCapture = new InputCapture(mockAdapter, mockInputModel);
  });

  it('should generate chat uid on send event', () => {
    inputCapture.handleSendEvent();
    expect(mockInputModel.generateChatUid).toHaveBeenCalled();
  });
});
```

### 2. 集成测试

- 测试DI容器的注册和解析功能
- 测试模块间的依赖注入是否正常工作
- 测试单例模式是否正确实现

## 迁移策略

### 1. 渐进式迁移

1. **第一阶段**：实现DI框架和接口
2. **第二阶段**：改造InputCapture模块
3. **第三阶段**：改造ArchiveButtonInject模块
4. **第四阶段**：更新BaseAIAdapter集成DI
5. **第五阶段**：清理旧代码和测试

### 2. 向后兼容

- 保持InputModel的单例模式不变
- 确保现有功能正常工作
- 逐步替换直接依赖为注入依赖

## 性能考虑

- DI容器使用Map存储，查找性能为O(1)
- 单例模式确保模型实例唯一性
- 避免过度抽象，保持简单实用

## 扩展性

- 支持添加新的模型和依赖
- 支持不同的生命周期管理
- 为未来的复杂依赖关系做准备
