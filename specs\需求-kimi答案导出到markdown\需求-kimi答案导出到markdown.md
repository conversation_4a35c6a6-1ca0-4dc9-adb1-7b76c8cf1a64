# 需求-kimi答案导出到markdown

## 原始需求
要求实现：markdown笔记平台的导出功能，与obsidian的流程一样。
1. 要在`src/content/inject/components`目录生成Markdown导出按钮，但不需要模态页，markdown的logo使用`markdown-mark.svg`。
2. 鼠标悬浮时，文字提示为"点击导出为标准markdown文件" 
3. 导出的md文件中不像obsidian，不需要元数据和模态页。
4. 文件句柄的流程，就与obsidian一致了。 
5. 总结文档生成到specs/需求-kimi答案导出到markdown目录下。

---

## 需求分析与讨论

### 已确认的信息
✅ `markdown-mark.svg` 已存在于 `src/content/inject/components/` 目录
✅ 可以参考 `ObsidianExportButton.ts` 的实现
✅ 可以参考 `ObsidianExportService.ts` 的文件句柄流程

### 需要明确的问题

#### 问题 1: 导出文件内容格式
您提到"导出的md文件中不像obsidian，不需要元数据和模态页"。

- **元数据（Frontmatter）**: Obsidian 导出的文件包含 YAML frontmatter：
  ```yaml
  ---
  title: xxx
  date: xxx
  tags: [xxx]
  source: Kimi Chat
  ---
  ```
  
  **请确认**: Markdown 导出是否需要这个 frontmatter？还是完全不要？

- **内容格式**: 当前 Obsidian 导出格式是：
  ```markdown
  # 标题
  
  ## 问题
  
  问题内容
  
  ## 答案
  
  答案内容
  ```
  
  **请确认**: Markdown 导出的内容格式是什么？
  - 选项 A: 只有问题和答案，不要标题结构
  - 选项 B: 保持相同的 "## 问题" 和 "## 答案" 结构
  - 选项 C: 其他自定义格式

#### 问题 2: UI 交互流程
您提到"不需要模态页"，即点击按钮直接导出。

**请确认**:
- 是否需要用户输入标题？（Obsidian 需要通过 Modal 输入标题和标签）
  - 如果不需要，标题从哪里来？（使用问题的前 50 个字符？使用默认格式如 "Kimi答案_时间戳"？）
- 是否需要标签功能？
  - 如果不需要，直接去掉
  
#### 问题 3: 文件命名规则
**请确认** Markdown 导出的文件命名格式：
- 选项 A: 使用问题前 N 个字符（如 `问题内容的前50字.md`）
- 选项 B: 使用时间戳（如 `2025-10-07-154230.md`）
- 选项 C: 固定前缀 + 时间戳（如 `Kimi答案-2025-10-07-154230.md`）
- 选项 D: 其他格式

#### 问题 4: Toast 提示
**请确认** 导出成功/失败的提示文案：
- 成功提示：`已导出为标准 Markdown 文件` ？
- 失败提示：`Markdown 导出失败，请重试` ？

---

## 我的建议方案（待您确认）

基于简化的原则，我建议：

### 方案 A：最简化版本
1. **内容格式**: 不要 frontmatter，只保留问题和答案
   ```markdown
   ## 问题
   
   问题内容
   
   ## 答案
   
   答案内容
   ```

2. **文件命名**: 使用时间戳格式 `markdown-export-2025-10-07-154230.md`

3. **交互流程**: 点击按钮 → 直接导出（不输入标题/标签）

4. **Toast 提示**: 
   - 成功：`✅ 已导出为 Markdown 文件`
   - 失败：`❌ 导出失败，请重试`

### 方案 B：保留部分结构
1. **内容格式**: 保留简化的 frontmatter + 问题答案
   ```markdown
   ---
   date: 2025-10-07 15:42:30
   ---
   
   ## 问题
   
   问题内容
   
   ## 答案
   
   答案内容
   ```

2. 其他同方案 A

---

**请您选择方案或提出您的具体要求，我将据此实现。**

---

## ✅ 需求确认

### 最终方案
1. **内容格式**: 不要 frontmatter，保持 "## 问题" 和 "## 答案" 结构
   ```markdown
   ## 问题
   
   问题内容
   
   ## 答案
   
   答案内容
   ```

2. **文件命名**: 使用问题内容，取前 N 个字符（避免过长）
   - 规则：过滤特殊字符、限制长度

3. **交互流程**: 点击按钮 → 直接导出
   - ❌ 不需要用户输入标题
   - ❌ 不需要标签功能

4. **Toast 提示**: 
   - 成功：`Markdown 下载成功`
   - 失败：`Markdown 下载失败`

---

## 📝 实现计划

### 文件清单
1. ✅ `MarkdownExportButton.ts` - 导出按钮组件
2. ✅ `MarkdownExportButton.css` - 按钮样式
3. ✅ `MarkdownExportService.ts` - 导出服务（简化版）
4. ✅ 更新相关文件以集成 Markdown 导出功能

### 开始实现...

---

## 🎉 实现完成

### 已创建文件

#### 1. 按钮组件
- ✅ `src/content/inject/components/MarkdownExportButton.ts`
- ✅ `src/content/inject/components/MarkdownExportButton.css`

#### 2. 导出服务
- ✅ `src/content/service/MarkdownExportService.ts`

#### 3. 注入器
- ✅ `src/content/inject/MarkdownExportInject.ts`

#### 4. 集成
- ✅ `src/content/adapters/kimi/KimiAnswerController.ts`（已修改）

### 功能特性

#### ✅ 内容格式
```markdown
## 问题

问题内容

## 答案

答案内容
```
- 不包含 YAML frontmatter
- 纯标准 Markdown 格式

#### ✅ 文件命名
- 使用问题内容（前 50 字符）
- 自动过滤特殊字符
- 自动处理重名

#### ✅ 交互流程
- 点击按钮 → 直接导出
- 无需输入标题和标签
- Toast 提示成功/失败

#### ✅ 文件存储
- 平台: `markdown`
- 独立于 Obsidian/Notion
- 使用 FileSystemService

### 详细文档
📄 完整实现文档已生成：`specs/需求-kimi答案导出到markdown/complete.md`

---

## 💡 测试说明

### 如何测试

1. **重新加载扩展**
   - 访问 `chrome://extensions`
   - 找到 EchoSync 扩展
   - 点击"重新加载"按钮

2. **访问 Kimi Chat**
   - 打开 https://kimi.moonshot.cn
   - 创建或打开一个对话

3. **查看按钮**
   - 在每个 AI 回答旁边
   - 应该看到两个按钮：
     - Obsidian Logo（紫色）
     - Markdown Logo（黑白）

4. **测试导出**
   - 鼠标悬浮 Markdown 按钮
   - 查看 Tooltip："点击导出为标准markdown文件"
   - 点击按钮
   - 首次会提示选择目录
   - 查看导出的文件内容

5. **验证内容**
   - 文件名使用问题内容
   - 文件内容只包含 "## 问题" 和 "## 答案"
   - 不包含元数据

### 预期结果

- ✅ 按钮正常显示
- ✅ Tooltip 显示正确
- ✅ 点击直接导出
- ✅ 文件内容格式正确
- ✅ Toast 提示显示
- ✅ 与 Obsidian 导出独立

---

## 🎯 下一步

所有代码已完成并编译成功，请按照上述测试步骤进行验证。