/* Notion 导出按钮样式 */

/* CSS 变量定义 */
:root {
  --notion-primary: #000000;
  --notion-secondary: #37352F;
  --notion-text: #1F2937;
  --notion-text-light: #6B7280;
  --notion-border: #E5E7EB;
  --notion-bg: #FFFFFF;
  --notion-bg-hover: #F3F4F6;
  
  --notion-spacing-xs: 4px;
  --notion-spacing-sm: 8px;
  --notion-spacing-md: 16px;
  
  --notion-radius-sm: 4px;
  
  --notion-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  /* 暗色模式适配 - 图标颜色 */
  --notion-icon-color: #374151;
  --notion-icon-hover: #000000;
  --notion-icon-stroke: rgba(255, 255, 255, 0.4);  /* 增强描边强度 */
  --notion-glow-color: rgba(16, 185, 129, 0.3);    /* 绿色外发光（Notion 品牌色系） */
}

/* 暗色模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --notion-primary: #FFFFFF;
    --notion-secondary: #E5E7EB;
    --notion-text: #F3F4F6;
    --notion-text-light: #9CA3AF;
    --notion-border: #374151;
    --notion-bg: #1F2937;
    --notion-bg-hover: #374151;
    
    --notion-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    
    /* 暗色模式 - 图标颜色 */
    --notion-icon-color: #9CA3AF;
    --notion-icon-hover: #FFFFFF;
    --notion-icon-stroke: rgba(255, 255, 255, 0.5);  /* 暗色模式下更强的描边 */
    --notion-glow-color: rgba(16, 185, 129, 0.5);    /* 暗色模式下更强的外发光 */
  }
}

/* 按钮基础样式 */
.notion-export-btn {
  /* 布局 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  /* 尺寸 */
  width: 32px;
  height: 32px;
  padding: 0;
  
  /* 外观 */
  background: transparent;
  border: none;
  border-radius: var(--notion-radius-sm);
  color: var(--notion-icon-color);
  
  /* 交互 */
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* 重置 */
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  /* 闪烁动画 */
  animation: notion-pulse 2s ease-in-out infinite;
  
  /* 增强外发光效果 - 让图标在任何背景下都醒目 */
  filter: drop-shadow(0 0 2px var(--notion-icon-stroke))
          drop-shadow(0 0 4px var(--notion-glow-color))
          drop-shadow(0 0 6px var(--notion-glow-color));
}

/* 闪烁动画关键帧 */
@keyframes notion-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 悬浮状态 - 停止闪烁 */
.notion-export-btn:hover {
  background: var(--notion-bg-hover);
  color: var(--notion-icon-hover);
  box-shadow: var(--notion-shadow-sm);
  animation: none; /* 悬浮时停止闪烁 */
  
  /* 悬浮时加强外发光效果 */
  filter: drop-shadow(0 0 3px var(--notion-icon-stroke))
          drop-shadow(0 0 6px var(--notion-glow-color))
          drop-shadow(0 0 10px var(--notion-glow-color));
}

/* 激活状态 */
.notion-export-btn:active {
  transform: scale(0.95);
}

/* 禁用状态 */
.notion-export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* SVG 图标样式 */
.notion-export-btn svg {
  display: block;
  width: 20px;
  height: 20px;
  pointer-events: none;
}

/* 自定义 Tooltip 容器 */
.notion-export-btn {
  position: relative;
}

/* 自定义 Tooltip 提示 */
.notion-tooltip {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  
  padding: 6px 12px;
  
  background: var(--notion-text);
  color: var(--notion-bg);
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  border-radius: var(--notion-radius-sm);
  
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  
  pointer-events: none;
  z-index: 10000;
}

/* Tooltip 箭头 */
.notion-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  
  border: 4px solid transparent;
  border-top-color: var(--notion-text);
}

/* 按钮悬浮时显示 Tooltip */
.notion-export-btn:hover .notion-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --notion-primary: #FFFFFF;
    --notion-secondary: #E5E7EB;
    --notion-text: #F9FAFB;
    --notion-text-light: #9CA3AF;
    --notion-border: #374151;
    --notion-bg: #1F2937;
    --notion-bg-hover: #374151;
  }
  
  .notion-tooltip {
    background: var(--notion-bg);
    color: var(--notion-text);
  }
  
  .notion-tooltip::after {
    border-top-color: var(--notion-bg);
  }
}
