# 权限管理系统设计文档

## 概述

为了确保 EchoSync 扩展能够正常使用剪贴板功能，我们实现了一个完整的权限管理系统。该系统在插件加载时主动申请权限，并提供多种降级和重试策略。

## 架构设计

### 1. PermissionManager（权限管理器）

**位置**: `src/content/utils/PermissionManager.ts`

**功能**:
- 单例模式，全局唯一实例
- 管理剪贴板权限的申请、检查和状态维护
- 提供智能重试策略

**核心方法**:

```typescript
// 检查并请求权限（智能模式）
checkAndRequestPermission(): Promise<boolean>

// 请求剪贴板权限
requestClipboardPermission(): Promise<boolean>

// 用户交互时请求权限
requestPermissionOnUserInteraction(): Promise<boolean>

// 设置自动重试策略
setupAutoRetryOnUserInteraction(): void

// 检查权限状态
isClipboardPermissionGranted(): boolean
```

### 2. index.ts（入口文件集成）

**位置**: `src/content/index.ts`

**初始化流程**:

```
1. 加载 Content Script
   ↓
2. 请求剪贴板权限 (checkAndRequestPermission)
   ↓
3a. ✅ 权限授予 → 继续初始化
3b. ❌ 权限未授予 → 设置自动重试策略
   ↓
4. 初始化 ContentScriptManager
   ↓
5. 应用正常运行
```

## 权限请求策略

### 策略 1: 页面加载时立即请求

**时机**: Content Script 加载后立即执行

**方法**: `permissionManager.checkAndRequestPermission()`

**特点**:
- 最早尝试获取权限
- 可能因为文档未聚焦而失败
- 失败不影响后续流程

### 策略 2: 用户交互时自动重试

**时机**: 用户首次点击页面或聚焦时

**方法**: `permissionManager.setupAutoRetryOnUserInteraction()`

**特点**:
- 自动监听 `click` 和 `focus` 事件
- 权限授予后自动移除监听器
- 提高权限授予成功率

### 策略 3: 三种权限检查方式

#### 方式 1: Permissions API（标准方式）
```typescript
const status = await navigator.permissions.query({ 
    name: 'clipboard-read' 
});
```

- ✅ 标准 API，推荐使用
- ✅ 可查询权限状态（granted / denied / prompt）
- ❌ 部分浏览器不支持

#### 方式 2: 直接访问剪贴板（触发提示）
```typescript
await navigator.clipboard.readText();
```

- ✅ 触发浏览器权限提示
- ✅ 兼容性好
- ❌ 需要文档聚焦

#### 方式 3: 焦点管理（提高成功率）
```typescript
window.focus();
await navigator.clipboard.readText();
```

- ✅ 主动获取焦点
- ✅ 提高权限授予成功率
- ✅ 配合用户交互效果最佳

## 用户体验设计

### 权限状态提示

#### 1. 权限被拒绝时
显示友好的提示通知：
- 🎨 红色警告样式
- 📝 清晰的说明文字
- 🔧 提供解决方案指引
- ⏱️ 5秒后自动消失

#### 2. 权限需要用户交互
控制台日志输出：
```
[PermissionManager] ⏳ 剪贴板权限需要用户交互授予
[PermissionManager] 设置自动重试策略
```

#### 3. 权限授予成功
控制台日志输出：
```
[PermissionManager] ✅ 剪贴板权限已授予
[PermissionManager] ✅ 权限授予成功，移除用户交互监听器
```

## 降级方案

### 层级 1: 剪贴板 API
- 优先使用官方提供的剪贴板内容
- 格式完整、准确

### 层级 2: DOM 提取
- 从页面 DOM 提取 markdown 内容
- 使用 Turndown + 自定义规则
- 保证功能可用

### 层级 3: 功能降级
- 即使没有剪贴板权限
- 仍可使用 DOM 提取功能
- 不影响核心功能

## 日志系统

### 日志级别

**info**: 正常流程
```typescript
console.info('[PermissionManager] 开始请求剪贴板权限')
console.info('[EchoSync] 步骤 1/3: 请求剪贴板权限')
```

**warn**: 警告信息
```typescript
console.warn('[PermissionManager] 文档未获得焦点，无法请求剪贴板权限')
console.warn('[EchoSync] ⚠️ 剪贴板权限未授予，将在用户交互时重试')
```

**error**: 错误信息
```typescript
console.error('[PermissionManager] 请求剪贴板权限失败:', error)
console.error('[EchoSync] Failed to initialize app:', error)
```

## 最佳实践

### 1. 权限请求时机
- ✅ 页面加载时尝试
- ✅ 用户交互时重试
- ❌ 不要频繁请求

### 2. 错误处理
- ✅ 区分错误类型（NotAllowedError / NotFoundError）
- ✅ 提供友好的用户提示
- ✅ 记录详细的日志信息

### 3. 焦点管理
- ✅ 检查 `document.hasFocus()`
- ✅ 必要时调用 `window.focus()`
- ✅ 配合用户交互效果更好

### 4. 状态维护
- ✅ 缓存权限状态（避免重复请求）
- ✅ 监听权限变化事件
- ✅ 提供状态查询接口

## 兼容性

### 支持的浏览器
- ✅ Chrome 66+
- ✅ Edge 79+
- ✅ Safari 13.1+
- ✅ Firefox 63+

### 降级支持
- 不支持 Clipboard API → 仅使用 DOM 提取
- 不支持 Permissions API → 直接尝试访问剪贴板
- 权限被拒绝 → 使用 DOM 提取作为备选方案

## 测试要点

### 1. 正常流程测试
- [ ] 页面加载时成功获取权限
- [ ] 剪贴板功能正常工作
- [ ] 日志输出清晰

### 2. 权限拒绝测试
- [ ] 拒绝权限后显示提示
- [ ] 自动重试机制生效
- [ ] 降级到 DOM 提取

### 3. 焦点问题测试
- [ ] 后台标签页打开（无焦点）
- [ ] 用户点击后获取权限
- [ ] DevTools 打开时的行为

### 4. SPA 路由测试
- [ ] 路由切换后权限状态保持
- [ ] 不会重复请求权限

## 未来优化

### 1. 权限状态持久化
使用 `chrome.storage.local` 缓存权限状态

### 2. 用户设置面板
允许用户手动触发权限请求

### 3. 更智能的提示
根据用户行为自适应提示策略

### 4. 权限撤销检测
监听权限被撤销的情况，及时通知用户

## 总结

通过完整的权限管理系统，EchoSync 能够：
- ✅ 主动请求必要的剪贴板权限
- ✅ 提供多层次的降级方案
- ✅ 优化用户体验（友好提示 + 智能重试）
- ✅ 确保功能稳定可用

该系统已集成到 `index.ts` 入口文件，所有子类（Kimi、ChatGPT 等）都能受益于统一的权限管理策略。
