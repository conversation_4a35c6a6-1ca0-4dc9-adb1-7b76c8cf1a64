
// EchoSync 自定义事件枚举
export enum EchoSyncEventEnum {
  // 输入框获得焦点时触发
  INPUT_FOCUSED = 'echosync:input-focused',
  // 输入框内容变化时触发
  INPUT_CHANGED = 'echosync:input-changed',
  // 提示词发送时触发
  PROMPT_SEND = 'echosync:prompt-send',
  // 检查新消息时触发
  CHECK_NEW_MESSAGE = 'echosync:check-new-message',
  // 页面URL发生变化时触发
  PAGE_CHANGED = 'echosync:page-changed',
  // 元素吸附到边界时触发
  SNAP_TO_BOUNDARY = 'echosync:snap-to-boundary',
  // 点击悬浮气泡时触发
  FLOATING_BUBBLE_CLICKED = 'echosync:floating-bubble-clicked',
  // 显示已存储的提示词列表时触发
  SHOW_STORED_PROMPTS = 'echosync:show-stored-prompts',
  // 调试功能开关时触发
  DEBUG_FEATURES = 'echosync:debug-features',
  // 点击历史记录项时触发
  HISTORY_ITEM_CLICK = 'echosync:history-item-click',
  // 处理历史记录点击事件时触发
  HANDLE_HISTORY_CLICK = 'echosync:handle-history-click',

  // 答案提取完成时触发
  ANSWER_EXTRACTED = 'echosync:answer-extracted',
}