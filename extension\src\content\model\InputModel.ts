import { Singleton } from '@/common/base';

/**
 * 输入模型
 * 继承Singleton基类，自动拥有单例能力
 */
export class InputModel extends Singleton<InputModel> {

  private currPrompt: string = '';

  constructor() {
    super();
  }

  public setCurrPrompt(currentValue: string) {
    this.currPrompt = currentValue;
  }

  public getCurrPrompt(): string {
    return this.currPrompt;
  }

  /**
   * 清理数据
   */
  public clear(): void {
    this.currPrompt = '';
    console.info('【InputModel】 cleared');
  }

  // /**
  //  * 可选：实现destroy方法用于清理
  //  */
  // public destroy(): void {
  //   this.clear();
  // }
}

