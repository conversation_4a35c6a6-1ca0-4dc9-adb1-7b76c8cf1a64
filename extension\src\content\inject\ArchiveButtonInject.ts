import { ArchiveButton } from '@/content/inject/components/ArchiveButton';
import { BaseAIAdapter } from '@/content/core/BaseAIAdapter';
import { EchoSyncEventEnum } from '../types/DOMEnum';
import ArchiveService from '../service/ArchiveService';
import { PlatformEntity } from '@/common/types/database_entity';

/**
 * 存档按钮注入逻辑
 * 使用简单的构造函数依赖注入
 */
export class ArchiveButtonInject {
  public component: ArchiveButton;  // 改为public，允许外部访问

  private adapter: BaseAIAdapter | null = null;
  private resizeObserver: ResizeObserver | null = null;
  private resizeTimeout: NodeJS.Timeout | null = null;
  private archiveService: ArchiveService;

  constructor(
    adapter: BaseAIAdapter,
  ) {
    this.adapter = adapter;
    
    // 正确获取 ArchiveService 单例实例
    this.archiveService = ArchiveService.getInstance();
    
    // 验证 archiveService 是否正确初始化
    if (!this.archiveService) {
      console.error('【EchoSync】ArchiveButtonInject: ArchiveService initialization failed');
      throw new Error('ArchiveService initialization failed');
    }
    
    console.log('【EchoSync】ArchiveButtonInject: ArchiveService initialized successfully');
    
    this.component = new ArchiveButton();
    this.setupEventListeners();
    this.inject();
  }

  /**
   * 注入存档按钮到页面
   */
  async inject(): Promise<void> {
    if (!this.adapter) {
      console.warn('【EchoSync】ArchiveButtonInject: No adapter instance available')
      return
    }

    // 渲染UI组件
    const buttonElement = this.component.render()
    document.body.appendChild(buttonElement)

    // 设置点击事件
    this.component.onClick(()=>this.onClickArchive())

    // 初始定位
    this.repositionButton()
  }

  /**
   * 点击存档按钮
   */
  public onClickArchive(): void{
      const inputPrompt = this.adapter.getInputCapture().getCurrentInput()
      const platformResult: PlatformEntity = this.adapter.getCurrentPlatform()
      this.archiveService.archivePrompt(inputPrompt,platformResult.id).then(() => {
          // 显示存档动画
          this.component.showArchivedState()
          this.component.showArchiveAnimation()
      })
  }


  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听输入框变化事件
    document.addEventListener(EchoSyncEventEnum.INPUT_CHANGED, (e: CustomEvent) => {
      const value = e.detail.value
      this.updateButtonState(value)
    })

    // 监听提示词发送事件
    document.addEventListener('echosync:prompt-sent', (e: CustomEvent) => {
      const { chatUid, timestamp } = e.detail
      console.log('【ArchiveButtonInject】Prompt sent event received:', { chatUid, timestamp })
      this.showArchivedState()
    })
  }

  /**
   * 重新定位按钮
   */
  private repositionButton(): void {
    if (!this.adapter) return
    // 直接查找输入元素
    const inputElement = this.adapter.getInputCapture().getInputElement()
    this.positionButton(inputElement)
  }

  /**
   * 定位存档按钮
   */
  private positionButton(inputElement: HTMLElement): void {
    const updatePosition = () => {
      const rect = inputElement.getBoundingClientRect()
      const buttonSize = 25
      const margin = 8

      let left = rect.right + margin
      let top = rect.bottom - buttonSize

      // 边界检查
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      left = Math.max(margin, Math.min(left, windowWidth - buttonSize - margin))
      top = Math.max(margin, Math.min(top, windowHeight - buttonSize - margin))

      this.component.updatePosition(left, top)
    }

    updatePosition()

    // 防抖处理
    const debouncedUpdate = () => {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout)
      }
      this.resizeTimeout = setTimeout(updatePosition, 100)
    }

    window.addEventListener('resize', debouncedUpdate)
    window.addEventListener('scroll', debouncedUpdate)

    // 观察容器变化
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
    this.resizeObserver = new ResizeObserver(debouncedUpdate)
    this.resizeObserver.observe(inputElement)

    if (inputElement.parentElement) {
      this.resizeObserver.observe(inputElement.parentElement)
    }
  }

  /**
   * 更新按钮状态
   */
  private updateButtonState(inputValue?: string): void {
    // 检查 archiveService 是否可用
    if (!this.archiveService) {
      console.error('【EchoSync】ArchiveService is not available');
      this.component.hide();
      return;
    }

    let content = inputValue
    const hasContent = (content || '').trim().length > 0

    // 检查是否已存档
    const isArchived = this.archiveService.isArchivedPrompt(content)

    if (hasContent && !this.archiveService.isArchivedPrompt(content)) {
      // 有内容且未存档
      this.component.showWithGlow()
    } else if (isArchived) {
      // 已存档
      this.component.showArchivedState()
    } else {
      // 无内容或已存档
      this.component.hide()
    }
  }

  /**
   * 显示已存档状态
   */
  private showArchivedState(): void {
    this.component.showArchivedState()
    console.log('【ArchiveButtonInject】Showing archived state')
  }

  /**
   * 销毁注入器
   */
  destroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout)
      this.resizeTimeout = null
    }

    this.component.destroy()
    this.archiveService.destroy()
    this.archiveService = null

    // 清理数据
    this.adapter = null
  }
}