/**
 * 权限管理器
 * 负责申请和管理浏览器权限（特别是剪贴板权限）
 */
export class PermissionManager {
    private static instance: PermissionManager | null = null;
    private clipboardPermissionGranted: boolean = false;

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): PermissionManager {
        if (!PermissionManager.instance) {
            PermissionManager.instance = new PermissionManager();
        }
        return PermissionManager.instance;
    }

    /**
     * 请求剪贴板读取权限
     * @returns 是否授予权限
     */
    public async requestClipboardPermission(): Promise<boolean> {
        try {
            // console.info('[PermissionManager] 开始请求剪贴板权限');

            // 检查浏览器是否支持 Clipboard API
            if (!navigator.clipboard) {
                console.warn('[PermissionManager] 浏览器不支持 Clipboard API');
                return false;
            }

            // 检查权限 API 是否可用
            if (!navigator.permissions || !navigator.permissions.query) {
                console.warn('[PermissionManager] 浏览器不支持 Permissions API');
                // 尝试直接读取剪贴板来触发权限提示
                return await this.tryClipboardAccess();
            }

            // 查询剪贴板权限状态
            const permissionStatus = await navigator.permissions.query({ 
                name: 'clipboard-read' as PermissionName 
            });

            // console.info('[PermissionManager] 剪贴板权限状态:', permissionStatus.state);

            if (permissionStatus.state === 'granted') {
                this.clipboardPermissionGranted = true;
                console.info('[PermissionManager] ✅ 剪贴板权限已授予');
                return true;
            }

            if (permissionStatus.state === 'denied') {
                console.warn('[PermissionManager] ❌ 剪贴板权限被拒绝');
                this.showPermissionDeniedNotice();
                return false;
            }

            // prompt 状态：需要用户交互才能请求权限
            if (permissionStatus.state === 'prompt') {
                console.info('[PermissionManager] ⏳ 剪贴板权限需要用户交互授予');
                // 监听权限状态变化
                permissionStatus.addEventListener('change', () => {
                    console.info('[PermissionManager] 权限状态已变更:', permissionStatus.state);
                    this.clipboardPermissionGranted = permissionStatus.state === 'granted';
                });
                return false;
            }

            return false;

        } catch (error) {
            console.error('[PermissionManager] 请求剪贴板权限失败:', error);
            // 如果权限 API 失败，尝试直接访问
            return await this.tryClipboardAccess();
        }
    }

    /**
     * 尝试访问剪贴板（用于触发权限提示或验证权限）
     */
    private async tryClipboardAccess(): Promise<boolean> {
        try {
            console.info('[PermissionManager] 尝试直接访问剪贴板');
            
            // 检查文档焦点
            if (!document.hasFocus()) {
                console.warn('[PermissionManager] 文档未获得焦点，无法请求剪贴板权限');
                return false;
            }

            // 尝试读取剪贴板（会触发权限提示）
            await navigator.clipboard.readText();
            
            this.clipboardPermissionGranted = true;
            console.info('[PermissionManager] ✅ 剪贴板访问成功');
            return true;

        } catch (error) {
            const errorName = error instanceof Error ? error.name : 'Unknown';
            
            if (errorName === 'NotAllowedError') {
                console.warn('[PermissionManager] ❌ 用户拒绝剪贴板权限或文档未聚焦');
            } else if (errorName === 'NotFoundError') {
                console.warn('[PermissionManager] 剪贴板为空（但权限可能已授予）');
                this.clipboardPermissionGranted = true;
                return true;
            } else {
                console.error('[PermissionManager] 剪贴板访问失败:', errorName, error);
            }
            
            return false;
        }
    }

    /**
     * 显示权限被拒绝的提示
     */
    private showPermissionDeniedNotice(): void {
        const notice = document.createElement('div');
        notice.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 16px 20px;
            background: #ff4444;
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 999999;
            font-family: system-ui, -apple-system, sans-serif;
            font-size: 14px;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        notice.innerHTML = `
            <div style="font-weight: 600; margin-bottom: 8px;">⚠️ EchoSync 权限提示</div>
            <div style="font-size: 13px; line-height: 1.5;">
                剪贴板权限被拒绝。部分功能可能无法使用。<br>
                您可以在浏览器地址栏左侧点击锁图标，允许访问剪贴板。
            </div>
        `;

        // 添加动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(400px);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(notice);

        // 5秒后自动移除
        setTimeout(() => {
            notice.style.animation = 'slideIn 0.3s ease-out reverse';
            setTimeout(() => {
                notice.remove();
                style.remove();
            }, 300);
        }, 5000);
    }

    /**
     * 用户交互时请求权限（点击、聚焦等事件触发）
     * 这种方式更容易获得权限授予
     */
    public async requestPermissionOnUserInteraction(): Promise<boolean> {
        // console.info('[PermissionManager] 用户交互时请求权限');
        
        // 确保文档获得焦点
        window.focus();
        
        return await this.requestClipboardPermission();
    }

    /**
     * 检查是否已授予剪贴板权限
     */
    public isClipboardPermissionGranted(): boolean {
        return this.clipboardPermissionGranted;
    }

    /**
     * 检查并请求权限（智能模式）
     * 如果已授予则直接返回，未授予则尝试请求
     */
    public async checkAndRequestPermission(): Promise<boolean> {
        if (this.clipboardPermissionGranted) {
            console.info('[PermissionManager] 剪贴板权限已授予（缓存）');
            return true;
        }

        return await this.requestClipboardPermission();
    }

    /**
     * 设置自动重试策略
     * 在用户首次点击页面时自动请求权限
     */
    public setupAutoRetryOnUserInteraction(): void {
        console.info('[PermissionManager] 设置自动重试策略');

        const handleUserInteraction = async (event: Event) => {
            // 如果已经授予权限，移除监听器
            if (this.clipboardPermissionGranted) {
                document.removeEventListener('click', handleUserInteraction);
                document.removeEventListener('focus', handleUserInteraction);
                console.info('[PermissionManager] 权限已授予，移除用户交互监听器');
                return;
            }

            // console.info('[PermissionManager] 检测到用户交互，尝试请求权限');
            const granted = await this.requestPermissionOnUserInteraction();

            if (granted) {
                // 权限授予成功，移除监听器
                document.removeEventListener('click', handleUserInteraction);
                document.removeEventListener('focus', handleUserInteraction);
                console.info('[PermissionManager] ✅ 权限授予成功，移除用户交互监听器');
            }
        };

        // 监听用户交互事件
        document.addEventListener('click', handleUserInteraction, { once: false });
        document.addEventListener('focus', handleUserInteraction, { once: false, capture: true });
    }
}

// 导出单例实例
export const permissionManager = PermissionManager.getInstance();
