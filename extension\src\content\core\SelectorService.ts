import SelectorManager from "@/content/configs/SelectorManager";
import { DOMUtils } from "@/content/utils/DOMUtils";

/**
 * 选择器管理服务，提供多选择器策略提升兼容性
 * 使用统一的SelectorManager管理选择器
 */
export class SelectorService {

    /**
     * 查找完成按钮
     * @param container 容器元素
     * @returns 找到的按钮元素或null
     */
    public static findCompletionButton(container: Element): Element | null {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.copyButton) {
            console.warn(`【SelectorService】 未找到复制按钮选择器配置`);
            return null;
        }

        const element = DOMUtils.findElementInContainer(container, selectors.copyButton);
        if (element) {
            console.info(`【SelectorService】 找到完成按钮`);
            return element;
        }

        console.warn(`【SelectorService】 未找到任何完成按钮`);
        return null;
    }

    /**
     * 获取所有完成选择器
     */
    public static getCompletionSelectors(): string[] {
        const selectors = SelectorManager.getSelector();
        return selectors?.copyButton || [];
    }

    /**
     * 检查元素是否为完成按钮
     */
    public static isCompletionButton(element: Element): boolean {
        const selectors = SelectorManager.getSelector();
        if (!selectors?.copyButton) {
            return false;
        }

        return selectors.copyButton.some(selector => {
            try {
                return element.matches(selector);
            } catch (error) {
                return false;
            }
        });
    }
}