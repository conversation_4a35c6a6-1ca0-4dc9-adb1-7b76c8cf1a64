# 导出标题智能截断优化

## 优化背景

在导出提示词答案到各个笔记平台（Obsidian、Notion、Markdown）时，直接使用提示词问题作为文件名和标题会导致：

1. **文件名过长**：超出文件系统限制或显示不全
2. **标题显示不美观**：在 Modal 输入框中显示过长，影响用户体验
3. **截断不智能**：简单的 `substring(0, 50)` 可能在单词中间截断

## 优化方案

### 1. 配置化截断长度

**新增配置**：在 `SyncSettings` 中添加 `export_title_len` 配置项

```typescript
// SettingsStorageService.ts
export interface SyncSettings {
  platforms: { ... }
  export_title_len?: number // 导出标题截断长度（默认40）
}
```

**访问方法**：
- `SettingsStorageService.getExportTitleLength()` - 获取配置的截断长度
- `SettingsStorageService.setExportTitleLength(length)` - 设置截断长度
- `ContentSettingsService.getExportTitleLength()` - Content 模块访问接口

### 2. 创建智能截断工具类

**文件**: `extension/src/content/utils/TextUtils.ts`

#### 核心功能

- **智能截断** (`truncateTitle`)
  - 默认截断长度：40个字符（可配置）
  - 按词边界截断（避免截断单词中间）
  - 自动添加省略号 `...`
  - 移除末尾标点符号

- **文件名清理** (`sanitizeFilename`)
  - 替换文件系统非法字符为 `-`
  - 处理多个空格

- **安全文件名生成** (`generateSafeFilename`)
  - 结合截断和清理功能
  - 适用于导出文件

#### 智能截断算法

```typescript
// 1. 检查文本长度
if (trimmed.length <= maxLength) {
    return trimmed;
}

// 2. 截断到最大长度
let truncated = trimmed.substring(0, maxLength);

// 3. 按词边界调整
// - 中文：每个字都是独立的，直接截断
// - 英文：回退到最近的空格或标点
truncated = this.truncateAtWordBoundary(truncated, original);

// 4. 移除末尾标点符号和空格
truncated = this.removeTrailingPunctuation(truncated);

// 5. 添加省略号
return truncated + '...';
```

#### 词边界检测

- **中文字符**: 直接在字符边界截断（CJK 范围检测）
- **英文单词**: 回退到最近的空格或标点符号
- **长单词**: 如果整个文本都是一个单词，保持原截断位置

### 3. 修改导出组件

#### ObsidianExportModal.ts

**修改位置**: `createTitleInput()` 方法

```typescript
// 修改前
this.titleInput.value = defaultTitle;

// 修改后
const truncatedTitle = TextUtils.truncateTitle(defaultTitle); // 使用默认40
this.titleInput.value = truncatedTitle;
```

#### NotionExportModal.ts

**修改位置**: `createTitleInput()` 方法

```typescript
// 修改前
const truncatedTitle = defaultTitle.length > 50 
    ? defaultTitle.substring(0, 50) + '...' 
    : defaultTitle;

// 修改后
const truncatedTitle = TextUtils.truncateTitle(defaultTitle); // 使用默认40 
    ? defaultTitle.substring(0, 50) + '...' 
    : defaultTitle;

// 修改后
const truncatedTitle = TextUtils.truncateTitle(defaultTitle, 20);
```

## 测试用例

### 中文文本测试

```typescript
输入: '这是一个非常非常非常非常长的标题需要被截断'
输出: '这是一个非常非常非常非常长的标题...'
```

### 英文文本测试（词边界）

```typescript
输入: 'How to use TypeScript with React and Redux'
输出: 'How to use...'
// 注意：在 'use' 后截断，而不是在 'TypeScript' 中间
```

### 中英文混合测试

```typescript
输入: '如何使用 TypeScript 开发 Chrome 扩展程序的完整指南'
输出: '如何使用 TypeScript 开发 Chrome...'
```

### 标点符号清理

```typescript
输入: '这是一个标题，包含逗号，需要截断，'
输出: '这是一个标题，包含逗号...'
// 注意：省略号前的逗号被移除
```

## 优化效果对比

### 修改前

| 组件 | 截断方式 | 示例输出 |
|------|---------|---------|
| ObsidianExportModal | 无截断 | `如何使用TypeScript开发Chrome扩展程序的完整指南包含详细步骤和代码示例以及...` (完整显示) |
| NotionExportModal | `substring(0, 50)` | `如何使用TypeScript开发Chrome扩展程序的完整指南包含详细步骤和代码示例...` (50字) |

### 修改后

| 组件 | 截断方式 | 示例输出 |
|------|---------|---------|
| ObsidianExportModal | `truncateTitle(40)` | `如何使用 TypeScript 开发 Chrome 扩展程序的完整指南包含详细...` (40字) |
| NotionExportModal | `truncateTitle(40)` | `如何使用 TypeScript 开发 Chrome 扩展程序的完整指南包含详细...` (40字) |

### 配置化管理

- **默认长度**: 40 字符（在 `SyncSettings` 中配置）
- **可调整**: 通过 `SettingsStorageService.setExportTitleLength(length)` 修改
- **跨设备同步**: 配置存储在 `chrome.storage.sync`，自动同步到所有设备

## 技术细节

### CJK 字符检测

```typescript
private static isCJK(char: string): boolean {
    const code = char.charCodeAt(0);
    return (
        (code >= 0x4E00 && code <= 0x9FFF) ||   // CJK统一表意文字
        (code >= 0x3400 && code <= 0x4DBF) ||   // CJK扩展A
        (code >= 0x3040 && code <= 0x309F) ||   // 平假名
        (code >= 0x30A0 && code <= 0x30FF) ||   // 片假名
        (code >= 0xAC00 && code <= 0xD7AF)      // 韩文音节
    );
}
```

### 词边界回退算法

```typescript
// 向前查找最近的空格或标点
const lastSpaceIndex = truncated.search(/[\s\p{P}](?!.*[\s\p{P}])/u);

if (lastSpaceIndex > 0) {
    // 找到词边界，截断到该位置
    truncated = truncated.substring(0, lastSpaceIndex);
}
```

### 正则表达式说明

- `/[\s\p{P}]/u`: 匹配空格或 Unicode 标点符号
- `(?!.*[\s\p{P}])`: 负向前瞻，确保找到最后一个匹配

## 性能考虑

- **时间复杂度**: O(n)，n 为文本长度
- **空间复杂度**: O(n)，创建截断后的新字符串
- **优化**: 只处理超过最大长度的文本

## 扩展性

### 可配置参数

```typescript
// 自定义截断长度
TextUtils.truncateTitle(text, 30); // 30个字符

// 使用默认长度
TextUtils.truncateTitle(text); // 20个字符
```

### 未来扩展

1. **响应式截断**: 根据屏幕尺寸动态调整长度
2. **多语言优化**: 针对不同语言的特殊处理
3. **智能缩写**: 使用常见缩写替换长词

## 注意事项

1. **不处理 Tooltip**: Obsidian 和 Notion 的 Tooltip 已经足够简短（8-9字），无需修改
2. **MarkdownExportButton**: Tooltip 保持原样（根据用户反馈无需修改）
3. **HistoryBubble**: 已在其他优化中处理，本次不涉及

## 测试步骤

1. 在 Chrome 扩展管理页面点击"重新加载"
2. 访问 Kimi.ai 并提问一个很长的问题（超过20字）
3. 点击 Obsidian 导出按钮，查看 Modal 中的标题输入框
4. 点击 Notion 导出按钮，查看 Modal 中的标题输入框
5. 验证标题已被智能截断为 20 字符 + `...`

## 代码位置

```
extension/src/content/
├── utils/
│   ├── TextUtils.ts           # 工具类（新增）
│   └── TextUtils.test.ts      # 单元测试（新增）
└── inject/components/
    ├── ObsidianExportModal.ts # 已修改
    └── NotionExportModal.ts   # 已修改
```

## 提交信息

```
feat: 优化导出标题智能截断

- 创建 TextUtils 工具类，提供智能截断功能
- 按词边界截断，避免在单词中间截断
- 统一 Obsidian 和 Notion Modal 的标题截断长度为 20 字符
- 添加完整的单元测试覆盖
- 支持中文、英文和混合文本的智能处理

Closes #优化导出标题过长问题
```

## 相关文档

- [TextUtils API 文档](./TextUtils.ts)
- [单元测试说明](./TextUtils.test.ts)
- [ObsidianExportModal 修改](../inject/components/ObsidianExportModal.ts)
- [NotionExportModal 修改](../inject/components/NotionExportModal.ts)
