/* Design System Variables */
:root {
    /* Colors */
    --blue-600: #2563eb;
    --blue-500: #3b82f6;
    --purple-600: #7c3aed;
    --purple-500: #8b5cf6;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    
    /* Text Colors */
    --text-primary: #0f172a;
    --text-muted: #64748b;
    
    /* Background */
    --bg-start: #f8fafc;
    --bg-end: #e2e8f0;
    --card-bg: #ffffff;
    
    /* Typography */
    --font-family: "Inter", system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-md: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
    --font-weight-extrabold: 800;
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 24px;
    --spacing-2xl: 32px;
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(15, 23, 42, 0.04);
    --shadow-md: 0 6px 20px rgba(15, 23, 42, 0.06);
    --shadow-lg: 0 12px 32px rgba(15, 23, 42, 0.08);
    --shadow-xl: 0 20px 48px rgba(15, 23, 42, 0.12);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(180deg, var(--bg-start), #ffffff 40%, var(--bg-end));
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
}

.popup-container {
    width: 800px;
    height: 700px;
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
}

.popup-container.fullscreen {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
}

/* Header */
.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background: var(--card-bg);
    border-bottom: 1px solid rgba(15, 23, 42, 0.06);
}

.user-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--blue-500), var(--purple-500));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-sm);
    border: 2px solid var(--blue-500);
}

.status-badge {
    background: linear-gradient(135deg, var(--blue-500), var(--purple-500));
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
}

.fullscreen-btn {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: background 0.2s ease;
}

.fullscreen-btn:hover {
    background: rgba(15, 23, 42, 0.05);
}

/* Layout */
.main-layout {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 120px;
    background: var(--card-bg);
    border-right: 1px solid rgba(15, 23, 42, 0.06);
    padding: var(--spacing-lg) 0;
}

.popup-container.fullscreen .sidebar {
    width: 200px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-muted);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.nav-item:hover {
    background: rgba(37, 99, 235, 0.05);
    color: var(--blue-600);
}

.nav-item.active {
    background: rgba(37, 99, 235, 0.1);
    color: var(--blue-600);
    border-right: 2px solid var(--blue-500);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: rgba(248, 250, 252, 0.5);
}

/* Pages */
.page {
    display: none;
}

.page.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Cards */
.card {
    background: var(--card-bg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    transition: all 0.2s ease;
}

.card-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(15, 23, 42, 0.06);
    background: rgba(248, 250, 252, 0.5);
}

.card-content {
    padding: var(--spacing-lg);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--blue-500);
    color: white;
}

.btn-primary:hover {
    background: var(--blue-600);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--card-bg);
    color: var(--blue-600);
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.btn-secondary:hover {
    background: rgba(37, 99, 235, 0.05);
    border-color: var(--blue-500);
    transform: translateY(-1px);
}

.btn-small {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.status-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
    background: #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch.active {
    background: var(--blue-500);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-switch.active::after {
    transform: translateX(20px);
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(15, 23, 42, 0.06);
    border-radius: 4px;
    overflow: hidden;
    margin: var(--spacing-sm) 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--blue-500), var(--purple-500));
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: progressPulse 2s ease-in-out infinite;
}

/* Grid Layouts */
.action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    background: rgba(37, 99, 235, 0.02);
    border: 1px solid rgba(37, 99, 235, 0.08);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-item:hover {
    background: rgba(37, 99, 235, 0.05);
    border-color: rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.action-icon {
    font-size: var(--font-size-xl);
}

.action-label {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-muted);
    text-align: center;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin: var(--spacing-md) 0;
}

.stat-item {
    text-align: center;
    padding: var(--spacing-md);
    background: rgba(37, 99, 235, 0.05);
    border-radius: var(--radius-md);
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--blue-600);
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

/* Platform Tabs */
.platform-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.platform-tab {
    flex: 1;
    padding: var(--spacing-md);
    text-align: center;
    border: 1px solid rgba(15, 23, 42, 0.12);
    border-radius: var(--radius-md);
    background: var(--card-bg);
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-muted);
    transition: all 0.2s ease;
}

.platform-tab:hover {
    color: var(--blue-600);
    border-color: rgba(37, 99, 235, 0.2);
    transform: translateY(-1px);
}

.platform-tab.active {
    background: var(--blue-500);
    color: white;
    border-color: var(--blue-500);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

/* Platform Content */
.platform-content {
    display: none;
}

.platform-content.active {
    display: block;
}

.device-item {
    border: 1px solid rgba(15, 23, 42, 0.06);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    transition: all 0.2s ease;
}

.device-item:hover {
    border-color: rgba(37, 99, 235, 0.12);
    box-shadow: var(--shadow-sm);
}

.device-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-medium);
}

.device-path {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.device-path input {
    flex: 1;
    padding: var(--spacing-md);
    border: 1px solid rgba(15, 23, 42, 0.12);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

.current-device {
    background: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
}

.path-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: var(--spacing-sm);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-6px); }
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes fadeIn {
    0% { opacity: 0; transform: translateY(10px); }
    100% { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.float {
    animation: float 3s ease-in-out infinite;
}
