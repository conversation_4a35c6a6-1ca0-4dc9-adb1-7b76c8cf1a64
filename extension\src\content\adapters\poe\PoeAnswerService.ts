import { BaseAnswerService } from "@/content/core/BaseAnswerService";
import SelectorManager from "../../configs/SelectorManager";
import { DOMUtils } from "../../utils/DOMUtils";
import { AnswerModel } from "../../model/AnswerModel";
import { PoeClipboardService } from "./PoeClipboardService";

/**
 * Poe 答案管理服务
 * 
 * ⚠️ 特殊说明：
 * Poe 平台使用问答对(Message Tuple)结构，与其他平台不同：
 * - 每个问答对([class*="ChatMessagesView_messageTuple"])包含两个message
 * - 第一个message是用户问题（右侧）
 * - 第二个message是AI答案（左侧）
 * - 答案完成标志：[class*="Message_messageMetadataContainer"]（显示时间的元素）
 * 
 * 因此需要覆盖BaseAnswerService的部分方法来适配这种结构
 */
export class PoeAnswerService extends BaseAnswerService {
    private clipboardService: PoeClipboardService;

    constructor() {
        super();
        this.clipboardService = new PoeClipboardService();
    }

    /**
     * 获取剪贴板服务实例（BaseAnswerService 抽象方法实现）
     * @returns PoeClipboardService 实例
     */
    protected getClipboardService(): PoeClipboardService {
        return this.clipboardService;
    }

    /**
     * 覆盖：捕获现有的问答对
     * Poe特殊处理：遍历问答对容器，分别提取问题和答案
     * @param chatListElement 聊天列表元素
     */
    protected async captureExistingQAPairs(chatListElement: Element): Promise<void> {
        try {
            console.info('【PoeAnswerService】 开始捕获现有问答对（Poe问答对模式）');
            const selectors = SelectorManager.getSelector();

            // Poe使用问答对结构，需要特殊处理
            const messageTuples = DOMUtils.findElementAllInContainer(
                chatListElement, 
                selectors.messageTuple || []
            );

            console.info(`【PoeAnswerService】 找到 ${messageTuples.length} 个问答对`);

            if (messageTuples.length === 0) {
                console.warn('【PoeAnswerService】 未找到问答对容器，尝试使用通用方法');
                // 如果没有找到问答对，回退到基类方法
                await super.captureExistingQAPairs(chatListElement);
                return;
            }

            // 遍历每个问答对
            for (let i = 0; i < messageTuples.length; i++) {
                const tuple = messageTuples[i];
                console.info(`【PoeAnswerService】 处理第 ${i + 1} 个问答对`);

                // 获取问答对中的所有消息元素
                const messages = this.getMessagesFromTuple(tuple);

                if (messages.length < 2) {
                    console.warn(`【PoeAnswerService】 问答对 ${i + 1} 消息数量不足（期望2个，实际${messages.length}个），跳过`);
                    continue;
                }

                // 第一个消息是问题
                const promptMessage = messages[0];
                await this.processExistingPrompt(promptMessage);

                // 第二个消息是答案
                const answerMessage = messages[1];
                await this.processExistingAnswer(answerMessage);
            }

            console.info('【PoeAnswerService】 现有问答对捕获完成');
        } catch (error) {
            console.error('【PoeAnswerService】 捕获现有问答对失败', error);
        }
    }

    /**
     * 从问答对容器中提取消息元素
     * @param tupleElement 问答对容器元素
     * @returns 消息元素数组 [问题消息, 答案消息]
     */
    private getMessagesFromTuple(tupleElement: Element): Element[] {
        const selectors = SelectorManager.getSelector();
        
        // 尝试通过 messageElement 选择器查找（使用模糊匹配作为后备）
        const messages = DOMUtils.findElementAllInContainer(
            tupleElement,
            selectors.messageElement || ['[id^="message-"]', '[class*="ChatMessage_chatMessage"]']
        );

        if (messages.length >= 2) {
            return messages;
        }

        // 备用方案：直接查找所有子元素中的消息
        const allMessages = Array.from(tupleElement.querySelectorAll('[id^="message-"]'));
        return allMessages.slice(0, 2) as Element[];
    }

    protected getPromptDivHash(promptElement: Element): string|null {
        return promptElement.id;
    }

    protected getAnswerDivHash(answerElement: Element): string|null {
        return answerElement.id;
    }

    /**
     * 覆盖：处理已存在的问题
     * Poe特殊处理：问题在右侧消息气泡中
     * @param promptElement 问题元素（在Poe中是整个问题消息）
     */
    protected async processExistingPrompt(promptElement: Element): Promise<void> {
        try {
            const selectors = SelectorManager.getSelector();
            
            // Poe的问题内容在右侧消息气泡的markdown容器中
            const contentNode = DOMUtils.findElementInContainer(
                promptElement,
                selectors.promptContent || selectors.markdown || []
            );

            if (contentNode?.textContent) {
                const content = contentNode.textContent.trim();
                if (content) {
                    await AnswerModel.getInstance().addPrompt(content, this.getPromptDivHash(promptElement));
                    console.info('【PoeAnswerService】 问题提取完成', content.slice(0, 50));
                }
            } else {
                console.warn('【PoeAnswerService】 未找到问题内容节点');
            }
        } catch (error) {
            console.error('【PoeAnswerService】 处理问题失败', error);
        }
    }

    /**
     * 覆盖：处理已存在的答案
     * Poe特殊处理：
     * 1. 答案在左侧消息气泡中
     * 2. 使用HTML解析方式提取内容（没有复制按钮）
     * 3. 答案完成标志是 Message_messageMetadataContainer__nBPq7
     * @param answerElement 答案元素（在Poe中是整个答案消息）
     */
    protected async processExistingAnswer(answerElement: Element): Promise<void> {
        try {
            console.info('【PoeAnswerService】Existing模式: 开始提取答案（Poe HTML解析模式）');
            const selectors = SelectorManager.getSelector();

            // 1. 查找答案完成标志
            let completionNode = DOMUtils.findElementInContainer(
                answerElement,
                selectors.answerCompletion || []
            );

            // 如果没有找到完成标志，等待答案完成
            if (!completionNode) {
                console.info('【PoeAnswerService】Existing模式: 答案尚未完成，等待完成标志...');
                completionNode = await DOMUtils.asyncSelectElementInContainer(
                    answerElement,
                    selectors.answerCompletion || [],
                    this.ANSWER_TIMEOUT
                );
            }

            if (!completionNode) {
                console.warn('【PoeAnswerService】Existing模式: 未找到答案完成标志，可能答案尚未生成完成');
                return;
            }

            console.info('【PoeAnswerService】Existing模式: 答案已完成，开始提取内容');

            // 2. 使用剪贴板服务提取内容（Poe使用HTML解析，不需要复制按钮）
            const clipboardService = this.getClipboardService();
            const answerContent = await clipboardService.extractAnswerContent(answerElement);

            if (!answerContent?.trim()) {
                console.warn('【PoeAnswerService】Existing模式: 提取的答案内容为空');
                return;
            }

            console.info('【PoeAnswerService】Existing模式: 答案内容提取成功', {
                length: answerContent.length,
                preview: answerContent.slice(0, 100)
            });

            // 3. 保存到数据模型
            const answerIndex = await AnswerModel.getInstance().addAnswer(answerContent, this.getAnswerDivHash(answerElement));
            console.info(`【PoeAnswerService】Existing模式: 已保存，下标: ${answerIndex}`);

            // 4. 触发事件通知
            this.dispatchAnswerExtractedEvent(completionNode, answerIndex);

        } catch (error) {
            console.error('【PoeAnswerService】Existing模式: 提取失败', error);
        }
    }

    /**
     * 覆盖：处理新增的节点
     * Poe特殊处理：检测问答对容器的添加
     * @param node 新增的 DOM 节点
     */
    protected async handleNewNode(node: Element): Promise<void> {
        const selectors = SelectorManager.getSelector();

        // 优先检测问答对容器（Poe特有）
        if (selectors.messageTuple && selectors.messageTuple.some(sel => node.matches(sel))) {
            console.info('【PoeAnswerService】 检测到新的问答对容器');
            await this.handleNewMessageTuple(node);
            return;
        }

        // 检查子节点中是否有问答对容器
        const childTuples = DOMUtils.findElementAllInContainer(node, selectors.messageTuple || []);
        if (childTuples.length > 0) {
            console.info(`【PoeAnswerService】 在子节点中检测到 ${childTuples.length} 个问答对容器`);
            for (const tuple of childTuples) {
                await this.handleNewMessageTuple(tuple);
            }
            return;
        }

        // 如果没有检测到问答对，使用基类的通用处理
        await super.handleNewNode(node);
    }

    /**
     * 处理新增的问答对容器
     * @param tupleElement 问答对容器元素
     */
    private async handleNewMessageTuple(tupleElement: Element): Promise<void> {
        try {
            console.info('【PoeAnswerService】 开始处理新问答对');

            // 获取问答对中的消息
            const messages = this.getMessagesFromTuple(tupleElement);

            if (messages.length < 2) {
                console.warn(`【PoeAnswerService】 问答对消息数量不足（期望2个，实际${messages.length}个）`);
                // 可能答案还在生成中，只处理问题
                if (messages.length === 1) {
                    await this.processExistingPrompt(messages[0]);
                    // 等待答案消息出现
                    console.info('【PoeAnswerService】 等待答案消息出现...');
                }
                return;
            }

            // 处理问题（第一个消息）
            const promptMessage = messages[0];
            if (!this.generatingPromptElement) {
                this.generatingPromptElement = promptMessage;
                await this.processExistingPrompt(promptMessage);
            }

            // 处理答案（第二个消息）
            const answerMessage = messages[1];
            if (!this.generatingAnswerElement) {
                this.generatingAnswerElement = answerMessage;
                
                // 检查答案是否已完成
                const selectors = SelectorManager.getSelector();
                const hasCompletion = DOMUtils.findElementInContainer(
                    answerMessage,
                    selectors.answerCompletion || []
                );

                if (hasCompletion) {
                    console.info('【PoeAnswerService】 答案已完成，直接提取');
                    await this.processExistingAnswer(answerMessage);
                } else {
                    console.info('【PoeAnswerService】 答案生成中，开始监听');
                    await this.processGeneratingAnswer(answerMessage);
                }
            }

        } catch (error) {
            console.error('【PoeAnswerService】 处理新问答对失败', error);
        }
    }

    /**
     * 覆盖：处理生成中的答案
     * Poe特殊处理：等待 Message_messageMetadataContainer__nBPq7 出现
     * @param answerElement 答案元素
     */
    protected async processGeneratingAnswer(answerElement: Element): Promise<void> {
        console.info('【PoeAnswerService】 开始监听Poe答案生成完成状态');
        const startTime = Date.now();
        const selectors = SelectorManager.getSelector();

        // 创建监听器等待答案完成标志出现
        const completionObserver = new MutationObserver((mutations) => {
            // 检查是否超时
            if (Date.now() - startTime > this.ANSWER_TIMEOUT) {
                console.warn('【PoeAnswerService】 等待答案完成超时');
                completionObserver.disconnect();
                this.clearGeneratingElements();
                return;
            }

            // 检查答案完成标志
            const completionNode = DOMUtils.findElementInContainer(
                answerElement,
                selectors.answerCompletion || []
            );

            if (completionNode) {
                console.info('【PoeAnswerService】 检测到答案完成标志');
                completionObserver.disconnect();
                
                // 提取答案内容
                this.processExistingAnswer(answerElement).then(() => {
                    this.clearGeneratingElements();
                }).catch(error => {
                    console.error('【PoeAnswerService】 提取答案内容失败', error);
                    this.clearGeneratingElements();
                });
            }
        });

        // 开始监听
        completionObserver.observe(answerElement, this.ANSWER_GENERATION_CONFIG);
        console.info('【PoeAnswerService】 Poe答案完成监听器已启动');
    }
}
