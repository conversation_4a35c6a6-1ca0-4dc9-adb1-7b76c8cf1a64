# Kimi 答案导出到 Obsidian - 架构设计

## 🏗️ 整体架构

```
┌──────────────────────────────────────────────────────────────────┐
│                        Kimi 页面 (DOM)                            │
│  ┌────────────────────────────────────────────────────────────┐  │
│  │  Answer Completion 组件                                     │  │
│  │  ├─ 复制按钮                                                │  │
│  │  ├─ 其他按钮                                                │  │
│  │  └─ [Obsidian 图标] ← 注入点                               │  │
│  └────────────────────────────────────────────────────────────┘  │
└──────────────────────────────────────────────────────────────────┘
                              ↓
┌──────────────────────────────────────────────────────────────────┐
│                   ObsidianExportInject (注入器)                   │
│  • 监听 DOM 变化                                                  │
│  • 在答案节点中注入 Obsidian 图标                                │
│  • 管理图标组件实例                                              │
└──────────────────────────────────────────────────────────────────┘
                              ↓
┌──────────────────────────────────────────────────────────────────┐
│              ObsidianExportButton (图标组件)                      │
│  • 渲染 Obsidian 图标                                            │
│  • 显示 tooltip                                                  │
│  • 处理点击事件                                                  │
└──────────────────────────────────────────────────────────────────┘
                              ↓ 点击
┌──────────────────────────────────────────────────────────────────┐
│              ObsidianExportModal (导出卡片)                       │
│  • 显示问答内容                                                  │
│  • 标题编辑                                                      │
│  • Tag 选择器                                                    │
│  • 导出按钮                                                      │
└──────────────────────────────────────────────────────────────────┘
                              ↓ 导出
┌──────────────────────────────────────────────────────────────────┐
│           ObsidianExportService (导出服务)                        │
│  • 从 AnswerModel 获取数据                                       │
│  • 生成 Markdown 内容                                            │
│  • 生成文件名                                                    │
│  • 保存文件                                                      │
└──────────────────────────────────────────────────────────────────┘
                              ↓
┌──────────────────────────────────────────────────────────────────┐
│                    数据和服务层                                   │
│  ┌────────────────────┐  ┌──────────────────────────────────┐  │
│  │   AnswerModel      │  │  ContentSettingsService          │  │
│  │  • promptList[]    │  │  • getExportPath('obsidian')     │  │
│  │  • answerList[]    │  │  • isPlatformConfigured()        │  │
│  └────────────────────┘  └──────────────────────────────────┘  │
└──────────────────────────────────────────────────────────────────┘
```

## 📦 模块设计

### 1. ObsidianExportInject (注入器)

**职责**: 
- 监听答案节点的添加
- 在 answerCompletion 组件中注入 Obsidian 图标
- 管理图标组件的生命周期

**关键方法**:
```typescript
class ObsidianExportInject {
  private observer: MutationObserver | null;
  private buttons: Map<Element, ObsidianExportButton>;
  
  // 启动监听
  public start(): void
  
  // 停止监听
  public stop(): void
  
  // 处理新答案节点
  private handleNewAnswer(answerElement: Element, answerIndex: number): void
  
  // 注入图标
  private injectButton(completionElement: Element, answerIndex: number): void
}
```

**依赖**:
- `ObsidianExportButton`: 图标组件
- `SelectorManager`: 选择器配置
- `DOMUtils`: DOM 工具

---

### 2. ObsidianExportButton (图标组件)

**职责**:
- 渲染 Obsidian 图标
- 显示悬浮提示
- 处理点击事件

**关键方法**:
```typescript
class ObsidianExportButton {
  private element: HTMLElement;
  private answerIndex: number;
  
  // 构造函数
  constructor(answerIndex: number)
  
  // 渲染组件
  public render(): HTMLElement
  
  // 绑定点击事件
  public onClick(handler: (index: number) => void): void
  
  // 显示 tooltip
  private showTooltip(): void
  
  // 隐藏 tooltip
  private hideTooltip(): void
}
```

**UI 结构**:
```html
<button class="obsidian-export-btn" title="点击导出 markdown 到 obsidian">
  <svg><!-- Obsidian Logo --></svg>
</button>
```

**样式**:
- 尺寸: 32x32px
- 颜色: 默认灰色 (#6B7280)，悬浮蓝色 (#3B82F6)
- 过渡: 0.2s ease
- 圆角: 4px

---

### 3. ObsidianExportModal (导出卡片)

**职责**:
- 显示导出设置界面
- 收集用户输入（标题、标签）
- 触发导出操作

**关键方法**:
```typescript
class ObsidianExportModal {
  private element: HTMLElement;
  private answerIndex: number;
  private tags: string[];
  
  // 构造函数
  constructor(answerIndex: number)
  
  // 显示 Modal
  public show(): void
  
  // 隐藏 Modal
  public hide(): void
  
  // 获取表单数据
  private getFormData(): ExportFormData
  
  // 处理导出
  private handleExport(): Promise<void>
  
  // 添加标签
  private addTag(tag: string): void
  
  // 删除标签
  private removeTag(tag: string): void
}
```

**UI 结构**:
```html
<div class="obsidian-modal-overlay">
  <div class="obsidian-modal">
    <div class="modal-header">
      <img src="obsidian-logo" />
      <button class="close-btn">×</button>
    </div>
    
    <div class="modal-body">
      <!-- 标题 -->
      <div class="form-group">
        <label>标题</label>
        <input type="text" value="[问题内容]" />
      </div>
      
      <!-- 正文预览 -->
      <div class="form-group">
        <label>正文</label>
        <div class="content-preview">[答案内容]</div>
      </div>
      
      <!-- Tag 选择器 -->
      <div class="form-group">
        <label>标签</label>
        <div class="tag-input-wrapper">
          <input type="text" placeholder="输入标签，回车添加" />
        </div>
        <div class="tag-list">
          <span class="tag">AI <button>×</button></span>
          <span class="tag">Kimi <button>×</button></span>
        </div>
      </div>
      
      <!-- 元数据 -->
      <div class="form-group">
        <label>创建时间</label>
        <div class="metadata">2024-01-06 14:30:22</div>
      </div>
    </div>
    
    <div class="modal-footer">
      <button class="export-btn">Add to Obsidian</button>
    </div>
  </div>
</div>
```

**样式规范**:
- Modal 宽度: 600px
- Modal 最大高度: 80vh
- 背景遮罩: rgba(0, 0, 0, 0.5)
- z-index: 10000
- 动画: fade-in 300ms

---

### 4. ObsidianExportService (导出服务)

**职责**:
- 数据获取和处理
- Markdown 生成
- 文件命名
- 文件保存

**关键方法**:
```typescript
class ObsidianExportService {
  private static instance: ObsidianExportService;
  
  // 单例
  public static getInstance(): ObsidianExportService
  
  // 导出到 Obsidian
  public async exportToObsidian(params: ExportParams): Promise<void>
  
  // 获取问答对
  private getQAPair(answerIndex: number): QAPair | null
  
  // 生成 Markdown 内容
  private generateMarkdown(data: ExportData): string
  
  // 生成文件名
  private generateFilename(title: string): string
  
  // 检查配置
  private async checkConfiguration(): Promise<boolean>
  
  // 保存文件
  private async saveFile(filename: string, content: string): Promise<void>
}
```

**类型定义**:
```typescript
interface ExportParams {
  answerIndex: number;
  title: string;
  tags: string[];
}

interface ExportData {
  title: string;
  question: string;
  answer: string;
  tags: string[];
  timestamp: string;
}

interface QAPair {
  prompt: string;
  answer: string;
}
```

**文件名生成逻辑**:
```typescript
private generateFilename(title: string): string {
  // 1. 过滤特殊字符
  let filename = title.replace(/[\/\\:\*\?"<>\|]/g, '-');
  
  // 2. 限制长度
  if (filename.length > 100) {
    filename = filename.substring(0, 100);
  }
  
  // 3. 检查重复，添加后缀
  let finalFilename = filename + '.md';
  let counter = 1;
  
  while (await this.fileExists(finalFilename)) {
    finalFilename = `${filename}_${counter}.md`;
    counter++;
  }
  
  return finalFilename;
}
```

**Markdown 模板**:
```typescript
private generateMarkdown(data: ExportData): string {
  return `---
title: ${data.title}
date: ${data.timestamp}
tags: [${data.tags.join(', ')}]
source: Kimi Chat
---

# ${data.title}

## 问题

${data.question}

## 答案

${data.answer}
`;
}
```

---

## 🔄 数据流设计

### 1. 注入流程
```
页面加载
  ↓
ObsidianExportInject.start()
  ↓
监听 DOM 变化
  ↓
检测到新答案节点
  ↓
获取 answerIndex (数组索引)
  ↓
查找 answerCompletion 组件
  ↓
注入 ObsidianExportButton
  ↓
存储 button 实例到 Map
```

### 2. 导出流程
```
用户点击 Obsidian 图标
  ↓
ObsidianExportButton.onClick(answerIndex)
  ↓
创建 ObsidianExportModal(answerIndex)
  ↓
从 AnswerModel 获取问答对
  ↓
显示 Modal，预填充数据
  ↓
用户编辑标题、添加标签
  ↓
点击 "Add to Obsidian"
  ↓
ObsidianExportService.exportToObsidian()
  ↓
检查配置 (isPlatformConfigured)
  ├─ 未配置 → Toast 提示
  └─ 已配置 → 继续
      ↓
  生成 Markdown 内容
      ↓
  生成文件名
      ↓
  获取导出路径
      ↓
  保存文件
      ↓
  Toast 提示成功/失败
```

### 3. answerIndex 管理
```
promptList: ['问题1', '问题2', '问题3']
answerList: ['答案1', '答案2', '答案3']

answerIndex = 0
  → prompt = promptList[0] = '问题1'
  → answer = answerList[0] = '答案1'

answerIndex = 1
  → prompt = promptList[1] = '问题2'
  → answer = answerList[1] = '答案2'
```

---

## 🎨 样式系统设计

### CSS 变量
```css
:root {
  /* 颜色 */
  --obsidian-primary: #3B82F6;
  --obsidian-secondary: #8B5CF6;
  --obsidian-text: #1F2937;
  --obsidian-text-light: #6B7280;
  --obsidian-border: #E5E7EB;
  --obsidian-bg: #FFFFFF;
  --obsidian-bg-hover: #F3F4F6;
  
  /* 间距 */
  --obsidian-spacing-xs: 4px;
  --obsidian-spacing-sm: 8px;
  --obsidian-spacing-md: 16px;
  --obsidian-spacing-lg: 24px;
  
  /* 字体 */
  --obsidian-font-sm: 12px;
  --obsidian-font-md: 14px;
  --obsidian-font-lg: 16px;
  --obsidian-font-xl: 18px;
  
  /* 圆角 */
  --obsidian-radius-sm: 4px;
  --obsidian-radius-md: 8px;
  --obsidian-radius-lg: 12px;
  
  /* 阴影 */
  --obsidian-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --obsidian-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --obsidian-shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
}
```

### 组件样式类
```css
/* 按钮 */
.obsidian-export-btn { }

/* Modal */
.obsidian-modal-overlay { }
.obsidian-modal { }
.modal-header { }
.modal-body { }
.modal-footer { }

/* 表单 */
.form-group { }
.form-label { }
.form-input { }
.form-textarea { }

/* 标签 */
.tag-input-wrapper { }
.tag-list { }
.tag { }
.tag-remove { }

/* 按钮 */
.export-btn { }
.close-btn { }
```

---

## 🔌 集成点设计

### 1. 与 KimiAnswerService 集成
```typescript
// KimiAnswerService.ts
class KimiAnswerService {
  private async processExistingAnswer(answerElement: Element): Promise<void> {
    // ... 现有逻辑 ...
    
    if (answerContent) {
      await AnswerModel.getInstance().addAnswer(answerContent);
      
      // 触发事件 (新增)
      const answerIndex = AnswerModel.getInstance().getAnswerCount() - 1;
      this.dispatchAnswerExtractedEvent(answerElement, answerIndex);
    }
  }
  
  private dispatchAnswerExtractedEvent(element: Element, index: number): void {
    const event = new CustomEvent('ANSWER_EXTRACTED', {
      detail: { answerElement: element, answerIndex: index }
    });
    document.dispatchEvent(event);
  }
}
```

### 2. 与 AnswerModel 集成
```typescript
// AnswerModel.ts
class AnswerModel {
  // 新增方法
  public getQAPair(index: number): QAPair | null {
    if (index < 0 || index >= this.answerList.length) {
      return null;
    }
    
    return {
      prompt: this.promptList[index]?.prompt || '',
      answer: this.answerList[index] || ''
    };
  }
  
  public getAnswerCount(): number {
    return this.answerList.length;
  }
}
```

### 3. 与 ContentSettingsService 集成
```typescript
// ObsidianExportService.ts
private async checkConfiguration(): Promise<boolean> {
  const isConfigured = await contentSettingsService.isPlatformConfigured('obsidian');
  
  if (!isConfigured) {
    this.showToast('请先在设置中配置 Obsidian 导出路径');
    return false;
  }
  
  return true;
}

private async getExportPath(): Promise<string | null> {
  return await contentSettingsService.getExportPath('obsidian');
}
```

---

## 🛡️ 错误处理设计

### 错误类型
```typescript
enum ExportError {
  NOT_CONFIGURED = 'NOT_CONFIGURED',
  INVALID_INDEX = 'INVALID_INDEX',
  FILE_SAVE_FAILED = 'FILE_SAVE_FAILED',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

class ObsidianExportError extends Error {
  constructor(
    public code: ExportError,
    message: string
  ) {
    super(message);
  }
}
```

### 错误处理流程
```typescript
try {
  await exportService.exportToObsidian(params);
  showToast('✅ 导出成功！', 'success');
} catch (error) {
  if (error instanceof ObsidianExportError) {
    switch (error.code) {
      case ExportError.NOT_CONFIGURED:
        showToast('请先在设置中配置 Obsidian 导出路径', 'warning');
        break;
      case ExportError.INVALID_INDEX:
        showToast('无法获取问答内容', 'error');
        break;
      case ExportError.FILE_SAVE_FAILED:
        showToast(`导出失败: ${error.message}`, 'error');
        break;
      default:
        showToast('导出失败，请重试', 'error');
    }
  } else {
    console.error('Unexpected error:', error);
    showToast('导出失败，请重试', 'error');
  }
}
```

---

## 📊 性能优化设计

### 1. 懒加载
- Modal 组件按需创建和销毁
- 不在视口内的按钮延迟注入

### 2. 防抖节流
- DOM 变化监听使用防抖 (300ms)
- Tag 输入使用防抖 (500ms)

### 3. 缓存策略
- 已注入的按钮存储在 Map 中
- 避免重复注入

### 4. 内存管理
- Modal 关闭后销毁实例
- Observer 停止时清理事件监听

---

## 🧪 测试设计

### 单元测试
- ObsidianExportButton 组件测试
- ObsidianExportService 逻辑测试
- 文件名生成测试
- Markdown 生成测试

### 集成测试
- 注入流程测试
- 导出流程测试
- 错误处理测试

### E2E 测试
- 完整用户流程测试
- 边界情况测试
- 性能测试
