/* Markdown 导出按钮样式 */

/* CSS 变量定义 */
:root {
  --markdown-primary: #000000;
  --markdown-secondary: #333333;
  --markdown-text: #1F2937;
  --markdown-text-light: #6B7280;
  --markdown-border: #E5E7EB;
  --markdown-bg: #FFFFFF;
  --markdown-bg-hover: #F3F4F6;
  
  --markdown-spacing-xs: 4px;
  --markdown-spacing-sm: 8px;
  --markdown-spacing-md: 16px;
  
  --markdown-radius-sm: 4px;
  
  --markdown-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  /* 暗色模式适配 - 图标颜色 */
  --markdown-icon-color: #374151;
  --markdown-icon-hover: #000000;
  --markdown-icon-stroke: rgba(255, 255, 255, 0.4);  /* 增强描边强度 */
  --markdown-glow-color: rgba(59, 130, 246, 0.3);    /* 蓝色外发光 */
}

/* 暗色模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --markdown-primary: #FFFFFF;
    --markdown-secondary: #E5E7EB;
    --markdown-text: #F3F4F6;
    --markdown-text-light: #9CA3AF;
    --markdown-border: #374151;
    --markdown-bg: #1F2937;
    --markdown-bg-hover: #374151;
    
    --markdown-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    
    /* 暗色模式 - 图标颜色 */
    --markdown-icon-color: #9CA3AF;
    --markdown-icon-hover: #FFFFFF;
    --markdown-icon-stroke: rgba(255, 255, 255, 0.5);  /* 暗色模式下更强的描边 */
    --markdown-glow-color: rgba(59, 130, 246, 0.5);    /* 暗色模式下更强的外发光 */
  }
}

/* 按钮基础样式 */
.markdown-export-btn {
  /* 布局 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  /* 尺寸 */
  width: 32px;
  height: 32px;
  padding: 0;
  
  /* 外观 */
  background: transparent;
  border: none;
  border-radius: var(--markdown-radius-sm);
  color: var(--markdown-icon-color);
  
  /* 交互 */
  cursor: pointer;
  transition: all 0.2s ease;
  
  /* 重置 */
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  
  /* 闪烁动画 */
  animation: markdown-pulse 2s ease-in-out infinite;
  
  /* 增强外发光效果 - 让图标在任何背景下都醒目 */
  filter: drop-shadow(0 0 2px var(--markdown-icon-stroke))
          drop-shadow(0 0 4px var(--markdown-glow-color))
          drop-shadow(0 0 6px var(--markdown-glow-color));
}

/* 闪烁动画关键帧 */
@keyframes markdown-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* 悬浮状态 - 停止闪烁 */
.markdown-export-btn:hover {
  background: var(--markdown-bg-hover);
  color: var(--markdown-icon-hover);
  box-shadow: var(--markdown-shadow-sm);
  animation: none; /* 悬浮时停止闪烁 */
  
  /* 悬浮时加强外发光效果 */
  filter: drop-shadow(0 0 3px var(--markdown-icon-stroke))
          drop-shadow(0 0 6px var(--markdown-glow-color))
          drop-shadow(0 0 10px var(--markdown-glow-color));
}

/* 激活状态 */
.markdown-export-btn:active {
  transform: scale(0.95);
}

/* 禁用状态 */
.markdown-export-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* SVG 图标样式 */
.markdown-export-btn svg {
  display: block;
  width: 20px;
  height: 20px;
  pointer-events: none;
}

/* 自定义 Tooltip 容器 */
.markdown-export-btn {
  position: relative;
}

/* 自定义 Tooltip 提示 */
.markdown-tooltip {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  
  padding: 6px 12px;
  
  background: var(--markdown-text);
  color: var(--markdown-bg);
  font-size: 12px;
  line-height: 1.4;
  white-space: nowrap;
  border-radius: var(--markdown-radius-sm);
  
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  
  pointer-events: none;
  z-index: 10000;
}

/* Tooltip 箭头 */
.markdown-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  
  border: 4px solid transparent;
  border-top-color: var(--markdown-text);
}

/* 按钮悬浮时显示 Tooltip */
.markdown-export-btn:hover .markdown-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --markdown-primary: #FFFFFF;
    --markdown-secondary: #E5E7EB;
    --markdown-text: #F9FAFB;
    --markdown-text-light: #9CA3AF;
    --markdown-border: #374151;
    --markdown-bg: #1F2937;
    --markdown-bg-hover: #374151;
  }
  
  .markdown-tooltip {
    background: var(--markdown-bg);
    color: var(--markdown-text);
  }
  
  .markdown-tooltip::after {
    border-top-color: var(--markdown-bg);
  }
}
