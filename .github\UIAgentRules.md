# UI Agent Rules - Linear 现代极简风设计守则

> 本文档为 Claude、Cursor 等 AI 编程助手提供 UI 设计和实现的核心规范。  
> **强制执行**：所有 UI 相关代码生成必须严格遵守本规范。

---

## � 文档索引

- **UIAgentRules.md**（当前文档）- 核心设计原则和提示词
- **CSSDesignTokens.md** - 完整的 CSS 变量、间距、字体等规范
- **MicrointeractionsGuide.md** - 微交互设计详细指南（Apple 风格）

---

## 🎯 设计哲学

### 核心原则
**Linear 现代极简风**：清晰、专业、克制、精致  
**微交互设计 (Microinteractions)**：每个操作都有即时、愉悦的反馈  
**8pt Grid System**：所有间距必须是 4 的倍数

**CRAP 四原则**：
- **Contrast** (对比)：建立清晰的视觉层级
- **Repetition** (重复)：保持设计一致性
- **Alignment** (对齐)：所有元素精确对齐
- **Proximity** (亲密性)：相关元素分组

### 设计价值观
- ✅ **清晰优先**：可读性 > 视觉冲击力
- ✅ **专业感**：适配 C 端专业用户审美
- ✅ **长时间友好**：PC 大屏使用不疲劳
- ✅ **细节精致**：微妙但有质感的设计
- ✅ **微交互丰富**：每个操作都有愉悦的反馈
- ❌ **禁止花哨**：拒绝玻璃拟态、炫目渐变

---

## 🎨 快速参考

> 📖 **完整 CSS 变量**：参见 `CSSDesignTokens.md`

### 常用变量速查
| 类型 | 变量名 | 说明 |
|------|--------|------|
| **颜色** | `--primary` | 主蓝色（交互/选中） |
| | `--purple` | 辅助紫色（强化/强调） |
| | `--text-primary` | 文字主色 |
| | `--border-light` | 边框浅色 |
| **间距** | `--space-3/4/8` | 12px/16px/32px |
| **字体** | `--font-base/lg` | 14px/20px |
| **圆角** | `--radius-md/lg` | 8px/12px |
| **动效** | `--duration-fast` | 150ms |
| | `--easing-spring` | 弹性缓动 |

---

## ⚡ 微交互设计原则

### Apple 风格核心
1. **即时反馈**：用户操作立即得到响应（100-200ms）
2. **愉悦感**：微妙的动效增强体验
3. **引导性**：动效引导用户注意力
4. **自然感**：模拟物理世界的真实感（弹性、惯性）

> 📖 **完整微交互场景代码**：参见 `MicrointeractionsGuide.md`  
> 📖 **CSS 变量定义**：参见 `CSSDesignTokens.md`

---

## 🚫 禁止规则

### 绝对禁止
- ❌ 玻璃拟态效果（`backdrop-filter: blur()`）
- ❌ 过度圆角（超过 16px）
- ❌ 纯黑色（使用 `var(--text-primary)`）
- ❌ 奇数间距（必须是 4 的倍数）
- ❌ 过长动画（超过 300ms）
- ❌ 硬编码颜色值

### 缩放动画使用规范
**✅ 允许**：微小缩放（0.95-1.15）用于反馈
- 按钮按下：`scale(0.98)`
- 图标悬停：`scale(1.1)`
- 输入框焦点：`scale(1.01)`

**❌ 禁止**：大幅度缩放或单纯视觉效果
- 过度缩放：`scale(1.3)`
- 卡片纯缩放（应配合位移 `translateY`）

> 📖 **完整禁止代码示例**：参见 `CSSDesignTokens.md`

---

## 🧩 核心组件规范

> 📖 **完整代码**：参见 `CSSDesignTokens.md`

### 卡片（Card）
- 边框：1.5px，圆角 12px，内边距 32px
- **Linear 特色**：`::before` 伪元素实现 3px 顶部蓝色边框
- 悬停：上浮 -4px + 蓝色阴影 + 顶部边框显现

### Badge（标签）
- 圆角 6px，内边距 6px 12px
- **必须**：渐变背景 + 大写字母 + letter-spacing 0.5px

### 按钮（Button）
- 圆角 8px，内边距 12px 20px
- **必须**：hover 上浮 -1px + active 微缩放 0.98

---

## 🎯 等级颜色映射（平权设计）

### 统一色系设计理念
**核心思想**：体现肤色平权的包容性价值观，所有等级使用统一色系，通过功能数量而非颜色强度来区分等级。

**色彩策略**：
- 🔵 **主色蓝色**：用于所有交互、选中状态、悬停效果
- 🟣 **辅助紫色**：在蓝色单调时用于强化和强调（如已解锁 Badge、重要提示等）

**设计原则**：
- ✅ **平等对待**：所有等级视觉权重相同
- ✅ **功能区分**：通过功能列表长度体现价值差异
- ✅ **双色协作**：蓝色为主、紫色点缀，避免单调
- ✅ **包容性**：避免颜色层级暗示社会等级

> 📖 **完整代码**：参见 `CSSDesignTokens.md`

---

## 💡 AI 助手使用指南

### 生成 UI 组件步骤
1. **确认场景**：按钮/卡片/输入框/模态框？
2. **应用规范**：CSS 变量 + 8pt Grid + 微交互
3. **检查禁止项**：无玻璃拟态、无过度圆角、无纯黑色
4. **添加 Linear 特色**：顶部蓝色边框 + 渐变 Badge + 蓝色阴影

### 示例提示词
```
请创建会员解锁页面（平权设计）：
1. 遵守 UIAgentRules.md 的 CSS 变量
2. 卡片含 3px 顶部蓝色边框（Linear 特色）
3. 悬停上浮 -4px + 点击微缩放 0.98（Apple 风格）
4. 统一蓝色，通过功能数量区分等级
5. 已解锁用紫色 Badge 标识
```

### 代码生成 Checklist
- [ ] 使用 CSS 变量（不硬编码颜色）
- [ ] 间距是 4 的倍数（8pt Grid）
- [ ] 卡片边框 1.5px、圆角 12px
- [ ] 所有交互元素有 hover/active 状态
- [ ] 动画 ≤ 300ms + 弹性缓动
- [ ] 支持键盘导航 + `prefers-reduced-motion`

---

## 📐 标准尺寸速查

| 元素 | 尺寸 | CSS 变量 |
|------|------|----------|
| 卡片内边距 | 32px | `var(--space-8)` |
| 卡片边框 | 1.5px | - |
| 卡片圆角 | 12px | `var(--radius-lg)` |
| 按钮圆角 | 8px | `var(--radius-md)` |
| 按钮内边距 | 12px 20px | - |
| 卡片悬停位移 | -4px | - |
| 按钮悬停位移 | -1px | - |
| 顶部边框高度 | 3px | - |
