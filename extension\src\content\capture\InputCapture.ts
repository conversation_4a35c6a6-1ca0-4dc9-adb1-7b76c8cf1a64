import { BaseCapture } from './BaseCapture';
import { BaseAIAdapter } from '../core/BaseAIAdapter';
import { DOMUtils } from '../utils/DOMUtils';
import { EchoSyncEventEnum } from '../types/DOMEnum';
import { InputModel } from '../model/InputModel';
// import { AskCapture } from './AskCapture';
import SelectorManager from '../configs/SelectorManager';

/**
 * 输入框捕捉类
 * 使用简单的构造函数依赖注入
 */
export class InputCapture extends BaseCapture {

  // 输入元素
  private inputElement: HTMLElement | null = null;
  private lastInputValue: string = '';
  private adapter: BaseAIAdapter | null = null;
  // private askCapture: AskCapture;

  // 保存事件处理器引用，确保正确移除监听器
  private keydownHandler: (e: KeyboardEvent) => void;

  constructor(
    adapter: BaseAIAdapter,
  ) {
    super();
    this.adapter = adapter;
    this.initCaptureElement();
    this.initEventListener();
  }


  /**
   * 获得input元素
   */
  protected initCaptureElement() {
    const selectors = SelectorManager.getSelector()?.inputField
    // console.log('【EchoSync】Searching for input element with selectors:', selectors)
    
    this.inputElement = DOMUtils.findElement(selectors)
    
    if (!this.inputElement) {
        console.error('【InputCapture】Input element not found! selectors:', selectors)
    } else {
        console.info('【InputCapturec】Input element found:',this.inputElement)
    }
  }

  /**
   * 初始化对ui事件的监听
   */
  protected initEventListener(): void {
    this.setupFocusListener()

    // // 监听键盘事件（Enter键）
    // document.addEventListener('keydown', (e) => {
    //   if(e.key){
    //     console.log("【EchoSync】keydown:", e.key)
    //   }
    //   if (e.key === 'Enter' && !e.shiftKey && this.inputElement &&
    //     document.activeElement === this.inputElement) {

    //     // // 检查是否可以发送
    //     // if (this.askCapture.isButtonEnable()) {
    //     //   console.log('【EchoSync】Send triggered by Enter key');
    //     //   this.askCapture.handleSendEvent();
    //     // }
    //   }
    // });
  }

  /**
   * 设置输入框聚焦监听
   */
  private setupFocusListener(): void {
    const inputField = this.inputElement
    if (!inputField) {
      console.warn('【EchoSync】Input field not found, cannot set up focus listener')
      return
    }

    inputField.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      // 设置输入监听
      this.setupInputMonitoring(inputField)
      // 触发输入框聚焦事件
      document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.INPUT_FOCUSED, {
        detail: { element: target }
      }))
    })
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(inputElement: HTMLElement): void {
    if (!inputElement) {
      console.warn('【EchoSync】Cannot setup input monitoring: inputElement is null')
      return
    }

    // 额外检查 this.inputElement，确保不为 null
    if (!this.inputElement) {
      console.warn('【EchoSync】Cannot setup input monitoring: this.inputElement is null')
      return
    }

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      // console.info('【EchoSync】Input changed, value:', currentValue)
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        InputModel.getInstance().setCurrPrompt(currentValue)

        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent(EchoSyncEventEnum.INPUT_CHANGED, {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)
  }

  /**
   * 获取当前输入内容
   */
  public getCurrentInput(): string {
    if (!this.inputElement) return ''
    
    // 获取输入框的值
    const value = (this.inputElement as HTMLInputElement).value || 
                 (this.inputElement as HTMLTextAreaElement).value || 
                 this.inputElement.textContent || ''
                 
    return value.trim()
  }


  /**
   * 获取最后输入值
   */
  public getLastInputValue(): string {
    return this.lastInputValue
  }

  public getInputElement(): HTMLElement | null {
    if (!this.inputElement) {
     this.inputElement = DOMUtils.findElement(SelectorManager.getSelector()?.inputField)
    }
    return this.inputElement
  }


  /**
   * 销毁
   */
  destroy(): void {
    // 清理AskAnswerCapture
    // if (this.askCapture) {
    //   this.askCapture.destroy();
    // }

    this.inputElement = null
    this.lastInputValue = ''
  }
}