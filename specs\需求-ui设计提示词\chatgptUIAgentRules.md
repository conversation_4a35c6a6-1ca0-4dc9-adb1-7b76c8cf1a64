# ============================================================
# 🧠 AI Design & UI Agent Rules (.aicfg)
# ============================================================
# Purpose:
#   为 Copilot / Cursor / ChatGPT / Windsurf 等 AI 编程工具
#   提供一致的高级感设计、现代化交互与审美规范指导。
# Author:
#   [Your Name or Team]
# ============================================================

meta:
  version: 1.0
  scope: web-ui
  intent: "Ensure all AI-generated UI and frontend code follow consistent, modern, minimalist, high-end design principles."

rules:

  # ------------------------------------------------------------
  # 🎨 1. Design Philosophy
  # ------------------------------------------------------------
  - id: design-philosophy
    description: "Maintain visual order, consistency, and restraint in all UI generation."
    directives:
      - "Prioritize clarity, alignment, and hierarchy over decoration."
      - "Designs must feel calm, structured, and intentional."
      - "Avoid excessive effects, gradients, and random color choices."
      - "Whitespace and rhythm are key to visual comfort."

  # ------------------------------------------------------------
  # 📐 2. Layout & Grid
  # ------------------------------------------------------------
  - id: layout-grid
    description: "Use a consistent 8pt grid system and balanced layouts."
    directives:
      - "All paddings, margins, and gaps must be multiples of 8 (4/8/16/24/32)."
      - "Elements must align horizontally and vertically using flex or grid."
      - "Use max content width ≤ 1280px for large screens."
      - "Maintain visual balance: top-heavy composition, centered alignment."

  # ------------------------------------------------------------
  # 🎨 3. Color System
  # ------------------------------------------------------------
  - id: color-system
    description: "Enforce a minimal, low-saturation color palette."
    directives:
      - "Primary color: neutral or desaturated blue (#2563EB / #3B82F6)."
      - "Accent color only for call-to-action elements."
      - "Avoid high-saturation or neon colors."
      - "Background colors: #F9FAFB (light), #111827 (dark)."
      - "Use shadow-sm for cards and shadow-lg for modals only."

  # ------------------------------------------------------------
  # ✍️ 4. Typography
  # ------------------------------------------------------------
  - id: typography
    description: "Maintain clean and consistent typography."
    directives:
      - "Use system or modern sans-serif fonts: Inter, Roboto, Noto Sans SC."
      - "Only two font weights per view (e.g. Regular + Semibold)."
      - "Font hierarchy: H1 32px, H2 24px, H3 18px, Body 16px, Small 14px."
      - "Line height = 1.5 × font size."
      - "Text block width ≤ 70 characters."

  # ------------------------------------------------------------
  # 🧩 5. Components
  # ------------------------------------------------------------
  - id: components
    description: "Component design must follow consistent proportions and states."
    directives:
      - "Buttons: 8px radius, uniform height, clear hover and active states."
      - "Inputs: internal padding ≥ 12px, focus ring with subtle glow."
      - "Cards: 16px padding, subtle shadow-sm, rounded corners."
      - "Modals: centered, use backdrop blur or semi-transparent overlay."
      - "Lists/Tables: hover highlight, consistent row spacing."
      - "Navigation: fixed height (64px), shadow on scroll."

  # ------------------------------------------------------------
  # ⚙️ 6. Motion & Interaction
  # ------------------------------------------------------------
  - id: motion
    description: "Animations should be subtle, physical, and consistent."
    directives:
      - "Duration between 150ms–300ms."
      - "Easing: ease-in-out or cubic-bezier(0.4, 0, 0.2, 1)."
      - "Avoid looping or flashing animations."
      - "Use fade/scale transitions for enter/exit animations."

  # ------------------------------------------------------------
  # 💻 7. Technology Constraints
  # ------------------------------------------------------------
  - id: tech-stack
    description: "Limit CSS and component library diversity."
    directives:
      - "CSS: Tailwind CSS only."
      - "Components: Prefer shadcn/ui or custom unified library."
      - "Animations: Use framer-motion."
      - "Icons: Use lucide-react, 1.5px stroke width."
      - "Dark mode via CSS variables or data-theme attributes."
      - "Centralize theme in designTokens.ts."

  # ------------------------------------------------------------
  # 🤖 8. AI Generation Rules
  # ------------------------------------------------------------
  - id: ai-behavior
    description: "AI must follow the design principles above in all code generations."
    directives:
      - "When generating React/Vue/Svelte components, follow the 8pt grid and typography rules."
      - "Avoid random colors, oversized shadows, or inconsistent border-radius."
      - "Ensure accessibility (aria-label, keyboard focus, proper contrast)."
      - "Use semantic HTML and meaningful component names."
      - "Document any non-trivial animation or design logic with comments."

  # ------------------------------------------------------------
  # 🧱 9. Design Tokens Example (AI Reference)
  # ------------------------------------------------------------
  - id: design-tokens
    description: "Provide a default token set for consistency."
    reference:
      code: |
        export const designTokens = {
          radius: { sm: '4px', md: '8px', lg: '16px' },
          colors: {
            primary: '#2563EB',
            secondary: '#64748B',
            background: '#F9FAFB',
            text: '#111827',
          },
          shadows: {
            sm: '0 1px 3px rgba(0,0,0,0.1)',
            lg: '0 4px 20px rgba(0,0,0,0.15)',
          },
          animation: {
            duration: '200ms',
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          },
        };

  # ------------------------------------------------------------
  # 🧠 10. Inspiration References
  # ------------------------------------------------------------
  - id: inspiration
    description: "Keep aesthetic references for AI learning context."
    references:
      - "Refactoring UI by Adam Wathan & Steve Schoger"
      - "Google Material 3 Design"
      - "Apple Human Interface Guidelines"
      - "shadcn/ui"
      - "Awwwards / Mobbin design references"

---

guidance:
  - "When prompting the AI (Copilot, Cursor, etc.), include this line:"
  - > “遵守项目的《AI Design & UI Agent Rules》，生成风格统一、极简、高级感的UI组件与代码。”
  - "AI must prefer clean, predictable, and harmonious output rather than flashy or experimental styles."
