import { ContentScriptManager } from '@/content/core/ContentScriptManager'
import { DOMUtils } from '@/content/utils/DOMUtils'
import { permissionManager } from '@/content/core/PermissionManager'

console.log('【EchoSync】index EchoSync Content Script loaded on:', window.location.href)
console.log('【EchoSync】index Current hostname:', window.location.hostname)
console.log('【EchoSync】index User agent:', navigator.userAgent)


// 全局管理器实例
let globalManager: ContentScriptManager | null = null

/**
 * 强制清理所有气泡相关元素
 */
function forceCleanupAllBubbles(): void {
  console.log('【index】Force cleaning up all bubble elements before reinitializing...')

  const bubbleSelectors = [
    '#echosync-floating-bubble',
    '[id*="echosync-floating-bubble"]',
    '[class*="floating-bubble"]',
    '[class*="bubble-content"]'
  ]

  let cleanedCount = 0
  bubbleSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    elements.forEach(element => {
      if (element.parentNode) {
        element.remove()
        cleanedCount++
      }
    })
  })

  if (cleanedCount > 0) {
    console.log(`【index】Cleaned up ${cleanedCount} bubble elements before reinitializing`)
  }
}

/**
 * 初始化或重新初始化管理器
 */
async function initializeManager(): Promise<void> {
  // 销毁现有管理器前，强制清理气泡元素
  if (globalManager) {
    // 强制清理所有气泡元素
    forceCleanupAllBubbles()

    globalManager.destroy()
    globalManager = null
  }

  // 等待一帧确保清理完成
  await new Promise(resolve => requestAnimationFrame(resolve))

  // 等待页面完全加载
  await DOMUtils.waitForPageLoad()

  // 创建新的管理器
  globalManager = new ContentScriptManager()

  // 初始化管理器（等待DOM元素可用）
  await globalManager.initialize()
}

/**
 * 初始化权限和管理器
 */
async function initializeApp(): Promise<void> {
  console.log('【EchoSync】开始初始化应用...')

  // 1. 首先尝试请求剪贴板权限
  console.log('【EchoSync】步骤 1/3: 请求剪贴板权限')
  const permissionGranted = await permissionManager.checkAndRequestPermission()

  if (permissionGranted) {
    console.log('【EchoSync】✅ 剪贴板权限已授予，继续初始化')
  } else {
    console.warn('【EchoSync】⚠️ 剪贴板权限未授予，将在用户交互时重试')
    // 设置自动重试策略：用户首次点击页面时自动请求
    permissionManager.setupAutoRetryOnUserInteraction()
  }

  // 2. 初始化内容脚本管理器（不阻塞）
  console.log('【EchoSync】步骤 2/3: 初始化内容脚本管理器')
  await initializeManager()

  console.log('【EchoSync】步骤 3/3: 应用初始化完成 ✅')
}

// 初始化应用（包含权限请求）
initializeApp().catch(error => {
  console.error('【EchoSync】Failed to initialize app:', error)
})

// 处理SPA路由变化
let lastUrl = location.href
const urlObserver = new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    console.log('【index】URL changed, reinitializing...', url)

    // 使用waitForPageLoad等待页面渲染完成后重新初始化
    initializeManager().catch(error => {
      console.error('【index】Failed to reinitialize manager after URL change:', error)
    })
  }
})

// 
urlObserver.observe(document, { subtree: true, childList: true })

// 整页刷新或关闭标签页时彻底清理：
window.addEventListener('beforeunload', () => {
  if (globalManager) {
    globalManager.destroy()
    globalManager = null
  }
  urlObserver.disconnect()
})
