import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { GrokAnswerService } from "./GrokAnswerService";
import { GrokPageService } from "./GrokPageService";

/**
 * Grok 答案控制器
 * 继承 BaseAnswerController，提供 Grok 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 GrokAnswerService 和 GrokPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 */
export class GrokAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: GrokAnswerService;
        pageService: GrokPageService;
    } {
        console.info('[GrokAnswerController] 创建 Grok 服务实例');
        
        return {
            answerService: new GrokAnswerService(),
            pageService: new GrokPageService()
        };
    }
}
