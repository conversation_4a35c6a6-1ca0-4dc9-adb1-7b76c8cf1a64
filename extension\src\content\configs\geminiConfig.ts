import { SelectorConfig } from "./SelectorManager";

/**
 * Gemini 适配器的选择器配置
 * 整合了所有 Gemini 平台特有的选择器
 * 
 * Gemini 使用 Angular Material 和 Web Components
 * 页面结构基于自定义元素如 <chat-app>, <model-response> 等
 */
export const geminiSelector: SelectorConfig = {
  inputField: [
    'rich-textarea .ql-editor[contenteditable="true"]',  // Quill 编辑器
    '.textarea[contenteditable="true"]',                  // 通用可编辑文本框
    'rich-textarea .textarea',                            // rich-textarea 内的文本框
  ],
  
  sendButton: [
    'button[aria-label="发送"]',                          // 中文发送按钮
    'button[aria-label="Send"]',                          // 英文发送按钮  
    'button:has(mat-icon[fonticon="send"])',             // 包含发送图标的按钮
    '.send-button',                                       // 发送按钮类名
    'button[jslog*="173899"]',                           // 通过 jslog 属性识别
  ],
  
  headerContent: [],

  // Gemini 特有的选择器
  chatContentList: [
    'chat-window-content #chat-history',                 // 聊天历史容器
    'infinite-scroller',                                  // 无限滚动容器
    '.chat-history-scroll-container',                    // 滚动容器
  ],

  promptItem: [
    'user-query',                                         // 用户查询自定义元素
    '.conversation-container:has(user-query)',           // 包含用户查询的对话容器
  ],

  promptContent: [
    'user-query-content .query-text',                    // 查询文本
    '.user-query-bubble-with-background .query-text',   // 带背景的查询气泡
    'user-query .query-text p',                          // 查询文本段落
  ],

  promptAction: [
    'user-query button',                                  // 用户查询区域的按钮
    'user-query .action-button',                         // 操作按钮
  ],

  answerItem: [
    'model-response',                                     // AI 回答自定义元素
    'response-container',                                 // 回答容器组件
    '.conversation-container:has(model-response)',       // 包含 AI 回答的对话容器
  ],

  answerCompletion: [
    'model-response:has(message-actions)',               // 带有操作按钮的回答(表示完成)
    'response-container.complete',                        // 标记为完成的回答容器
    'message-actions',                                    // 消息操作区域出现表示完成
  ],

  copyButton: [
    'copy-button > button[aria-label="复制"]',           // 中文复制按钮
    'copy-button > button[aria-label="Copy"]',           // 英文复制按钮
    'button[mattooltip="复制回答"]',                     // 通过 tooltip 识别
    'button[mattooltip="Copy response"]',                // 英文 tooltip
    'copy-button button',                                 // 复制按钮组件内的按钮
  ],

  markdown: [
    'message-content .markdown',                          // 消息内容中的 markdown
    '.markdown.markdown-main-panel',                     // 主 markdown 面板
    'message-content .markdown.stronger',                // 增强的 markdown 样式
  ]
};
