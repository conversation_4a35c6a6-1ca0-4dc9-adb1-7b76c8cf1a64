# GitHub Copilot 项目指令 · 项目特定知识

## 核心原则
1. ✅ 使用中文
2. ❌ 不自动生成文档
3. ❌ 不执行编译命令（开发模式自动编译）
4. ✅ 提醒重新加载扩展
5. ✅ 遵循项目架构

## 关键技术

| 技术 | 实现 |
|------|------|
| File System Access API | DirectoryHandle 存储在 IndexedDB，元信息在 SettingsStorage |
| Kimi.ai 集成 | MutationObserver 监听 DOM → 解析答案 → 导出 Obsidian |
| 消息传递 | Content → Proxy → MessagingService → Background → Chrome API |

## 命名核心规则

| 类型 | 说明 |
|------|------|
| Manager | 单例管理类（位于 `core/` 或 `utils/`） |
| Service | 非单例业务类（位于 `adapters/` 或 `service/`） |
| Controller | 流程控制类（位于 `adapters/`） |
| Inject | 注入组件（位于 `inject/`） |

完整规范见 `docs/0_Base/代码规范和命名约定.md`。

