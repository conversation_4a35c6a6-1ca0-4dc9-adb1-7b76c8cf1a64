import { AnswerModel } from '../model/AnswerModel';
import { ExportError, QAPair } from '../types/ExportTypes';
import { fileSystemService } from './FileSystemService';

/**
 * Markdown 导出错误类
 */
export class MarkdownExportError extends Error {
    constructor(
        public code: ExportError,
        message: string
    ) {
        super(message);
        this.name = 'MarkdownExportError';
    }
}

/**
 * Markdown 导出服务
 * 负责生成标准 Markdown 内容、文件命名、文件保存
 */
export class MarkdownExportService {
    private static instance: MarkdownExportService;
    private existingFilenames: Set<string> = new Set();

    private constructor() {}

    /**
     * 获取单例实例
     */
    public static getInstance(): MarkdownExportService {
        if (!MarkdownExportService.instance) {
            MarkdownExportService.instance = new MarkdownExportService();
        }
        return MarkdownExportService.instance;
    }

    /**
     * 导出为标准 Markdown
     */
    public async exportToMarkdown(answerIndex: number): Promise<void> {
        try {
            console.log('[MarkdownExportService] 开始导出', { answerIndex });

            // 1. 获取问答对
            const qaPair = this.getQAPair(answerIndex);
            if (!qaPair) {
                throw new MarkdownExportError(
                    ExportError.INVALID_INDEX,
                    '无法获取问答内容'
                );
            }

            // 2. 生成 Markdown 内容（不包含 frontmatter）
            const markdown = this.generateMarkdown(qaPair);

            // 3. 生成文件名（使用问题内容）
            const filename = await this.generateFilename(qaPair.prompt);

            // 4. 保存文件（会自动处理目录选择）
            await this.saveFile(filename, markdown);

            console.log('[MarkdownExportService] 导出成功', { filename });

            // 5. 显示成功提示
            this.showToast('Markdown 下载成功', 'success');

        } catch (error) {
            // 用户取消选择目录
            if (error instanceof Error && error.message === '用户取消了目录选择') {
                console.log('[MarkdownExportService] 用户取消了目录选择');
                return; // 不抛出错误，静默返回
            }

            if (error instanceof MarkdownExportError) {
                this.showToast(error.message, 'error');
                throw error;
            }
            
            console.error('[MarkdownExportService] 导出失败', error);
            this.showToast('Markdown 下载失败', 'error');
            throw new MarkdownExportError(
                ExportError.FILE_SAVE_FAILED,
                error instanceof Error ? error.message : '未知错误'
            );
        }
    }

    /**
     * 获取问答对
     */
    private getQAPair(answerIndex: number): QAPair | null {
        return AnswerModel.getInstance().getQAPair(answerIndex);
    }

    /**
     * 生成标准 Markdown 内容
     * 不包含 frontmatter，只有问题和答案
     */
    private generateMarkdown(qaPair: QAPair): string {
        return `## 问题

${qaPair.prompt}

## 答案

${qaPair.answer}
`;
    }

    /**
     * 生成文件名
     * 使用问题内容，避免过长
     */
    private async generateFilename(question: string): Promise<string> {
        // 1. 过滤特殊字符
        let filename = question.replace(/[/\\:*?"<>|\r\n]/g, '-');

        // 2. 限制长度（取前 50 个字符）
        if (filename.length > 50) {
            filename = filename.substring(0, 50);
        }

        // 3. 去除首尾空格和横杠
        filename = filename.trim().replace(/^-+|-+$/g, '');

        // 4. 如果文件名为空，使用默认名称
        if (!filename) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
            filename = `markdown-export-${timestamp}`;
        }

        // 5. 检查重复，添加后缀
        let finalFilename = filename + '.md';
        let counter = 1;

        while (this.existingFilenames.has(finalFilename)) {
            finalFilename = `${filename}_${counter}.md`;
            counter++;
        }

        // 6. 记录文件名
        this.existingFilenames.add(finalFilename);

        return finalFilename;
    }

    /**
     * 保存文件
     */
    private async saveFile(filename: string, content: string): Promise<void> {
        try {
            console.log('[MarkdownExportService] 保存文件', { filename });

            // 使用 File System Access API 保存文件（指定平台为 markdown）
            await fileSystemService.writeFile('markdown', filename, content);

            console.log('[MarkdownExportService] 文件保存成功', { filename });

        } catch (error) {
            console.error('[MarkdownExportService] 保存文件失败', error);
            
            if (error instanceof MarkdownExportError) {
                throw error;
            }
            
            throw new MarkdownExportError(
                ExportError.FILE_SAVE_FAILED,
                error instanceof Error ? error.message : '文件保存失败'
            );
        }
    }

    /**
     * 显示 Toast 提示
     */
    private showToast(message: string, type: 'success' | 'warning' | 'error'): void {
        const toast = document.createElement('div');
        toast.className = `markdown-toast markdown-toast-${type}`;
        toast.textContent = message;

        // 简单的样式（内联）
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 24px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            backgroundColor: type === 'success' ? '#10B981' : type === 'error' ? '#EF4444' : '#F59E0B',
            color: '#FFFFFF',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        });

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.opacity = '1';
        }, 10);

        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }, 3000);
    }
}

/**
 * 导出单例实例
 */
export const markdownExportService = MarkdownExportService.getInstance();
