import { BaseAnswerController } from "@/content/core/BaseAnswerController";
import { ChatGPTAnswerService } from "./ChatGPTAnswerService";
import { ChatGPTPageService } from "./ChatGPTPageService";

/**
 * ChatGPT 答案控制器
 * 继承 BaseAnswerController，提供 ChatGPT 平台特定的服务实例
 * 
 * 职责：
 * - 创建并管理 ChatGPTAnswerService 和 ChatGPTPageService 实例
 * - 继承父类的完整答案处理流程和页面状态检测逻辑
 */
export class ChatGPTAnswerController extends BaseAnswerController {
    
    /**
     * 创建平台特定的服务实例（BaseAnswerController 抽象方法实现）
     * @returns 包含 answerService 和 pageService 的对象
     */
    protected createServices(): {
        answerService: ChatGPTAnswerService;
        pageService: ChatGPTPageService;
    } {
        console.info('[ChatGPTAnswerController] 创建 ChatGPT 服务实例');
        
        return {
            answerService: new ChatGPTAnswerService(),
            pageService: new ChatGPTPageService()
        };
    }
}
