# ChatGPT 适配器快速参考

## 文件结构

```
extension/src/content/
├── configs/
│   ├── chatgptConfig.ts          # ChatGPT 选择器配置
│   └── SelectorManager.ts        # 已注册 ChatGPT
├── adapters/
│   └── chatgpt/
│       ├── chatgpt.ts                      # 主适配器
│       ├── ChatGPTAnswerController.ts      # 控制器
│       ├── ChatGPTAnswerService.ts         # 答案服务
│       ├── ChatGPTPageService.ts           # 页面服务
│       └── ChatGPTClipboardService.ts      # 剪贴板服务
└── core/
    └── ContentScriptManager.ts   # 已集成 ChatGPT
```

## 使用方式

适配器已自动集成到项目中，当用户访问 ChatGPT 页面时会自动激活。

### 支持的 URL 模式

- **Home 页**: `https://chatgpt.com/?model=gpt-4` （带查询参数）
- **Chat 页**: `https://chatgpt.com/c/68e3d520-b8d4-8333-aed9-497044744535`

### 自动触发流程

1. 用户打开 ChatGPT 页面
2. `ContentScriptManager` 检测到 ChatGPT 平台
3. 创建 `ChatGPTAdapter` 实例
4. 初始化答案控制器
5. 开始监听 DOM 变化

## 核心功能

### 1. 答案捕获

- 监听 `article[data-turn="assistant"]` 元素
- 检测 `[data-testid="copy-turn-action-button"]` 出现 → 答案完成
- 点击复制按钮获取 Markdown 内容
- 失败时从 `.markdown.prose` 提取并转换

### 2. 暗色主题支持

自动处理以下元素：
- 代码块容器：`.bg-token-sidebar-surface-primary`
- 语言标签：`language-*` class
- 内联代码：`<code>` 标签

### 3. 内容清理

自动移除：
- "Copy code" / "复制代码" 提示
- `[object Object]` 占位符
- SVG 图标标记
- 多余空行

## 测试方法

### 前提条件
1. 编译扩展：`npm run build` 或 `npm run dev`
2. 在浏览器中加载扩展（开发者模式）
3. 打开 ChatGPT 页面

### 测试步骤

#### 测试 1: 答案捕获
```
1. 访问 https://chatgpt.com/
2. 输入问题："请用 Python 写一个快速排序"
3. 等待答案生成完成
4. 查看控制台日志：
   - [ChatGPTAdapter] 初始化
   - [ChatGPTAnswerService] 检测到复制按钮
   - [ChatGPTAnswerService] 答案提取成功
```

#### 测试 2: 暗色主题代码块
```
1. 确保 ChatGPT 使用暗色主题
2. 提问包含代码的问题
3. 检查提取的 Markdown 格式是否正确：
   ```python
   def quicksort(arr):
       ...
   ```
```

#### 测试 3: 剪贴板降级
```
1. 禁用剪贴板权限（浏览器设置）
2. 提问任意问题
3. 验证仍能从 DOM 提取内容
4. 查看日志：[ChatGPTClipboardService] 使用降级方法
```

#### 测试 4: 页面检测
```
1. 访问 Home 页：https://chatgpt.com/
   → 日志显示: isWelcomePage = true
2. 访问 Chat 页：https://chatgpt.com/c/[id]
   → 日志显示: isChatPage = true
```

## 调试技巧

### 查看日志
打开浏览器开发者工具 → Console，筛选关键词：
- `[ChatGPTAdapter]` - 适配器生命周期
- `[ChatGPTAnswerService]` - 答案捕获流程
- `[ChatGPTClipboardService]` - 剪贴板操作
- `[ChatGPTPageService]` - 页面检测

### 常见问题

#### 问题 1: 适配器未激活
**症状**: 控制台没有 `[ChatGPTAdapter]` 日志  
**解决**:
- 检查 URL 是否匹配：`https://chatgpt.com/*`
- 检查 `PlatformDetector` 是否识别 ChatGPT

#### 问题 2: 答案未捕获
**症状**: 答案生成但未触发回调  
**排查**:
1. 检查复制按钮选择器是否正确
2. 验证 MutationObserver 是否启动
3. 查看是否有 DOM 结构变化

#### 问题 3: 内容提取为空
**症状**: `extractCompletedAnswer` 返回空字符串  
**排查**:
1. 检查剪贴板权限
2. 验证 Markdown 选择器是否匹配
3. 查看 Turndown 转换是否成功

## 选择器速查表

| 功能 | 选择器 | 用途 |
|-----|-------|------|
| 输入框 | `#prompt-textarea` | 定位输入区域 |
| 发送按钮 | `[data-testid="send-button"]` | 检测提交动作 |
| 答案容器 | `article[data-turn="assistant"]` | 定位 AI 回答 |
| 复制按钮 | `[data-testid="copy-turn-action-button"]` | 判断完成 + 点击复制 |
| Markdown 内容 | `.markdown.prose` | DOM 提取内容 |

## API 参考

### ChatGPTAdapter

```typescript
const adapter = new ChatGPTAdapter(platform);
await adapter.initialize();  // 初始化
adapter.destroy();           // 清理资源
```

### ChatGPTAnswerService

```typescript
// 继承 BaseAnswerService
processGeneratingAnswer(answerElement, onUpdate, onComplete);
isAnswerComplete(answerElement): Promise<boolean>;
extractCompletedAnswer(answerElement): Promise<string>;
```

### ChatGPTClipboardService

```typescript
// 继承 BaseClipboardService
simulateClickAndGetContent(answerElement, copyButton): Promise<string>;
getFallbackClipboardContent(answerElement): Promise<string>;  // 重写
getClipboardContent(answerElement): Promise<string>;          // 重写
```

### ChatGPTPageService

```typescript
// 继承 BasePageService
getPlatformName(): string;                    // 返回 "ChatGPT"
getUrlPatterns(): { home: RegExp, chat: RegExp };
isWelcomePage(): boolean;
isChatPage(): boolean;
```

## 扩展方法

如需修改功能，推荐修改点：

### 修改选择器
编辑 `chatgptConfig.ts`，更新对应数组：
```typescript
export const chatgptSelector: SelectorConfig = {
  answerItem: [
    'article[data-turn="assistant"]',  // 修改这里
    // 添加更多备选
  ],
  // ...
};
```

### 修改完成判断逻辑
编辑 `ChatGPTAnswerService.ts`，重写 `isAnswerComplete` 方法：
```typescript
async isAnswerComplete(answerElement: Element): Promise<boolean> {
  // 自定义判断逻辑
  return true;
}
```

### 修改内容清理规则
编辑 `ChatGPTClipboardService.ts`，修改 `getClipboardContent` 方法：
```typescript
protected async getClipboardContent(answerElement: Element): Promise<string> {
  let content = await super.getClipboardContent(answerElement);
  // 添加自定义清理规则
  return content;
}
```

## 相关文档

- [设计文档](../../需求-chatgpt平台实现.md)
- [实现总结](../specs/chatgpt-adapter-implementation-summary.md)
- [Kimi 适配器参考](../extension/src/content/adapters/kimi/)

---

**最后更新**: 2025-01-XX  
**维护者**: GitHub Copilot
