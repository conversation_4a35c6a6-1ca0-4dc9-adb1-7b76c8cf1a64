# 7本书的核心原则提炼：
1. 《写给大家看的设计书》 - CRAP四原则
   * Contrast (对比)
   * Repetition (重复)
   * Alignment (对齐)
   * Proximity (亲密性)
2. 《Don't Make Me Think》 - 交互自明性

自解释性、减少认知负担、即时反馈、视觉层级
3. 《Designing Interface Animation》 - 动效设计

有目的的动效、速度与缓动、连贯性、性能优先
4. 《Laws of UX》 - UX定律

Hick's Law、<PERSON>tts's Law、Miller's Law、Jakob's Law、Peak-End Rule
5. 《Refactoring UI》 - 前端实战

从功能出发、建立设计系统、少用边框、留白高级感、层次化灰色
6. 《简约至上》 - 简化四原则

删除、组织、隐藏、转移
7. 《设计中的设计》 - 日式美学

克制、留白即设计、自然与本质


# 书籍列表

《写给大家看的设计书》（The Non-Designer’s Design Book）
→ 最适合非设计出身的开发者，教你排版、对齐、留白、对比。

《Don’t Make Me Think》（别让我思考）– <PERSON>
→ 交互设计的圣经，让页面“自解释”。

《Designing Interface Animation》 – Val Head
→ 系统讲解网页动效设计的逻辑与节奏。

《Laws of UX》（UX定律）
→ 把心理学转化为交互设计原则，非常适合理工思维。

《Refactoring UI》 – Adam Wathan & Steve Schoger
→ 前端开发者提升设计水平的神书（强烈推荐！）


《写给大家看的设计书》 - Robin Williams

掌握CRAP四原则：对比、重复、对齐、亲密性

特别适合零基础快速建立设计思维

《简约至上》 - Giles Colborne

学习简化复杂界面的实用方法

《设计中的设计》 - 原研哉

理解日式美学和设计哲学

