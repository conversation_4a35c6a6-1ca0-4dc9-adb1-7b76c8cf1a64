import React from 'react'
import { PlatformTabs, StatusIndicator } from '../components/button'
import { useSettings } from '../hooks/useSettings'
import { NotionConfigSection } from '../components/NotionConfigSection'

export function SettingsPage() {
  const {
    settings,
    loading,
    error,
    browseFolder
  } = useSettings()

  const platforms = [
    { id: 'notion', label: 'Notion', icon: '/notion.svg' },
    { id: 'obsidian', label: 'Obsidian', icon: '/obsidian.svg' },
    { id: 'markdown', label: 'Markdown', icon: '/markdown.svg' }
  ]
  
  // 当前活跃平台（默认为notion）
  const [activePlatform, setActivePlatform] = React.useState<'obsidian' | 'notion' | 'markdown'>('notion')

  // 处理加载状态
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <div>加载设置中...</div>
      </div>
    )
  }

  // 处理错误状态
  if (error) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
        <div className="card fade-in">
          <div className="card-content">
            <div style={{ color: 'var(--error)', textAlign: 'center' }}>
              加载设置失败: {error}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 如果没有设置数据
  if (!settings) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '200px' }}>
        <div>设置数据不可用</div> 
      </div>
    )
  }

  const getStatusIndicator = (deviceId: string, hasPath: boolean) => {
    // 判断是否是当前设备
    const isCurrent = deviceId === settings.currentDevice.currentDeviceId
    
    if (isCurrent) {
      return <StatusIndicator type="success">当前设备</StatusIndicator>
    } else if (hasPath) {
      return <StatusIndicator type="success">已配置</StatusIndicator>
    } else {
      return <StatusIndicator type="warning">未配置</StatusIndicator>
    }
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-lg)' }}>
      {/* 设备信息 */}
      <div className="card fade-in">
        <div className="card-title">🖥️ 当前设备信息</div>
        <div className="card-content">
          <div style={{ fontWeight: 600 }}>{settings.currentDevice.fullName}</div>
        </div>
      </div>

      {/* 平台导出配置 */}
      <div className="card fade-in">
        <div className="card-title">📂 平台导出配置</div>
        <div className="card-content">
          {/* Platform Tabs */}
          <PlatformTabs
            tabs={platforms}
            activeTab={activePlatform}
            onTabChange={(platform) => {
              setActivePlatform(platform as 'obsidian' | 'notion' | 'markdown')
            }}
          />

          {/* Platform Content */}
          <div className="platform-content active">
            {/* Notion 平台：使用特殊的配置组件 */}
            {activePlatform === 'notion' ? (
              <div>
                <div style={{ 
                  padding: '12px', 
                  background: 'var(--bg-secondary)', 
                  borderRadius: '6px',
                  marginBottom: '16px',
                  fontSize: '14px',
                  color: 'var(--text-secondary)'
                }}>
                  💡 Notion 使用官方 API 导出，配置存储在本地，不跨设备同步
                </div>
                <NotionConfigSection />
              </div>
            ) : (
              /* Obsidian 和 Markdown 平台：使用设备路径配置 */
              Object.values(settings.platforms[activePlatform].devices).map((device) => {
              // 判断设备状态
              const isCurrent = device.id === settings.currentDevice.currentDeviceId
              
              return (
                <div
                  key={device.id}
                  className={`device-item ${isCurrent ? 'current-device' : ''}`}
                >
                  <div className="device-header">
                    <span>{device.icon}</span>
                    <span>{device.name}</span>
                    {getStatusIndicator(device.id, !!device.path)}
                  </div>
                  <div className="path-label">导出路径:</div>
                  <div className="device-path">
                    <input
                      type="text"
                      value={device.path}
                      placeholder={device.path ? '选择导出路径...' : '未配置导出路径'}
                      readOnly
                    />
                    <button
                      className="btn btn-secondary btn-small"
                      onClick={() => browseFolder(activePlatform, device.id)}
                    >
                      {device.path ? '浏览...' : '设置'}
                    </button>
                    {!isCurrent && device.path && (
                      <button style={{ background: 'none', border: 'none', cursor: 'pointer', padding: '4px' }}>
                        ⚙️
                      </button>
                    )}
                  </div>
                </div>
              )
            })
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
